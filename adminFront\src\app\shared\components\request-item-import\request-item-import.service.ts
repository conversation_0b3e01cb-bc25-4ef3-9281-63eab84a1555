import { Injectable } from '@angular/core';
import { NbDialogService } from '@nebular/theme';
import { Observable } from 'rxjs';
import { RequestItemImportComponent, RequestItemImportConfig } from './request-item-import.component';

export interface RequestItemImportServiceConfig {
  buildCaseId?: number;
  houseType?: number[];
  buttonText?: string;
  buttonIcon?: string;
  buttonClass?: string;
  dialogTitle?: string;
}

@Injectable({
  providedIn: 'root'
})
export class RequestItemImportService {

  constructor(private dialogService: NbDialogService) { }

  /**
   * 開啟需求項目匯入對話框
   * @param config 配置選項
   * @returns Observable 當用戶確認匯入時回傳配置，取消時回傳 null
   */
  openImportDialog(config?: RequestItemImportServiceConfig): Observable<RequestItemImportConfig | null> {
    const dialogRef = this.dialogService.open(RequestItemImportComponent, {
      context: {
        buildCaseId: config?.buildCaseId || 0,
        houseType: config?.houseType || []
      },
      closeOnBackdropClick: true,
      closeOnEsc: true,
      hasScroll: true,
      autoFocus: false
    });

    // 監聽項目匯入事件
    const itemsImported$ = dialogRef.componentRef.instance.itemsImported.asObservable();

    // 當有項目匯入時，關閉對話框並回傳結果
    itemsImported$.subscribe((config: RequestItemImportConfig) => {
      dialogRef.close(config);
    });

    return dialogRef.onClose;
  }
}
