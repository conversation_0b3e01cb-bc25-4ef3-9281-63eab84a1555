{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbButtonModule, NbIconModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./request-item-import.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@nebular/theme\";\nfunction RequestItemImportButtonComponent_nb_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"icon\", ctx_r0.icon);\n  }\n}\nexport class RequestItemImportButtonComponent {\n  constructor(importService) {\n    this.importService = importService;\n    this.buildCaseId = 0;\n    this.houseType = [];\n    this.text = '需求項目匯入';\n    this.icon = 'download-outline';\n    this.buttonClass = '';\n    this.disabled = false;\n    this.status = 'primary';\n    this.size = 'medium';\n    this.itemsImported = new EventEmitter();\n    this.beforeOpen = new EventEmitter();\n    this.error = new EventEmitter();\n  }\n  openImportDialog() {\n    this.beforeOpen.emit();\n    const serviceConfig = {\n      buildCaseId: this.buildCaseId,\n      houseType: this.houseType,\n      buttonText: this.text,\n      buttonIcon: this.icon,\n      buttonClass: this.buttonClass,\n      ...this.config\n    };\n    this.importService.openImportDialog(serviceConfig).subscribe({\n      next: result => {\n        if (result) {\n          this.itemsImported.emit(result);\n        }\n      },\n      error: error => {\n        this.error.emit('開啟匯入對話框時發生錯誤');\n      }\n    });\n  }\n  static {\n    this.ɵfac = function RequestItemImportButtonComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequestItemImportButtonComponent)(i0.ɵɵdirectiveInject(i1.RequestItemImportService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequestItemImportButtonComponent,\n      selectors: [[\"app-request-item-import-button\"]],\n      inputs: {\n        buildCaseId: \"buildCaseId\",\n        houseType: \"houseType\",\n        text: \"text\",\n        icon: \"icon\",\n        buttonClass: \"buttonClass\",\n        disabled: \"disabled\",\n        status: \"status\",\n        size: \"size\",\n        config: \"config\"\n      },\n      outputs: {\n        itemsImported: \"itemsImported\",\n        beforeOpen: \"beforeOpen\",\n        error: \"error\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 7,\n      consts: [[\"nbButton\", \"\", 3, \"click\", \"status\", \"size\", \"disabled\"], [\"class\", \"me-1\", 3, \"icon\", 4, \"ngIf\"], [1, \"me-1\", 3, \"icon\"]],\n      template: function RequestItemImportButtonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"button\", 0);\n          i0.ɵɵlistener(\"click\", function RequestItemImportButtonComponent_Template_button_click_0_listener() {\n            return ctx.openImportDialog();\n          });\n          i0.ɵɵtemplate(1, RequestItemImportButtonComponent_nb_icon_1_Template, 1, 1, \"nb-icon\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.buttonClass);\n          i0.ɵɵproperty(\"status\", ctx.status)(\"size\", ctx.size)(\"disabled\", ctx.disabled || !ctx.buildCaseId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.icon);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.text, \" \");\n        }\n      },\n      dependencies: [CommonModule, i2.NgIf, NbButtonModule, i3.NbButtonComponent, NbIconModule, i3.NbIconComponent],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "NbButtonModule", "NbIconModule", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "icon", "RequestItemImportButtonComponent", "constructor", "importService", "buildCaseId", "houseType", "text", "buttonClass", "disabled", "status", "size", "itemsImported", "beforeOpen", "error", "openImportDialog", "emit", "serviceConfig", "buttonText", "buttonIcon", "config", "subscribe", "next", "result", "ɵɵdirectiveInject", "i1", "RequestItemImportService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RequestItemImportButtonComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "RequestItemImportButtonComponent_Template_button_click_0_listener", "ɵɵtemplate", "RequestItemImportButtonComponent_nb_icon_1_Template", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassMap", "ɵɵadvance", "ɵɵtextInterpolate1", "i2", "NgIf", "i3", "NbButtonComponent", "NbIconComponent", "encapsulation"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\request-item-import\\request-item-import-button.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbButtonModule, NbIconModule, NbComponentSize, NbComponentStatus } from '@nebular/theme';\r\nimport { RequestItemImportService, RequestItemImportServiceConfig } from './request-item-import.service';\r\nimport { RequestItemImportConfig } from './request-item-import.component';\r\n\r\n@Component({\r\n  selector: 'app-request-item-import-button',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    NbButtonModule,\r\n    NbIconModule\r\n  ],\r\n  template: `\r\n    <button\r\n      nbButton\r\n      [status]=\"status\"\r\n      [size]=\"size\"\r\n      [disabled]=\"disabled || !buildCaseId\"\r\n      [class]=\"buttonClass\"\r\n      (click)=\"openImportDialog()\">\r\n      <nb-icon *ngIf=\"icon\" [icon]=\"icon\" class=\"me-1\"></nb-icon>\r\n      {{ text }}\r\n    </button>\r\n  `\r\n})\r\nexport class RequestItemImportButtonComponent {\r\n  @Input() buildCaseId: number = 0;\r\n  @Input() houseType: number[] = [];\r\n  @Input() text: string = '需求項目匯入';\r\n  @Input() icon: string = 'download-outline';\r\n  @Input() buttonClass: string = '';\r\n  @Input() disabled: boolean = false;\r\n  @Input() status: NbComponentStatus = 'primary';\r\n  @Input() size: NbComponentSize = 'medium';\r\n  @Input() config?: RequestItemImportServiceConfig;\r\n\r\n  @Output() itemsImported = new EventEmitter<RequestItemImportConfig>();\r\n  @Output() beforeOpen = new EventEmitter<void>();\r\n  @Output() error = new EventEmitter<string>();\r\n\r\n  constructor(private importService: RequestItemImportService) { }\r\n\r\n  openImportDialog() {\r\n    this.beforeOpen.emit();\r\n\r\n    const serviceConfig: RequestItemImportServiceConfig = {\r\n      buildCaseId: this.buildCaseId,\r\n      houseType: this.houseType,\r\n      buttonText: this.text,\r\n      buttonIcon: this.icon,\r\n      buttonClass: this.buttonClass,\r\n      ...this.config\r\n    };\r\n\r\n    this.importService.openImportDialog(serviceConfig)\r\n      .subscribe({\r\n        next: (result) => {\r\n          if (result) {\r\n            this.itemsImported.emit(result);\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.error.emit('開啟匯入對話框時發生錯誤');\r\n        }\r\n      });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;AACtE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,EAAEC,YAAY,QAA4C,gBAAgB;;;;;;;IAoB3FC,EAAA,CAAAC,SAAA,iBAA2D;;;;IAArCD,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAC,IAAA,CAAa;;;AAKzC,OAAM,MAAOC,gCAAgC;EAe3CC,YAAoBC,aAAuC;IAAvC,KAAAA,aAAa,GAAbA,aAAa;IAdxB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,SAAS,GAAa,EAAE;IACxB,KAAAC,IAAI,GAAW,QAAQ;IACvB,KAAAN,IAAI,GAAW,kBAAkB;IACjC,KAAAO,WAAW,GAAW,EAAE;IACxB,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,MAAM,GAAsB,SAAS;IACrC,KAAAC,IAAI,GAAoB,QAAQ;IAG/B,KAAAC,aAAa,GAAG,IAAInB,YAAY,EAA2B;IAC3D,KAAAoB,UAAU,GAAG,IAAIpB,YAAY,EAAQ;IACrC,KAAAqB,KAAK,GAAG,IAAIrB,YAAY,EAAU;EAEmB;EAE/DsB,gBAAgBA,CAAA;IACd,IAAI,CAACF,UAAU,CAACG,IAAI,EAAE;IAEtB,MAAMC,aAAa,GAAmC;MACpDZ,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBY,UAAU,EAAE,IAAI,CAACX,IAAI;MACrBY,UAAU,EAAE,IAAI,CAAClB,IAAI;MACrBO,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B,GAAG,IAAI,CAACY;KACT;IAED,IAAI,CAAChB,aAAa,CAACW,gBAAgB,CAACE,aAAa,CAAC,CAC/CI,SAAS,CAAC;MACTC,IAAI,EAAGC,MAAM,IAAI;QACf,IAAIA,MAAM,EAAE;UACV,IAAI,CAACX,aAAa,CAACI,IAAI,CAACO,MAAM,CAAC;QACjC;MACF,CAAC;MACDT,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,CAACE,IAAI,CAAC,cAAc,CAAC;MACjC;KACD,CAAC;EACN;;;uCAxCWd,gCAAgC,EAAAL,EAAA,CAAA2B,iBAAA,CAAAC,EAAA,CAAAC,wBAAA;IAAA;EAAA;;;YAAhCxB,gCAAgC;MAAAyB,SAAA;MAAAC,MAAA;QAAAvB,WAAA;QAAAC,SAAA;QAAAC,IAAA;QAAAN,IAAA;QAAAO,WAAA;QAAAC,QAAA;QAAAC,MAAA;QAAAC,IAAA;QAAAS,MAAA;MAAA;MAAAS,OAAA;QAAAjB,aAAA;QAAAC,UAAA;QAAAC,KAAA;MAAA;MAAAgB,UAAA;MAAAC,QAAA,GAAAlC,EAAA,CAAAmC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAZzCzC,EAAA,CAAA2C,cAAA,gBAM+B;UAA7B3C,EAAA,CAAA4C,UAAA,mBAAAC,kEAAA;YAAA,OAASH,GAAA,CAAAxB,gBAAA,EAAkB;UAAA,EAAC;UAC5BlB,EAAA,CAAA8C,UAAA,IAAAC,mDAAA,qBAAiD;UACjD/C,EAAA,CAAAgD,MAAA,GACF;UAAAhD,EAAA,CAAAiD,YAAA,EAAS;;;UAJPjD,EAAA,CAAAkD,UAAA,CAAAR,GAAA,CAAA/B,WAAA,CAAqB;UADrBX,EAFA,CAAAE,UAAA,WAAAwC,GAAA,CAAA7B,MAAA,CAAiB,SAAA6B,GAAA,CAAA5B,IAAA,CACJ,aAAA4B,GAAA,CAAA9B,QAAA,KAAA8B,GAAA,CAAAlC,WAAA,CACwB;UAG3BR,EAAA,CAAAmD,SAAA,EAAU;UAAVnD,EAAA,CAAAE,UAAA,SAAAwC,GAAA,CAAAtC,IAAA,CAAU;UACpBJ,EAAA,CAAAmD,SAAA,EACF;UADEnD,EAAA,CAAAoD,kBAAA,MAAAV,GAAA,CAAAhC,IAAA,MACF;;;qBAdAb,YAAY,EAAAwD,EAAA,CAAAC,IAAA,EACZxD,cAAc,EAAAyD,EAAA,CAAAC,iBAAA,EACdzD,YAAY,EAAAwD,EAAA,CAAAE,eAAA;MAAAC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}