{"ast": null, "code": "import { BaseComponent } from './components/base/baseComponent';\nimport { NbMenuItem, NbMenuModule } from '@nebular/theme';\nimport { RouterOutlet } from '@angular/router';\nimport { NgxSpinnerModule } from 'ngx-spinner';\nimport { OneColumnLayoutComponent } from '../@theme/layouts/one-column/one-column.layout';\nimport { LocalStorageService } from '../shared/services/local-storage.service';\nimport { STORAGE_KEY } from '../shared/constant/constant';\nimport { EEvent } from '../shared/services/event.service';\nimport { tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services\";\nimport * as i2 from \"./components/shared.observable\";\nimport * as i3 from \"../shared/helper/allowHelper\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"../shared/services/event.service\";\nimport * as i6 from \"@nebular/theme\";\nimport * as i7 from \"ngx-spinner\";\nexport class PagesComponent extends BaseComponent {\n  constructor(userService, share, allow, router, _eventService) {\n    super(allow);\n    this.userService = userService;\n    this.share = share;\n    this.allow = allow;\n    this.router = router;\n    this._eventService = _eventService;\n    this.allowMenu = [];\n    this.nbMenu = [];\n    this.menu = {};\n    this.share.SharedMenu.subscribe(res => {\n      this.menu = res;\n    });\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"CHANGE_ROLE\" /* EEvent.CHANGE_ROLE */) {\n        this.getMenu();\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.getMenu();\n  }\n  getMenu() {\n    this.userService.apiUserGetMenuPost$Json({\n      body: {}\n    }).subscribe(res => {\n      let _menu;\n      _menu = [];\n      res.Entries?.Menu.filter(x => x.CIsMenu === true).forEach(item => {\n        const _item = new NbMenuItem();\n        _item.title = item.CName;\n        _item.icon = item.CCssStyle;\n        _item.link = item.CPageUrl;\n        if (item.Child.length > 0) {\n          _item.children = [];\n          item.Child.forEach(subItem => {\n            if (subItem.Child.length > 0) {\n              const _subItem = new NbMenuItem();\n              _subItem.title = subItem.CName;\n              _subItem.link = subItem.CPageUrl;\n              _item.children.push(_subItem);\n            }\n            // 取得權限\n            subItem.Child.forEach(threeItem => {\n              this.allowMenu.push({\n                CPageUrl: subItem.CPageUrl,\n                CCompetenceType: threeItem.CCompetenceType\n              });\n            });\n          });\n          if (item.Child.some(x => x.Child.length > 0)) {\n            _menu.push(_item);\n          }\n        }\n      });\n      this.nbMenu = [..._menu];\n      LocalStorageService.AddLocalStorage(STORAGE_KEY.ALLOW, JSON.stringify(this.allowMenu));\n      this.share.SetMenu(res.Entries);\n      this.share.SetMenuTab(this.nbMenu);\n    });\n  }\n  static {\n    this.ɵfac = function PagesComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PagesComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.SharedObservable), i0.ɵɵdirectiveInject(i3.AllowHelper), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PagesComponent,\n      selectors: [[\"ngx-pages\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 2,\n      consts: [[3, \"items\"], [\"bdColor\", \"rgba(0, 0, 0, 0.8)\", \"size\", \"medium\", \"color\", \"#fff\", \"type\", \"square-jelly-box\", 3, \"fullScreen\"], [2, \"color\", \"white\"]],\n      template: function PagesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ngx-one-column-layout\");\n          i0.ɵɵelement(1, \"nb-menu\", 0)(2, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"ngx-spinner\", 1)(4, \"p\", 2);\n          i0.ɵɵtext(5, \" Loading... \");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"items\", ctx.nbMenu);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"fullScreen\", true);\n        }\n      },\n      dependencies: [OneColumnLayoutComponent, NbMenuModule, i6.NbMenuComponent, RouterOutlet, NgxSpinnerModule, i7.NgxSpinnerComponent],\n      styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 102:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nUndefined variable.\\\\n   \\u2577\\\\n32 \\u2502 $gradient-success: linear-gradient(135deg, $success-light 0%, $success-base 100%);\\\\r\\\\n   \\u2502                                            ^^^^^^^^^^^^^^\\\\n   \\u2575\\\\n  src\\\\\\\\app\\\\\\\\@theme\\\\\\\\styles\\\\\\\\_colors.scss 32:44  @import\\\\n  src\\\\\\\\app\\\\\\\\@theme\\\\\\\\styles\\\\\\\\themes.scss 6:9     @import\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\pages.component.scss 1:9    root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[102]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "NbMenuItem", "NbMenuModule", "RouterOutlet", "NgxSpinnerModule", "OneColumnLayoutComponent", "LocalStorageService", "STORAGE_KEY", "EEvent", "tap", "PagesComponent", "constructor", "userService", "share", "allow", "router", "_eventService", "allowMenu", "nbMenu", "menu", "SharedMenu", "subscribe", "res", "receive", "pipe", "action", "getMenu", "ngOnInit", "apiUserGetMenuPost$Json", "body", "_menu", "Entries", "<PERSON><PERSON>", "filter", "x", "CIsMenu", "for<PERSON>ach", "item", "_item", "title", "CName", "icon", "CCssStyle", "link", "CPageUrl", "Child", "length", "children", "subItem", "_subItem", "push", "threeItem", "CCompetenceType", "some", "AddLocalStorage", "ALLOW", "JSON", "stringify", "SetMenu", "SetMenuTab", "i0", "ɵɵdirectiveInject", "i1", "UserService", "i2", "SharedObservable", "i3", "AllowHelper", "i4", "Router", "i5", "EventService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PagesComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "i6", "NbMenuComponent", "i7", "NgxSpinnerComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\pages.component.ts"], "sourcesContent": ["import { BaseComponent } from './components/base/baseComponent';\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { NbMenuItem, NbMenuModule } from '@nebular/theme';\r\nimport { Router, RouterOutlet } from '@angular/router';\r\nimport { NgxSpinnerModule } from 'ngx-spinner';\r\nimport { OneColumnLayoutComponent } from '../@theme/layouts/one-column/one-column.layout';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\nimport { Menu, MenuAllow } from '../shared/model/menu.model';\r\nimport { UserService } from 'src/services/api/services';\r\nimport { AllowHelper } from '../shared/helper/allowHelper';\r\nimport { FunctionDto, GetMenuResponse } from 'src/services/api/models';\r\nimport { LocalStorageService } from '../shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from '../shared/constant/constant';\r\nimport { SharedObservable } from './components/shared.observable';\r\nimport { EEvent, EventService, IEvent } from '../shared/services/event.service';\r\nimport { tap } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'ngx-pages',\r\n  styleUrls: ['pages.component.scss'],\r\n  template: `\r\n    <ngx-one-column-layout>\r\n      <nb-menu [items]=\"nbMenu\"></nb-menu>\r\n      <router-outlet></router-outlet>\r\n    </ngx-one-column-layout>\r\n    <ngx-spinner bdColor=\"rgba(0, 0, 0, 0.8)\" size=\"medium\" color=\"#fff\" type=\"square-jelly-box\" [fullScreen]=\"true\"><p style=\"color: white\"> Loading... </p></ngx-spinner>\r\n  `,\r\n  standalone: true,\r\n  imports: [\r\n    OneColumnLayoutComponent,\r\n    NbMenuModule,\r\n    RouterOutlet,\r\n    NgxSpinnerModule\r\n  ],\r\n})\r\nexport class PagesComponent extends BaseComponent implements OnInit {\r\n\r\n  allowMenu = [] as MenuAllow[];\r\n  nbMenu = [] as NbMenuItem[];\r\n  menu = {} as GetMenuResponse;\r\n\r\n  constructor(\r\n    private userService: UserService,\r\n    private share: SharedObservable,\r\n    protected override allow: AllowHelper,\r\n    private router: Router,\r\n    private _eventService: EventService\r\n  ) {\r\n    super(allow);\r\n    this.share.SharedMenu.subscribe(res => {\r\n      this.menu = res;\r\n    });\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.CHANGE_ROLE) {\r\n          this.getMenu()\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getMenu()\r\n  }\r\n\r\n  getMenu() {\r\n    this.userService.apiUserGetMenuPost$Json({ body: {} }).subscribe(\r\n      res => {\r\n        let _menu: NbMenuItem[];\r\n        _menu = [];\r\n\r\n        res.Entries?.Menu!.filter(x => x.CIsMenu === true).forEach(item => {\r\n\r\n          const _item = new NbMenuItem();\r\n          _item.title = item.CName!;\r\n          _item.icon = item.CCssStyle!;\r\n          _item.link = item.CPageUrl!;\r\n\r\n          if (item.Child!.length > 0) {\r\n            _item.children = [];\r\n            item.Child!.forEach(subItem => {\r\n              if (subItem.Child!.length > 0) {\r\n                const _subItem = new NbMenuItem();\r\n                _subItem.title = subItem.CName!;\r\n                _subItem.link = subItem.CPageUrl!;\r\n                _item.children!.push(_subItem);\r\n              }\r\n\r\n              // 取得權限\r\n              subItem.Child!.forEach(threeItem => {\r\n                this.allowMenu.push({\r\n                  CPageUrl: subItem.CPageUrl!,\r\n                  CCompetenceType: threeItem.CCompetenceType!\r\n                });\r\n              });\r\n            });\r\n\r\n            if (item.Child!.some(x => x.Child!.length! > 0)) {\r\n              _menu.push(_item);\r\n            }\r\n          }\r\n        });\r\n        this.nbMenu = [..._menu]\r\n        LocalStorageService.AddLocalStorage(STORAGE_KEY.ALLOW, JSON.stringify(this.allowMenu));\r\n        this.share.SetMenu(res.Entries!);\r\n        this.share.SetMenuTab(this.nbMenu);\r\n      },\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iCAAiC;AAE/D,SAASC,UAAU,EAAEC,YAAY,QAAQ,gBAAgB;AACzD,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAASC,wBAAwB,QAAQ,gDAAgD;AAMzF,SAASC,mBAAmB,QAAQ,0CAA0C;AAC9E,SAASC,WAAW,QAAQ,6BAA6B;AAEzD,SAASC,MAAM,QAA8B,kCAAkC;AAC/E,SAASC,GAAG,QAAQ,MAAM;;;;;;;;;AAoB1B,OAAM,MAAOC,cAAe,SAAQV,aAAa;EAM/CW,YACUC,WAAwB,EACxBC,KAAuB,EACZC,KAAkB,EAC7BC,MAAc,EACdC,aAA2B;IAEnC,KAAK,CAACF,KAAK,CAAC;IANJ,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACM,KAAAC,KAAK,GAALA,KAAK;IAChB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IATvB,KAAAC,SAAS,GAAG,EAAiB;IAC7B,KAAAC,MAAM,GAAG,EAAkB;IAC3B,KAAAC,IAAI,GAAG,EAAqB;IAU1B,IAAI,CAACN,KAAK,CAACO,UAAU,CAACC,SAAS,CAACC,GAAG,IAAG;MACpC,IAAI,CAACH,IAAI,GAAGG,GAAG;IACjB,CAAC,CAAC;IACF,IAAI,CAACN,aAAa,CAACO,OAAO,EAAE,CAACC,IAAI,CAC/Bf,GAAG,CAAEa,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACG,MAAM,4CAAwB;QACpC,IAAI,CAACC,OAAO,EAAE;MAChB;IACF,CAAC,CAAC,CACH,CAACL,SAAS,EAAE;EACf;EAESM,QAAQA,CAAA;IACf,IAAI,CAACD,OAAO,EAAE;EAChB;EAEAA,OAAOA,CAAA;IACL,IAAI,CAACd,WAAW,CAACgB,uBAAuB,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC,CAACR,SAAS,CAC9DC,GAAG,IAAG;MACJ,IAAIQ,KAAmB;MACvBA,KAAK,GAAG,EAAE;MAEVR,GAAG,CAACS,OAAO,EAAEC,IAAK,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAK,IAAI,CAAC,CAACC,OAAO,CAACC,IAAI,IAAG;QAEhE,MAAMC,KAAK,GAAG,IAAIrC,UAAU,EAAE;QAC9BqC,KAAK,CAACC,KAAK,GAAGF,IAAI,CAACG,KAAM;QACzBF,KAAK,CAACG,IAAI,GAAGJ,IAAI,CAACK,SAAU;QAC5BJ,KAAK,CAACK,IAAI,GAAGN,IAAI,CAACO,QAAS;QAE3B,IAAIP,IAAI,CAACQ,KAAM,CAACC,MAAM,GAAG,CAAC,EAAE;UAC1BR,KAAK,CAACS,QAAQ,GAAG,EAAE;UACnBV,IAAI,CAACQ,KAAM,CAACT,OAAO,CAACY,OAAO,IAAG;YAC5B,IAAIA,OAAO,CAACH,KAAM,CAACC,MAAM,GAAG,CAAC,EAAE;cAC7B,MAAMG,QAAQ,GAAG,IAAIhD,UAAU,EAAE;cACjCgD,QAAQ,CAACV,KAAK,GAAGS,OAAO,CAACR,KAAM;cAC/BS,QAAQ,CAACN,IAAI,GAAGK,OAAO,CAACJ,QAAS;cACjCN,KAAK,CAACS,QAAS,CAACG,IAAI,CAACD,QAAQ,CAAC;YAChC;YAEA;YACAD,OAAO,CAACH,KAAM,CAACT,OAAO,CAACe,SAAS,IAAG;cACjC,IAAI,CAAClC,SAAS,CAACiC,IAAI,CAAC;gBAClBN,QAAQ,EAAEI,OAAO,CAACJ,QAAS;gBAC3BQ,eAAe,EAAED,SAAS,CAACC;eAC5B,CAAC;YACJ,CAAC,CAAC;UACJ,CAAC,CAAC;UAEF,IAAIf,IAAI,CAACQ,KAAM,CAACQ,IAAI,CAACnB,CAAC,IAAIA,CAAC,CAACW,KAAM,CAACC,MAAO,GAAG,CAAC,CAAC,EAAE;YAC/ChB,KAAK,CAACoB,IAAI,CAACZ,KAAK,CAAC;UACnB;QACF;MACF,CAAC,CAAC;MACF,IAAI,CAACpB,MAAM,GAAG,CAAC,GAAGY,KAAK,CAAC;MACxBxB,mBAAmB,CAACgD,eAAe,CAAC/C,WAAW,CAACgD,KAAK,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACxC,SAAS,CAAC,CAAC;MACtF,IAAI,CAACJ,KAAK,CAAC6C,OAAO,CAACpC,GAAG,CAACS,OAAQ,CAAC;MAChC,IAAI,CAAClB,KAAK,CAAC8C,UAAU,CAAC,IAAI,CAACzC,MAAM,CAAC;IACpC,CAAC,CACF;EACH;;;uCAzEWR,cAAc,EAAAkD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,MAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAS,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAd7D,cAAc;MAAA8D,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAd,EAAA,CAAAe,0BAAA,EAAAf,EAAA,CAAAgB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAdvBtB,EAAA,CAAAwB,cAAA,4BAAuB;UAErBxB,EADA,CAAAyB,SAAA,iBAAoC,oBACL;UACjCzB,EAAA,CAAA0B,YAAA,EAAwB;UACyF1B,EAAjH,CAAAwB,cAAA,qBAAiH,WAAwB;UAACxB,EAAA,CAAA2B,MAAA,mBAAW;UAAI3B,EAAJ,CAAA0B,YAAA,EAAI,EAAc;;;UAH5J1B,EAAA,CAAA4B,SAAA,EAAgB;UAAhB5B,EAAA,CAAA6B,UAAA,UAAAN,GAAA,CAAAjE,MAAA,CAAgB;UAGkE0C,EAAA,CAAA4B,SAAA,GAAmB;UAAnB5B,EAAA,CAAA6B,UAAA,oBAAmB;;;qBAIhHpF,wBAAwB,EACxBH,YAAY,EAAAwF,EAAA,CAAAC,eAAA,EACZxF,YAAY,EACZC,gBAAgB,EAAAwF,EAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}