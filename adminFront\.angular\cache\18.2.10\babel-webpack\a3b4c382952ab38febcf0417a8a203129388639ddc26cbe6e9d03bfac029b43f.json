{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule, NbSelectModule, NbOptionModule, NbInputModule } from '@nebular/theme';\nimport { GetRequirement } from 'src/services/api/models';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/requirement.service\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/enumHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nfunction RequestItemImportComponent_div_7_nb_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r3.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r3.CBuildCaseName, \" \");\n  }\n}\nfunction RequestItemImportComponent_div_7_nb_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r4.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r4.label, \" \");\n  }\n}\nfunction RequestItemImportComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"div\", 25)(3, \"label\", 26);\n    i0.ɵɵtext(4, \"\\u5EFA\\u6848\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nb-select\", 27);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestItemImportComponent_div_7_Template_nb_select_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchRequest.CBuildCaseID, $event) || (ctx_r1.searchRequest.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function RequestItemImportComponent_div_7_Template_nb_select_ngModelChange_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBuildCaseChange());\n    });\n    i0.ɵɵtemplate(6, RequestItemImportComponent_div_7_nb_option_6_Template, 2, 2, \"nb-option\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 25)(8, \"label\", 29);\n    i0.ɵɵtext(9, \"\\u623F\\u5C4B\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nb-select\", 30);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestItemImportComponent_div_7_Template_nb_select_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchRequest.CHouseType, $event) || (ctx_r1.searchRequest.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(11, RequestItemImportComponent_div_7_nb_option_11_Template, 2, 2, \"nb-option\", 28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 24)(13, \"div\", 25)(14, \"label\", 31);\n    i0.ɵɵtext(15, \"\\u5340\\u57DF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"input\", 32);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestItemImportComponent_div_7_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchRequest.CLocation, $event) || (ctx_r1.searchRequest.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 25)(18, \"label\", 33);\n    i0.ɵɵtext(19, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"input\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestItemImportComponent_div_7_Template_input_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchRequest.CRequirement, $event) || (ctx_r1.searchRequest.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 24)(22, \"div\", 35)(23, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_div_7_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resetSearch());\n    });\n    i0.ɵɵelement(24, \"nb-icon\", 37);\n    i0.ɵɵtext(25, \" \\u91CD\\u7F6E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_div_7_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelement(27, \"nb-icon\", 39);\n    i0.ɵɵtext(28, \" \\u641C\\u5C0B \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(29, \"hr\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchRequest.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.buildCaseList);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchRequest.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.houseTypeOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchRequest.CLocation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchRequest.CRequirement);\n  }\n}\nfunction RequestItemImportComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43)(2, \"span\", 44);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 45);\n    i0.ɵɵtext(5, \"\\u8F09\\u5165\\u9700\\u6C42\\u9805\\u76EE\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RequestItemImportComponent_div_25_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"nb-icon\", 54);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u6C92\\u6709\\u53EF\\u532F\\u5165\\u7684\\u9700\\u6C42\\u9805\\u76EE\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RequestItemImportComponent_div_25_div_12_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵelement(1, \"nb-icon\", 69);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const requirement_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u4F4D\\u7F6E: \", requirement_r7.CLocation, \" \");\n  }\n}\nfunction RequestItemImportComponent_div_25_div_12_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵelement(1, \"nb-icon\", 70);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const requirement_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u55AE\\u4F4D: \", requirement_r7.CUnit, \" \");\n  }\n}\nfunction RequestItemImportComponent_div_25_div_12_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵelement(1, \"nb-icon\", 71);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"currency\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const requirement_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u55AE\\u50F9: \", i0.ɵɵpipeBind4(3, 1, requirement_r7.CUnitPrice, \"TWD\", \"symbol-narrow\", \"1.0-0\"), \" \");\n  }\n}\nfunction RequestItemImportComponent_div_25_div_12_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const requirement_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5099\\u8A3B: \", requirement_r7.CRemark, \" \");\n  }\n}\nfunction RequestItemImportComponent_div_25_div_12_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 73);\n    i0.ɵɵtext(1, \" \\u7C21\\u5316 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequestItemImportComponent_div_25_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"nb-card\", 56)(2, \"nb-card-body\", 57)(3, \"div\", 58)(4, \"nb-checkbox\", 59);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestItemImportComponent_div_25_div_12_Template_nb_checkbox_ngModelChange_4_listener($event) {\n      const requirement_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      i0.ɵɵtwoWayBindingSet(requirement_r7.selected, $event) || (requirement_r7.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function RequestItemImportComponent_div_25_div_12_Template_nb_checkbox_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onRequirementItemChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 60)(6, \"div\", 61);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 62);\n    i0.ɵɵtemplate(9, RequestItemImportComponent_div_25_div_12_span_9_Template, 3, 1, \"span\", 63)(10, RequestItemImportComponent_div_25_div_12_span_10_Template, 3, 1, \"span\", 63)(11, RequestItemImportComponent_div_25_div_12_span_11_Template, 4, 6, \"span\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, RequestItemImportComponent_div_25_div_12_div_12_Template, 2, 1, \"div\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 65)(14, \"span\", 66);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, RequestItemImportComponent_div_25_div_12_span_16_Template, 2, 0, \"span\", 67);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const requirement_r7 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", requirement_r7.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(requirement_r7.CRequirement || \"\\u672A\\u547D\\u540D\\u9700\\u6C42\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", requirement_r7.CLocation);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", requirement_r7.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", requirement_r7.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", requirement_r7.CRemark);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", requirement_r7.CIsShow ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", requirement_r7.CIsShow ? \"\\u986F\\u793A\" : \"\\u96B1\\u85CF\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", requirement_r7.CIsSimple);\n  }\n}\nfunction RequestItemImportComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 46)(2, \"h6\", 2);\n    i0.ɵɵtext(3, \"\\u53EF\\u532F\\u5165\\u7684\\u9700\\u6C42\\u9805\\u76EE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 47)(5, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_div_25_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectAll());\n    });\n    i0.ɵɵelement(6, \"nb-icon\", 49);\n    i0.ɵɵtext(7, \" \\u5168\\u9078/\\u5168\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 13);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 50);\n    i0.ɵɵtemplate(11, RequestItemImportComponent_div_25_div_11_Template, 4, 0, \"div\", 51)(12, RequestItemImportComponent_div_25_div_12_Template, 17, 9, \"div\", 52);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate2(\"\\u5DF2\\u9078\\u64C7 \", ctx_r1.getSelectedCount(), \" / \", ctx_r1.getTotalCount(), \" \\u9805\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.requirements.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.requirements);\n  }\n}\nfunction RequestItemImportComponent_div_26_div_14_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u4F4D\\u7F6E: \", item_r8.CLocation, \"\");\n  }\n}\nfunction RequestItemImportComponent_div_26_div_14_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u55AE\\u4F4D: \", item_r8.CUnit, \"\");\n  }\n}\nfunction RequestItemImportComponent_div_26_div_14_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"currency\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u55AE\\u50F9: \", i0.ɵɵpipeBind4(2, 1, item_r8.CUnitPrice, \"TWD\", \"symbol-narrow\", \"1.0-0\"), \"\");\n  }\n}\nfunction RequestItemImportComponent_div_26_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"nb-card\", 56)(2, \"nb-card-body\", 57)(3, \"div\", 58)(4, \"div\", 83)(5, \"span\", 84);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 85)(8, \"div\", 86);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 87);\n    i0.ɵɵtemplate(11, RequestItemImportComponent_div_26_div_14_span_11_Template, 2, 1, \"span\", 63)(12, RequestItemImportComponent_div_26_div_14_span_12_Template, 2, 1, \"span\", 63)(13, RequestItemImportComponent_div_26_div_14_span_13_Template, 3, 6, \"span\", 15);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i_r9 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r8.CRequirement || \"\\u672A\\u547D\\u540D\\u9700\\u6C42\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r8.CLocation);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r8.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r8.CUnitPrice);\n  }\n}\nfunction RequestItemImportComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h6\", 74);\n    i0.ɵɵtext(2, \"\\u532F\\u5165\\u78BA\\u8A8D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 75)(4, \"nb-card\", 76)(5, \"nb-card-body\")(6, \"div\", 58);\n    i0.ɵɵelement(7, \"nb-icon\", 77);\n    i0.ɵɵelementStart(8, \"div\")(9, \"div\", 78);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 79);\n    i0.ɵɵtext(12, \"\\u9019\\u4E9B\\u9805\\u76EE\\u5C07\\u88AB\\u52A0\\u5165\\u5230\\u76EE\\u524D\\u7684\\u5EFA\\u6848\\u4E2D\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(13, \"div\", 80);\n    i0.ɵɵtemplate(14, RequestItemImportComponent_div_26_div_14_Template, 14, 5, \"div\", 81);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\\u5373\\u5C07\\u532F\\u5165 \", ctx_r1.getSelectedCount(), \" \\u500B\\u9700\\u6C42\\u9805\\u76EE\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getSelectedItems());\n  }\n}\nfunction RequestItemImportComponent_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStep());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 89);\n    i0.ɵɵtext(2, \" \\u4E0A\\u4E00\\u6B65 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequestItemImportComponent_button_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_button_33_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(1, \" \\u4E0B\\u4E00\\u6B65 \");\n    i0.ɵɵelement(2, \"nb-icon\", 91);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canProceed());\n  }\n}\nfunction RequestItemImportComponent_button_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_button_34_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.importRequirements());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 93);\n    i0.ɵɵtext(2, \" \\u78BA\\u8A8D\\u532F\\u5165 \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class RequestItemImportComponent {\n  constructor(requirementService, dialogRef, enumHelper, buildCaseService) {\n    this.requirementService = requirementService;\n    this.dialogRef = dialogRef;\n    this.enumHelper = enumHelper;\n    this.buildCaseService = buildCaseService;\n    this.buildCaseId = 0;\n    this.itemsImported = new EventEmitter();\n    this.currentStep = 1;\n    this.requirements = [];\n    this.loading = false;\n    // 搜尋相關屬性\n    this.searchRequest = {};\n    this.buildCaseList = [];\n    this.houseTypeOptions = [];\n  }\n  ngOnInit() {\n    this.initializeSearchForm();\n    this.getBuildCaseList();\n  }\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    this.houseTypeOptions = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.searchRequest.CStatus = 1; // 預設只顯示啟用的項目\n    this.searchRequest.CIsShow = true; // 預設只顯示需要顯示的項目\n    this.searchRequest.CIsSimple = null;\n    this.searchRequest.CRequirement = '';\n    this.searchRequest.CLocation = '';\n    // 預設全選所有房屋類型\n    this.searchRequest.CHouseType = this.houseTypeOptions.map(type => type.value);\n  }\n  // 取得建案列表\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).subscribe({\n      next: res => {\n        if (res.StatusCode === 0 && res.Entries) {\n          this.buildCaseList = res.Entries;\n          // 如果有傳入建案ID，使用傳入的ID；否則使用第一個建案\n          if (this.buildCaseId && this.buildCaseId > 0) {\n            this.searchRequest.CBuildCaseID = this.buildCaseId;\n          } else if (this.buildCaseList.length > 0) {\n            this.searchRequest.CBuildCaseID = this.buildCaseList[0].cID;\n          }\n          // 載入需求項目\n          if (this.searchRequest.CBuildCaseID) {\n            this.loadRequirementsFromAPI();\n          }\n        }\n      },\n      error: error => {\n        console.error('載入建案列表失敗:', error);\n      }\n    });\n  }\n  // 建案切換事件\n  onBuildCaseChange() {\n    this.loadRequirementsFromAPI();\n  }\n  // 搜尋事件\n  onSearch() {\n    this.loadRequirementsFromAPI();\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\n      this.searchRequest.CBuildCaseID = this.buildCaseList[0].cID;\n    }\n    this.loadRequirementsFromAPI();\n  }\n  loadRequirementsFromAPI() {\n    if (!this.searchRequest.CBuildCaseID) {\n      return;\n    }\n    this.loading = true;\n    // 準備API請求參數\n    const getRequirementListArgs = {\n      CBuildCaseID: this.searchRequest.CBuildCaseID,\n      CHouseType: this.searchRequest.CHouseType,\n      CLocation: this.searchRequest.CLocation || null,\n      CRequirement: this.searchRequest.CRequirement || null,\n      CStatus: this.searchRequest.CStatus,\n      CIsShow: this.searchRequest.CIsShow,\n      CIsSimple: this.searchRequest.CIsSimple,\n      PageIndex: 1,\n      PageSize: 100\n    };\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: getRequirementListArgs\n    }).subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.StatusCode === 0 && response.Entries) {\n          this.requirements = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n        } else {\n          this.requirements = [];\n        }\n      },\n      error: error => {\n        this.loading = false;\n        this.requirements = [];\n      }\n    });\n  }\n  onRequirementItemChange() {\n    // 當需求項目選擇變更時的處理\n  }\n  getSelectedItems() {\n    return this.requirements.filter(item => item.selected);\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const progressTexts = {\n      1: '請選擇要匯入的需求項目',\n      2: '確認匯入詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  importRequirements() {\n    const config = {\n      buildCaseId: this.buildCaseId,\n      selectedItems: this.getSelectedItems(),\n      totalItems: this.getSelectedItems().length\n    };\n    this.itemsImported.emit(config);\n    this.close();\n  }\n  close() {\n    this.resetSelections();\n    this.dialogRef.close();\n  }\n  resetSelections() {\n    this.currentStep = 1;\n    this.requirements.forEach(requirement => {\n      requirement.selected = false;\n    });\n  }\n  selectAll() {\n    const allSelected = this.requirements.every(item => item.selected);\n    this.requirements.forEach(item => {\n      item.selected = !allSelected;\n    });\n  }\n  getSelectedCount() {\n    return this.getSelectedItems().length;\n  }\n  getTotalCount() {\n    return this.requirements.length;\n  }\n  static {\n    this.ɵfac = function RequestItemImportComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequestItemImportComponent)(i0.ɵɵdirectiveInject(i1.RequirementService), i0.ɵɵdirectiveInject(i2.NbDialogRef), i0.ɵɵdirectiveInject(i3.EnumHelper), i0.ɵɵdirectiveInject(i4.BuildCaseService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequestItemImportComponent,\n      selectors: [[\"app-request-item-import\"]],\n      inputs: {\n        buildCaseId: \"buildCaseId\"\n      },\n      outputs: {\n        itemsImported: \"itemsImported\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 35,\n      vars: 16,\n      consts: [[1, \"request-item-import-dialog\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [\"nbButton\", \"\", \"ghost\", \"\", \"size\", \"small\", 3, \"click\"], [\"icon\", \"close-outline\"], [\"class\", \"search-section mb-4\", 4, \"ngIf\"], [1, \"progress-indicator\", \"mb-4\"], [1, \"progress-steps\", \"d-flex\", \"justify-content-between\"], [1, \"step\"], [1, \"step-number\"], [1, \"step-label\"], [1, \"step-connector\"], [1, \"progress-text\", \"text-center\", \"mt-2\"], [1, \"text-muted\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"left-actions\"], [\"nbButton\", \"\", \"ghost\", \"\", 3, \"click\", 4, \"ngIf\"], [1, \"right-actions\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"me-2\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"click\", 4, \"ngIf\"], [1, \"search-section\", \"mb-4\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-6\"], [\"for\", \"buildCase\", 1, \"label\", \"mb-2\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"houseType\", 1, \"label\", \"mb-2\"], [\"multiple\", \"\", \"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u623F\\u5C4B\\u985E\\u578B\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"location\", 1, \"label\", \"mb-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"location\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5340\\u57DF\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"requirement\", 1, \"label\", \"mb-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"requirement\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"text-right\"], [1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [\"icon\", \"refresh-outline\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [\"icon\", \"search-outline\"], [1, \"my-3\"], [3, \"value\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"sr-only\"], [1, \"mt-2\"], [1, \"selection-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"selection-actions\"], [\"nbButton\", \"\", \"ghost\", \"\", \"size\", \"small\", 1, \"me-2\", 3, \"click\"], [\"icon\", \"checkmark-square-2-outline\"], [1, \"requirement-list\", 2, \"max-height\", \"400px\", \"overflow-y\", \"auto\"], [\"class\", \"text-center py-4 text-muted\", 4, \"ngIf\"], [\"class\", \"requirement-item mb-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-center\", \"py-4\", \"text-muted\"], [\"icon\", \"inbox-outline\", 1, \"mb-2\", 2, \"font-size\", \"2rem\"], [1, \"requirement-item\", \"mb-2\"], [\"size\", \"small\"], [1, \"py-2\"], [1, \"d-flex\", \"align-items-center\"], [1, \"me-3\", 3, \"ngModelChange\", \"ngModel\"], [1, \"requirement-info\", \"flex-grow-1\"], [1, \"requirement-name\", \"fw-bold\"], [1, \"requirement-details\", \"small\", \"text-muted\"], [\"class\", \"me-3\", 4, \"ngIf\"], [\"class\", \"requirement-remark small text-info mt-1\", 4, \"ngIf\"], [1, \"requirement-status\"], [1, \"badge\", 3, \"ngClass\"], [\"class\", \"badge badge-info ms-1\", 4, \"ngIf\"], [1, \"me-3\"], [\"icon\", \"pin-outline\"], [\"icon\", \"cube-outline\"], [\"icon\", \"pricetags-outline\"], [1, \"requirement-remark\", \"small\", \"text-info\", \"mt-1\"], [1, \"badge\", \"badge-info\", \"ms-1\"], [1, \"mb-3\"], [1, \"import-summary\", \"mb-4\"], [\"status\", \"info\", \"size\", \"small\"], [\"icon\", \"info-outline\", 1, \"me-2\"], [1, \"fw-bold\"], [1, \"small\", \"text-muted\"], [1, \"selected-items-preview\", 2, \"max-height\", \"300px\", \"overflow-y\", \"auto\"], [\"class\", \"selected-item mb-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"selected-item\", \"mb-2\"], [1, \"item-number\", \"me-3\"], [1, \"badge\", \"badge-primary\"], [1, \"item-info\", \"flex-grow-1\"], [1, \"item-name\", \"fw-bold\"], [1, \"item-details\", \"small\", \"text-muted\"], [\"nbButton\", \"\", \"ghost\", \"\", 3, \"click\"], [\"icon\", \"arrow-back-outline\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"click\", \"disabled\"], [\"icon\", \"arrow-forward-outline\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"click\"], [\"icon\", \"download-outline\"]],\n      template: function RequestItemImportComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\", 1)(2, \"h5\", 2);\n          i0.ɵɵtext(3, \"\\u9700\\u6C42\\u9805\\u76EE\\u532F\\u5165\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function RequestItemImportComponent_Template_button_click_4_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵelement(5, \"nb-icon\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"nb-card-body\");\n          i0.ɵɵtemplate(7, RequestItemImportComponent_div_7_Template, 30, 6, \"div\", 5);\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9);\n          i0.ɵɵtext(12, \"1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 10);\n          i0.ɵɵtext(14, \"\\u9078\\u64C7\\u9805\\u76EE\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(15, \"div\", 11);\n          i0.ɵɵelementStart(16, \"div\", 8)(17, \"div\", 9);\n          i0.ɵɵtext(18, \"2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 10);\n          i0.ɵɵtext(20, \"\\u78BA\\u8A8D\\u532F\\u5165\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 12)(22, \"small\", 13);\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(24, RequestItemImportComponent_div_24_Template, 6, 0, \"div\", 14)(25, RequestItemImportComponent_div_25_Template, 13, 4, \"div\", 15)(26, RequestItemImportComponent_div_26_Template, 15, 2, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-card-footer\", 16)(28, \"div\", 17);\n          i0.ɵɵtemplate(29, RequestItemImportComponent_button_29_Template, 3, 0, \"button\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 19)(31, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function RequestItemImportComponent_Template_button_click_31_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(32, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(33, RequestItemImportComponent_button_33_Template, 3, 1, \"button\", 21)(34, RequestItemImportComponent_button_34_Template, 3, 0, \"button\", 22);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"active\", ctx.currentStep >= 1)(\"completed\", ctx.currentStep > 1);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"active\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.currentStep >= 2);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.getProgressText());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1 && !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        }\n      },\n      dependencies: [CommonModule, i5.NgClass, i5.NgForOf, i5.NgIf, i5.CurrencyPipe, FormsModule, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, NbCardModule, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, NbButtonModule, i2.NbButtonComponent, NbIconModule, i2.NbIconComponent, NbCheckboxModule, i2.NbCheckboxComponent, NbSelectModule, i2.NbSelectComponent, i2.NbOptionComponent, NbOptionModule, NbInputModule, i2.NbInputDirective],\n      styles: [\".badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.25em 0.4em;\\n  font-size: 75%;\\n  font-weight: 700;\\n  line-height: 1;\\n  text-align: center;\\n  white-space: nowrap;\\n  vertical-align: baseline;\\n  border-radius: 0.25rem;\\n}\\n.badge.badge-primary[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #007bff;\\n}\\n.badge.badge-success[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #28a745;\\n}\\n.badge.badge-info[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #17a2b8;\\n}\\n.badge.badge-secondary[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #6c757d;\\n}\\n\\n.request-item-import-dialog[_ngcontent-%COMP%] {\\n  min-width: 700px;\\n  max-width: 900px;\\n  min-height: 600px;\\n}\\n\\n.search-section[_ngcontent-%COMP%] {\\n  background-color: var(--nb-color-basic-100);\\n  padding: 1rem;\\n  border-radius: 0.5rem;\\n  border: 1px solid var(--nb-color-basic-300);\\n}\\n.search-section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.search-section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--nb-color-text-basic);\\n  display: block;\\n}\\n.search-section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%], .search-section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.search-section[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  min-width: 80px;\\n}\\n.search-section[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n.search-section[_ngcontent-%COMP%]   hr[_ngcontent-%COMP%] {\\n  border-color: var(--nb-color-basic-400);\\n  margin: 1rem 0;\\n}\\n\\n.progress-indicator[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%] {\\n  text-align: center;\\n  position: relative;\\n  flex: 1;\\n}\\n.progress-indicator[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-color: var(--nb-color-basic-400);\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto 8px;\\n  font-weight: bold;\\n  transition: all 0.3s ease;\\n}\\n.progress-indicator[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--nb-color-text-hint);\\n  transition: color 0.3s ease;\\n}\\n.progress-indicator[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background-color: var(--nb-color-primary-500);\\n}\\n.progress-indicator[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  color: var(--nb-color-text-basic);\\n  font-weight: 500;\\n}\\n.progress-indicator[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.completed[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background-color: var(--nb-color-success-500);\\n}\\n.progress-indicator[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n  height: 2px;\\n  background-color: var(--nb-color-basic-400);\\n  margin-top: 16px;\\n  transition: background-color 0.3s ease;\\n}\\n.progress-indicator[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector.active[_ngcontent-%COMP%] {\\n  background-color: var(--nb-color-primary-500);\\n}\\n\\n.selection-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid var(--nb-color-basic-300);\\n  padding-bottom: 12px;\\n}\\n\\n.requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-name[_ngcontent-%COMP%] {\\n  color: var(--nb-color-text-basic);\\n  margin-bottom: 4px;\\n}\\n.requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n}\\n.requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-details[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-details[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  font-size: 0.875rem;\\n}\\n.requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .requirement-remark[_ngcontent-%COMP%] {\\n  color: var(--nb-color-info-600);\\n  font-style: italic;\\n}\\n.requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-end;\\n  gap: 4px;\\n}\\n.requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:hover   nb-card[_ngcontent-%COMP%] {\\n  border-color: var(--nb-color-primary-300);\\n}\\n\\n.import-summary[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%] {\\n  background-color: var(--nb-color-info-100);\\n  border-color: var(--nb-color-info-300);\\n}\\n\\n.selected-items-preview[_ngcontent-%COMP%]   .selected-item[_ngcontent-%COMP%]   .item-number[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.selected-items-preview[_ngcontent-%COMP%]   .selected-item[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  color: var(--nb-color-text-basic);\\n  margin-bottom: 4px;\\n}\\n.selected-items-preview[_ngcontent-%COMP%]   .selected-item[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%]   .item-details[_ngcontent-%COMP%] {\\n  color: var(--nb-color-text-hint);\\n}\\n\\n.left-actions[_ngcontent-%COMP%], .right-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n@media (max-width: 768px) {\\n  .request-item-import-dialog[_ngcontent-%COMP%] {\\n    min-width: 95vw;\\n    max-width: 95vw;\\n  }\\n  .requirement-details[_ngcontent-%COMP%] {\\n    flex-direction: column !important;\\n  }\\n  .requirement-details[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    margin-bottom: 2px !important;\\n  }\\n  .selection-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n  }\\n  .selection-header[_ngcontent-%COMP%]   .selection-actions[_ngcontent-%COMP%] {\\n    justify-content: space-between;\\n    width: 100%;\\n  }\\n}\\n.dark[_nghost-%COMP%]   .progress-indicator[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .progress-indicator[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background-color: var(--nb-color-basic-600);\\n}\\n.dark[_nghost-%COMP%]   .progress-indicator[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .progress-indicator[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n  background-color: var(--nb-color-basic-600);\\n}\\n.dark[_nghost-%COMP%]   .selection-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .selection-header[_ngcontent-%COMP%] {\\n  border-bottom-color: var(--nb-color-basic-600);\\n}\\n.dark[_nghost-%COMP%]   .import-summary[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .import-summary[_ngcontent-%COMP%]   nb-card[_ngcontent-%COMP%] {\\n  background-color: var(--nb-color-info-200);\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "NbIconModule", "NbCheckboxModule", "NbSelectModule", "NbOptionModule", "NbInputModule", "GetRequirement", "EnumHouseType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r3", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "type_r4", "value", "label", "ɵɵtwoWayListener", "RequestItemImportComponent_div_7_Template_nb_select_ngModelChange_5_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "searchRequest", "CBuildCaseID", "ɵɵresetView", "ɵɵlistener", "onBuildCaseChange", "ɵɵtemplate", "RequestItemImportComponent_div_7_nb_option_6_Template", "RequestItemImportComponent_div_7_Template_nb_select_ngModelChange_10_listener", "CHouseType", "RequestItemImportComponent_div_7_nb_option_11_Template", "RequestItemImportComponent_div_7_Template_input_ngModelChange_16_listener", "CLocation", "RequestItemImportComponent_div_7_Template_input_ngModelChange_20_listener", "CRequirement", "RequestItemImportComponent_div_7_Template_button_click_23_listener", "resetSearch", "ɵɵelement", "RequestItemImportComponent_div_7_Template_button_click_26_listener", "onSearch", "ɵɵtwoWayProperty", "buildCaseList", "houseTypeOptions", "requirement_r7", "CUnit", "ɵɵpipeBind4", "CUnitPrice", "CRemark", "RequestItemImportComponent_div_25_div_12_Template_nb_checkbox_ngModelChange_4_listener", "_r6", "$implicit", "selected", "onRequirementItemChange", "RequestItemImportComponent_div_25_div_12_span_9_Template", "RequestItemImportComponent_div_25_div_12_span_10_Template", "RequestItemImportComponent_div_25_div_12_span_11_Template", "RequestItemImportComponent_div_25_div_12_div_12_Template", "RequestItemImportComponent_div_25_div_12_span_16_Template", "ɵɵtextInterpolate", "CIsShow", "CIsSimple", "RequestItemImportComponent_div_25_Template_button_click_5_listener", "_r5", "selectAll", "RequestItemImportComponent_div_25_div_11_Template", "RequestItemImportComponent_div_25_div_12_Template", "ɵɵtextInterpolate2", "getSelectedCount", "getTotalCount", "requirements", "length", "item_r8", "RequestItemImportComponent_div_26_div_14_span_11_Template", "RequestItemImportComponent_div_26_div_14_span_12_Template", "RequestItemImportComponent_div_26_div_14_span_13_Template", "i_r9", "RequestItemImportComponent_div_26_div_14_Template", "getSelectedItems", "RequestItemImportComponent_button_29_Template_button_click_0_listener", "_r10", "previousStep", "RequestItemImportComponent_button_33_Template_button_click_0_listener", "_r11", "nextStep", "canProceed", "RequestItemImportComponent_button_34_Template_button_click_0_listener", "_r12", "importRequirements", "RequestItemImportComponent", "constructor", "requirementService", "dialogRef", "enum<PERSON>elper", "buildCaseService", "buildCaseId", "itemsImported", "currentStep", "loading", "ngOnInit", "initializeSearchForm", "getBuildCaseList", "getEnumOptions", "CStatus", "map", "type", "apiBuildCaseGetUserBuildCasePost$Json", "body", "subscribe", "next", "res", "StatusCode", "Entries", "loadRequirementsFromAPI", "error", "console", "getRequirementListArgs", "PageIndex", "PageSize", "apiRequirementGetListPost$Json", "response", "item", "filter", "getProgressText", "progressTexts", "config", "selectedItems", "totalItems", "emit", "close", "resetSelections", "for<PERSON>ach", "requirement", "allSelected", "every", "ɵɵdirectiveInject", "i1", "RequirementService", "i2", "NbDialogRef", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i4", "BuildCaseService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RequestItemImportComponent_Template", "rf", "ctx", "RequestItemImportComponent_Template_button_click_4_listener", "RequestItemImportComponent_div_7_Template", "RequestItemImportComponent_div_24_Template", "RequestItemImportComponent_div_25_Template", "RequestItemImportComponent_div_26_Template", "RequestItemImportComponent_button_29_Template", "RequestItemImportComponent_Template_button_click_31_listener", "RequestItemImportComponent_button_33_Template", "RequestItemImportComponent_button_34_Template", "ɵɵclassProp", "i5", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "C<PERSON><PERSON>cyPipe", "i6", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbButtonComponent", "NbIconComponent", "NbCheckboxComponent", "NbSelectComponent", "NbOptionComponent", "NbInputDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\request-item-import\\request-item-import.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\request-item-import\\request-item-import.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport {\r\n  NbCardModule,\r\n  NbButtonModule,\r\n  NbIconModule,\r\n  NbCheckboxModule,\r\n  NbSelectModule,\r\n  NbOptionModule,\r\n  NbInputModule,\r\n  NbDialogRef\r\n} from '@nebular/theme';\r\nimport { RequirementService } from 'src/services/api/services/requirement.service';\r\nimport { BuildCaseService } from 'src/services/api/services';\r\nimport { GetListRequirementRequest, GetRequirement, GetRequirementListResponseBase, BuildCaseGetListReponse } from 'src/services/api/models';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\n\r\nexport interface ExtendedRequirementItem extends GetRequirement {\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface RequestItemImportConfig {\r\n  buildCaseId: number;\r\n  selectedItems: ExtendedRequirementItem[];\r\n  totalItems: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-request-item-import',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbIconModule,\r\n    NbCheckboxModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NbInputModule\r\n  ],\r\n  templateUrl: './request-item-import.component.html',\r\n  styleUrls: ['./request-item-import.component.scss']\r\n})\r\nexport class RequestItemImportComponent implements OnInit {\r\n  @Input() buildCaseId: number = 0;\r\n  @Output() itemsImported = new EventEmitter<RequestItemImportConfig>();\r\n\r\n  currentStep: number = 1;\r\n  requirements: ExtendedRequirementItem[] = [];\r\n  loading: boolean = false;\r\n  \r\n  // 搜尋相關屬性\r\n  searchRequest: GetListRequirementRequest & { CIsShow?: boolean | null; CIsSimple?: boolean | null } = {};\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  houseTypeOptions: any[] = [];\r\n\r\n  constructor(\r\n    private requirementService: RequirementService,\r\n    private dialogRef: NbDialogRef<RequestItemImportComponent>,\r\n    private enumHelper: EnumHelper,\r\n    private buildCaseService: BuildCaseService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.initializeSearchForm();\r\n    this.getBuildCaseList();\r\n  }\r\n\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    this.houseTypeOptions = this.enumHelper.getEnumOptions(EnumHouseType);\r\n    this.searchRequest.CStatus = 1; // 預設只顯示啟用的項目\r\n    this.searchRequest.CIsShow = true; // 預設只顯示需要顯示的項目\r\n    this.searchRequest.CIsSimple = null;\r\n    this.searchRequest.CRequirement = '';\r\n    this.searchRequest.CLocation = '';\r\n    // 預設全選所有房屋類型\r\n    this.searchRequest.CHouseType = this.houseTypeOptions.map(type => type.value);\r\n  }\r\n\r\n  // 取得建案列表\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .subscribe({\r\n        next: (res) => {\r\n          if (res.StatusCode === 0 && res.Entries) {\r\n            this.buildCaseList = res.Entries;\r\n            // 如果有傳入建案ID，使用傳入的ID；否則使用第一個建案\r\n            if (this.buildCaseId && this.buildCaseId > 0) {\r\n              this.searchRequest.CBuildCaseID = this.buildCaseId;\r\n            } else if (this.buildCaseList.length > 0) {\r\n              this.searchRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n            }\r\n            // 載入需求項目\r\n            if (this.searchRequest.CBuildCaseID) {\r\n              this.loadRequirementsFromAPI();\r\n            }\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('載入建案列表失敗:', error);\r\n        }\r\n      });\r\n  }\r\n\r\n  // 建案切換事件\r\n  onBuildCaseChange() {\r\n    this.loadRequirementsFromAPI();\r\n  }\r\n\r\n  // 搜尋事件\r\n  onSearch() {\r\n    this.loadRequirementsFromAPI();\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      this.searchRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n    }\r\n    this.loadRequirementsFromAPI();\r\n  }\r\n\r\n  loadRequirementsFromAPI() {\r\n    if (!this.searchRequest.CBuildCaseID) {\r\n      return;\r\n    }\r\n    \r\n    this.loading = true;\r\n    \r\n    // 準備API請求參數\r\n    const getRequirementListArgs: GetListRequirementRequest = {\r\n      CBuildCaseID: this.searchRequest.CBuildCaseID,\r\n      CHouseType: this.searchRequest.CHouseType,\r\n      CLocation: this.searchRequest.CLocation || null,\r\n      CRequirement: this.searchRequest.CRequirement || null,\r\n      CStatus: this.searchRequest.CStatus,\r\n      CIsShow: this.searchRequest.CIsShow,\r\n      CIsSimple: this.searchRequest.CIsSimple,\r\n      PageIndex: 1,\r\n      PageSize: 100\r\n    };\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({\r\n      body: getRequirementListArgs\r\n    }).subscribe({\r\n      next: (response: GetRequirementListResponseBase) => {\r\n        this.loading = false;\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          this.requirements = response.Entries.map(item => ({\r\n            ...item,\r\n            selected: false\r\n          }));\r\n        } else {\r\n          this.requirements = [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.loading = false;\r\n        this.requirements = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  onRequirementItemChange() {\r\n    // 當需求項目選擇變更時的處理\r\n  }\r\n\r\n  getSelectedItems(): ExtendedRequirementItem[] {\r\n    return this.requirements.filter(item => item.selected);\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.getSelectedItems().length > 0;\r\n      case 2:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 2) {\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const progressTexts = {\r\n      1: '請選擇要匯入的需求項目',\r\n      2: '確認匯入詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  importRequirements() {\r\n    const config: RequestItemImportConfig = {\r\n      buildCaseId: this.buildCaseId,\r\n      selectedItems: this.getSelectedItems(),\r\n      totalItems: this.getSelectedItems().length\r\n    };\r\n\r\n    this.itemsImported.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.resetSelections();\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  private resetSelections() {\r\n    this.currentStep = 1;\r\n    this.requirements.forEach(requirement => {\r\n      requirement.selected = false;\r\n    });\r\n  }\r\n\r\n  selectAll() {\r\n    const allSelected = this.requirements.every(item => item.selected);\r\n    this.requirements.forEach(item => {\r\n      item.selected = !allSelected;\r\n    });\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.getSelectedItems().length;\r\n  }\r\n\r\n  getTotalCount(): number {\r\n    return this.requirements.length;\r\n  }\r\n}", "<nb-card class=\"request-item-import-dialog\">\r\n  <nb-card-header class=\"d-flex justify-content-between align-items-center\">\r\n    <h5 class=\"mb-0\">需求項目匯入</h5>\r\n    <button nbButton ghost size=\"small\" (click)=\"close()\">\r\n      <nb-icon icon=\"close-outline\"></nb-icon>\r\n    </button>\r\n  </nb-card-header>\r\n\r\n  <nb-card-body>\r\n    <!-- 搜尋區塊 -->\r\n    <div class=\"search-section mb-4\" *ngIf=\"currentStep === 1\">\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-6\">\r\n          <label for=\"buildCase\" class=\"label mb-2\">建案</label>\r\n          <nb-select [(ngModel)]=\"searchRequest.CBuildCaseID\" \r\n                     (ngModelChange)=\"onBuildCaseChange()\"\r\n                     placeholder=\"請選擇建案\">\r\n            <nb-option *ngFor=\"let case of buildCaseList\" [value]=\"case.cID\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-6\">\r\n          <label for=\"houseType\" class=\"label mb-2\">房屋類型</label>\r\n          <nb-select [(ngModel)]=\"searchRequest.CHouseType\" \r\n                     multiple\r\n                     placeholder=\"請選擇房屋類型\">\r\n            <nb-option *ngFor=\"let type of houseTypeOptions\" [value]=\"type.value\">\r\n              {{ type.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-6\">\r\n          <label for=\"location\" class=\"label mb-2\">區域</label>\r\n          <input type=\"text\" \r\n                 nbInput \r\n                 id=\"location\" \r\n                 placeholder=\"請輸入區域\"\r\n                 [(ngModel)]=\"searchRequest.CLocation\">\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-6\">\r\n          <label for=\"requirement\" class=\"label mb-2\">工程項目</label>\r\n          <input type=\"text\" \r\n                 nbInput \r\n                 id=\"requirement\" \r\n                 placeholder=\"請輸入工程項目\"\r\n                 [(ngModel)]=\"searchRequest.CRequirement\">\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-right\">\r\n          <button class=\"btn btn-secondary me-2\" (click)=\"resetSearch()\">\r\n            <nb-icon icon=\"refresh-outline\"></nb-icon>\r\n            重置\r\n          </button>\r\n          <button class=\"btn btn-primary\" (click)=\"onSearch()\">\r\n            <nb-icon icon=\"search-outline\"></nb-icon>\r\n            搜尋\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <hr class=\"my-3\">\r\n    </div>\r\n\r\n    <!-- 進度指示器 -->\r\n    <div class=\"progress-indicator mb-4\">\r\n      <div class=\"progress-steps d-flex justify-content-between\">\r\n        <div class=\"step\" [class.active]=\"currentStep >= 1\" [class.completed]=\"currentStep > 1\">\r\n          <div class=\"step-number\">1</div>\r\n          <div class=\"step-label\">選擇項目</div>\r\n        </div>\r\n        <div class=\"step-connector\" [class.active]=\"currentStep > 1\"></div>\r\n        <div class=\"step\" [class.active]=\"currentStep >= 2\">\r\n          <div class=\"step-number\">2</div>\r\n          <div class=\"step-label\">確認匯入</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"progress-text text-center mt-2\">\r\n        <small class=\"text-muted\">{{ getProgressText() }}</small>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 載入中 -->\r\n    <div *ngIf=\"loading\" class=\"text-center py-4\">\r\n      <div class=\"spinner-border text-primary\" role=\"status\">\r\n        <span class=\"sr-only\">載入中...</span>\r\n      </div>\r\n      <div class=\"mt-2\">載入需求項目中...</div>\r\n    </div>\r\n\r\n    <!-- 步驟1：選擇需求項目 -->\r\n    <div *ngIf=\"currentStep === 1 && !loading\">\r\n      <div class=\"selection-header d-flex justify-content-between align-items-center mb-3\">\r\n        <h6 class=\"mb-0\">可匯入的需求項目</h6>\r\n        <div class=\"selection-actions\">\r\n          <button nbButton ghost size=\"small\" (click)=\"selectAll()\" class=\"me-2\">\r\n            <nb-icon icon=\"checkmark-square-2-outline\"></nb-icon>\r\n            全選/全消\r\n          </button>\r\n          <span class=\"text-muted\">已選擇 {{ getSelectedCount() }} / {{ getTotalCount() }} 項</span>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"requirement-list\" style=\"max-height: 400px; overflow-y: auto;\">\r\n        <div *ngIf=\"requirements.length === 0\" class=\"text-center py-4 text-muted\">\r\n          <nb-icon icon=\"inbox-outline\" class=\"mb-2\" style=\"font-size: 2rem;\"></nb-icon>\r\n          <div>沒有可匯入的需求項目</div>\r\n        </div>\r\n\r\n        <div *ngFor=\"let requirement of requirements\" class=\"requirement-item mb-2\">\r\n          <nb-card size=\"small\">\r\n            <nb-card-body class=\"py-2\">\r\n              <div class=\"d-flex align-items-center\">\r\n                <nb-checkbox \r\n                  [(ngModel)]=\"requirement.selected\" \r\n                  (ngModelChange)=\"onRequirementItemChange()\"\r\n                  class=\"me-3\">\r\n                </nb-checkbox>\r\n                \r\n                <div class=\"requirement-info flex-grow-1\">\r\n                  <div class=\"requirement-name fw-bold\">{{ requirement.CRequirement || '未命名需求' }}</div>\r\n                  <div class=\"requirement-details small text-muted\">\r\n                    <span *ngIf=\"requirement.CLocation\" class=\"me-3\">\r\n                      <nb-icon icon=\"pin-outline\"></nb-icon>\r\n                      位置: {{ requirement.CLocation }}\r\n                    </span>\r\n                    <span *ngIf=\"requirement.CUnit\" class=\"me-3\">\r\n                      <nb-icon icon=\"cube-outline\"></nb-icon>\r\n                      單位: {{ requirement.CUnit }}\r\n                    </span>\r\n                    <span *ngIf=\"requirement.CUnitPrice\" class=\"me-3\">\r\n                      <nb-icon icon=\"pricetags-outline\"></nb-icon>\r\n                      單價: {{ requirement.CUnitPrice | currency:'TWD':'symbol-narrow':'1.0-0' }}\r\n                    </span>\r\n                  </div>\r\n                  <div *ngIf=\"requirement.CRemark\" class=\"requirement-remark small text-info mt-1\">\r\n                    備註: {{ requirement.CRemark }}\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"requirement-status\">\r\n                  <span class=\"badge\" [ngClass]=\"requirement.CIsShow ? 'badge-success' : 'badge-secondary'\">\r\n                    {{ requirement.CIsShow ? '顯示' : '隱藏' }}\r\n                  </span>\r\n                  \r\n                  <span \r\n                    *ngIf=\"requirement.CIsSimple\" \r\n                    class=\"badge badge-info ms-1\">\r\n                    簡化\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </nb-card-body>\r\n          </nb-card>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 步驟2：確認匯入詳情 -->\r\n    <div *ngIf=\"currentStep === 2\">\r\n      <h6 class=\"mb-3\">匯入確認</h6>\r\n      \r\n      <div class=\"import-summary mb-4\">\r\n        <nb-card status=\"info\" size=\"small\">\r\n          <nb-card-body>\r\n            <div class=\"d-flex align-items-center\">\r\n              <nb-icon icon=\"info-outline\" class=\"me-2\"></nb-icon>\r\n              <div>\r\n                <div class=\"fw-bold\">即將匯入 {{ getSelectedCount() }} 個需求項目</div>\r\n                <div class=\"small text-muted\">這些項目將被加入到目前的建案中</div>\r\n              </div>\r\n            </div>\r\n          </nb-card-body>\r\n        </nb-card>\r\n      </div>\r\n\r\n      <div class=\"selected-items-preview\" style=\"max-height: 300px; overflow-y: auto;\">\r\n        <div *ngFor=\"let item of getSelectedItems(); let i = index\" class=\"selected-item mb-2\">\r\n          <nb-card size=\"small\">\r\n            <nb-card-body class=\"py-2\">\r\n              <div class=\"d-flex align-items-center\">\r\n                <div class=\"item-number me-3\">\r\n                  <span class=\"badge badge-primary\">{{ i + 1 }}</span>\r\n                </div>\r\n                <div class=\"item-info flex-grow-1\">\r\n                  <div class=\"item-name fw-bold\">{{ item.CRequirement || '未命名需求' }}</div>\r\n                  <div class=\"item-details small text-muted\">\r\n                    <span *ngIf=\"item.CLocation\" class=\"me-3\">位置: {{ item.CLocation }}</span>\r\n                    <span *ngIf=\"item.CUnit\" class=\"me-3\">單位: {{ item.CUnit }}</span>\r\n                    <span *ngIf=\"item.CUnitPrice\">單價: {{ item.CUnitPrice | currency:'TWD':'symbol-narrow':'1.0-0' }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </nb-card-body>\r\n          </nb-card>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <nb-card-footer class=\"d-flex justify-content-between\">\r\n    <div class=\"left-actions\">\r\n      <button \r\n        *ngIf=\"currentStep > 1\" \r\n        nbButton \r\n        ghost \r\n        (click)=\"previousStep()\">\r\n        <nb-icon icon=\"arrow-back-outline\"></nb-icon>\r\n        上一步\r\n      </button>\r\n    </div>\r\n\r\n    <div class=\"right-actions\">\r\n      <button nbButton ghost (click)=\"close()\" class=\"me-2\">\r\n        取消\r\n      </button>\r\n      \r\n      <button \r\n        *ngIf=\"currentStep < 2\" \r\n        nbButton \r\n        status=\"primary\" \r\n        [disabled]=\"!canProceed()\" \r\n        (click)=\"nextStep()\">\r\n        下一步\r\n        <nb-icon icon=\"arrow-forward-outline\"></nb-icon>\r\n      </button>\r\n      \r\n      <button \r\n        *ngIf=\"currentStep === 2\" \r\n        nbButton \r\n        status=\"success\" \r\n        (click)=\"importRequirements()\">\r\n        <nb-icon icon=\"download-outline\"></nb-icon>\r\n        確認匯入\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,EACdC,aAAa,QAER,gBAAgB;AAGvB,SAAoCC,cAAc,QAAiE,yBAAyB;AAE5I,SAASC,aAAa,QAAQ,mCAAmC;;;;;;;;;;ICArDC,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,GAAA,CAAkB;IAC9DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,cAAA,MACF;;;;;IAQAT,EAAA,CAAAC,cAAA,oBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAAC,KAAA,CAAoB;IACnEX,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,OAAA,CAAAE,KAAA,MACF;;;;;;IAhBFZ,EAHN,CAAAC,cAAA,cAA2D,cACxC,cACyB,gBACI;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpDH,EAAA,CAAAC,cAAA,oBAE+B;IAFpBD,EAAA,CAAAa,gBAAA,2BAAAC,6EAAAC,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAAnB,EAAA,CAAAoB,kBAAA,CAAAF,MAAA,CAAAG,aAAA,CAAAC,YAAA,EAAAP,MAAA,MAAAG,MAAA,CAAAG,aAAA,CAAAC,YAAA,GAAAP,MAAA;MAAA,OAAAf,EAAA,CAAAuB,WAAA,CAAAR,MAAA;IAAA,EAAwC;IACxCf,EAAA,CAAAwB,UAAA,2BAAAV,6EAAA;MAAAd,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAuB,WAAA,CAAiBL,MAAA,CAAAO,iBAAA,EAAmB;IAAA,EAAC;IAE9CzB,EAAA,CAAA0B,UAAA,IAAAC,qDAAA,wBAAiE;IAIrE3B,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,cAAwC,gBACI;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAC,cAAA,qBAEiC;IAFtBD,EAAA,CAAAa,gBAAA,2BAAAe,8EAAAb,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAAnB,EAAA,CAAAoB,kBAAA,CAAAF,MAAA,CAAAG,aAAA,CAAAQ,UAAA,EAAAd,MAAA,MAAAG,MAAA,CAAAG,aAAA,CAAAQ,UAAA,GAAAd,MAAA;MAAA,OAAAf,EAAA,CAAAuB,WAAA,CAAAR,MAAA;IAAA,EAAsC;IAG/Cf,EAAA,CAAA0B,UAAA,KAAAI,sDAAA,wBAAsE;IAK5E9B,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAiB,eACyB,iBACG;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnDH,EAAA,CAAAC,cAAA,iBAI6C;IAAtCD,EAAA,CAAAa,gBAAA,2BAAAkB,0EAAAhB,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAAnB,EAAA,CAAAoB,kBAAA,CAAAF,MAAA,CAAAG,aAAA,CAAAW,SAAA,EAAAjB,MAAA,MAAAG,MAAA,CAAAG,aAAA,CAAAW,SAAA,GAAAjB,MAAA;MAAA,OAAAf,EAAA,CAAAuB,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAC9Cf,EALE,CAAAG,YAAA,EAI6C,EACzC;IAEJH,EADF,CAAAC,cAAA,eAAwC,iBACM;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxDH,EAAA,CAAAC,cAAA,iBAIgD;IAAzCD,EAAA,CAAAa,gBAAA,2BAAAoB,0EAAAlB,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAAnB,EAAA,CAAAoB,kBAAA,CAAAF,MAAA,CAAAG,aAAA,CAAAa,YAAA,EAAAnB,MAAA,MAAAG,MAAA,CAAAG,aAAA,CAAAa,YAAA,GAAAnB,MAAA;MAAA,OAAAf,EAAA,CAAAuB,WAAA,CAAAR,MAAA;IAAA,EAAwC;IAEnDf,EANI,CAAAG,YAAA,EAIgD,EAC5C,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAiB,eACgB,kBACkC;IAAxBD,EAAA,CAAAwB,UAAA,mBAAAW,mEAAA;MAAAnC,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAuB,WAAA,CAASL,MAAA,CAAAkB,WAAA,EAAa;IAAA,EAAC;IAC5DpC,EAAA,CAAAqC,SAAA,mBAA0C;IAC1CrC,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAqD;IAArBD,EAAA,CAAAwB,UAAA,mBAAAc,mEAAA;MAAAtC,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAuB,WAAA,CAASL,MAAA,CAAAqB,QAAA,EAAU;IAAA,EAAC;IAClDvC,EAAA,CAAAqC,SAAA,mBAAyC;IACzCrC,EAAA,CAAAE,MAAA,sBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IACNH,EAAA,CAAAqC,SAAA,cAAiB;IACnBrC,EAAA,CAAAG,YAAA,EAAM;;;;IAlDWH,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAwC,gBAAA,YAAAtB,MAAA,CAAAG,aAAA,CAAAC,YAAA,CAAwC;IAGrBtB,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAc,MAAA,CAAAuB,aAAA,CAAgB;IAOnCzC,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAwC,gBAAA,YAAAtB,MAAA,CAAAG,aAAA,CAAAQ,UAAA,CAAsC;IAGnB7B,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAI,UAAA,YAAAc,MAAA,CAAAwB,gBAAA,CAAmB;IAa1C1C,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAwC,gBAAA,YAAAtB,MAAA,CAAAG,aAAA,CAAAW,SAAA,CAAqC;IAQrChC,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAwC,gBAAA,YAAAtB,MAAA,CAAAG,aAAA,CAAAa,YAAA,CAAwC;;;;;IAuCjDlC,EAFJ,CAAAC,cAAA,cAA8C,cACW,eAC/B;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;IACNH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,oDAAU;IAC9BF,EAD8B,CAAAG,YAAA,EAAM,EAC9B;;;;;IAgBFH,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAqC,SAAA,kBAA8E;IAC9ErC,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,mEAAU;IACjBF,EADiB,CAAAG,YAAA,EAAM,EACjB;;;;;IAeMH,EAAA,CAAAC,cAAA,eAAiD;IAC/CD,EAAA,CAAAqC,SAAA,kBAAsC;IACtCrC,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,oBAAAmC,cAAA,CAAAX,SAAA,MACF;;;;;IACAhC,EAAA,CAAAC,cAAA,eAA6C;IAC3CD,EAAA,CAAAqC,SAAA,kBAAuC;IACvCrC,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,oBAAAmC,cAAA,CAAAC,KAAA,MACF;;;;;IACA5C,EAAA,CAAAC,cAAA,eAAkD;IAChDD,EAAA,CAAAqC,SAAA,kBAA4C;IAC5CrC,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,oBAAAR,EAAA,CAAA6C,WAAA,OAAAF,cAAA,CAAAG,UAAA,wCACF;;;;;IAEF9C,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,oBAAAmC,cAAA,CAAAI,OAAA,MACF;;;;;IAQA/C,EAAA,CAAAC,cAAA,eAEgC;IAC9BD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IApCTH,EAJR,CAAAC,cAAA,cAA4E,kBACpD,uBACO,cACc,sBAItB;IAFbD,EAAA,CAAAa,gBAAA,2BAAAmC,uFAAAjC,MAAA;MAAA,MAAA4B,cAAA,GAAA3C,EAAA,CAAAgB,aAAA,CAAAiC,GAAA,EAAAC,SAAA;MAAAlD,EAAA,CAAAoB,kBAAA,CAAAuB,cAAA,CAAAQ,QAAA,EAAApC,MAAA,MAAA4B,cAAA,CAAAQ,QAAA,GAAApC,MAAA;MAAA,OAAAf,EAAA,CAAAuB,WAAA,CAAAR,MAAA;IAAA,EAAkC;IAClCf,EAAA,CAAAwB,UAAA,2BAAAwB,uFAAA;MAAAhD,EAAA,CAAAgB,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAuB,WAAA,CAAiBL,MAAA,CAAAkC,uBAAA,EAAyB;IAAA,EAAC;IAE7CpD,EAAA,CAAAG,YAAA,EAAc;IAGZH,EADF,CAAAC,cAAA,cAA0C,cACF;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACrFH,EAAA,CAAAC,cAAA,cAAkD;IAShDD,EARA,CAAA0B,UAAA,IAAA2B,wDAAA,mBAAiD,KAAAC,yDAAA,mBAIJ,KAAAC,yDAAA,mBAIK;IAIpDvD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAA0B,UAAA,KAAA8B,wDAAA,kBAAiF;IAGnFxD,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,eAAgC,gBAC4D;IACxFD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEPH,EAAA,CAAA0B,UAAA,KAAA+B,yDAAA,mBAEgC;IAO1CzD,EAJQ,CAAAG,YAAA,EAAM,EACF,EACO,EACP,EACN;;;;IAxCIH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAwC,gBAAA,YAAAG,cAAA,CAAAQ,QAAA,CAAkC;IAMInD,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAA0D,iBAAA,CAAAf,cAAA,CAAAT,YAAA,qCAAyC;IAEtElC,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAI,UAAA,SAAAuC,cAAA,CAAAX,SAAA,CAA2B;IAI3BhC,EAAA,CAAAO,SAAA,EAAuB;IAAvBP,EAAA,CAAAI,UAAA,SAAAuC,cAAA,CAAAC,KAAA,CAAuB;IAIvB5C,EAAA,CAAAO,SAAA,EAA4B;IAA5BP,EAAA,CAAAI,UAAA,SAAAuC,cAAA,CAAAG,UAAA,CAA4B;IAK/B9C,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAAuC,cAAA,CAAAI,OAAA,CAAyB;IAMX/C,EAAA,CAAAO,SAAA,GAAqE;IAArEP,EAAA,CAAAI,UAAA,YAAAuC,cAAA,CAAAgB,OAAA,uCAAqE;IACvF3D,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAmC,cAAA,CAAAgB,OAAA,wCACF;IAGG3D,EAAA,CAAAO,SAAA,EAA2B;IAA3BP,EAAA,CAAAI,UAAA,SAAAuC,cAAA,CAAAiB,SAAA,CAA2B;;;;;;IArDxC5D,EAFJ,CAAAC,cAAA,UAA2C,cAC4C,YAClE;IAAAD,EAAA,CAAAE,MAAA,uDAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE5BH,EADF,CAAAC,cAAA,cAA+B,iBAC0C;IAAnCD,EAAA,CAAAwB,UAAA,mBAAAqC,mEAAA;MAAA7D,EAAA,CAAAgB,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAuB,WAAA,CAASL,MAAA,CAAA6C,SAAA,EAAW;IAAA,EAAC;IACvD/D,EAAA,CAAAqC,SAAA,kBAAqD;IACrDrC,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAEnFF,EAFmF,CAAAG,YAAA,EAAO,EAClF,EACF;IAENH,EAAA,CAAAC,cAAA,eAA2E;IAMzED,EALA,CAAA0B,UAAA,KAAAsC,iDAAA,kBAA2E,KAAAC,iDAAA,mBAKC;IA+ChFjE,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAzDyBH,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAkE,kBAAA,wBAAAhD,MAAA,CAAAiD,gBAAA,WAAAjD,MAAA,CAAAkD,aAAA,cAAsD;IAK3EpE,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,SAAAc,MAAA,CAAAmD,YAAA,CAAAC,MAAA,OAA+B;IAKRtE,EAAA,CAAAO,SAAA,EAAe;IAAfP,EAAA,CAAAI,UAAA,YAAAc,MAAA,CAAAmD,YAAA,CAAe;;;;;IA8EhCrE,EAAA,CAAAC,cAAA,eAA0C;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA/BH,EAAA,CAAAO,SAAA,EAAwB;IAAxBP,EAAA,CAAAQ,kBAAA,mBAAA+D,OAAA,CAAAvC,SAAA,KAAwB;;;;;IAClEhC,EAAA,CAAAC,cAAA,eAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA3BH,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAQ,kBAAA,mBAAA+D,OAAA,CAAA3B,KAAA,KAAoB;;;;;IAC1D5C,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAkE;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzEH,EAAA,CAAAO,SAAA,EAAkE;IAAlEP,EAAA,CAAAQ,kBAAA,mBAAAR,EAAA,CAAA6C,WAAA,OAAA0B,OAAA,CAAAzB,UAAA,uCAAkE;;;;;IAPlG9C,EALV,CAAAC,cAAA,cAAuF,kBAC/D,uBACO,cACc,cACP,eACM;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAC/CF,EAD+C,CAAAG,YAAA,EAAO,EAChD;IAEJH,EADF,CAAAC,cAAA,cAAmC,cACF;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvEH,EAAA,CAAAC,cAAA,eAA2C;IAGzCD,EAFA,CAAA0B,UAAA,KAAA8C,yDAAA,mBAA0C,KAAAC,yDAAA,mBACJ,KAAAC,yDAAA,mBACR;IAM1C1E,EALU,CAAAG,YAAA,EAAM,EACF,EACF,EACO,EACP,EACN;;;;;IAbsCH,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAA0D,iBAAA,CAAAiB,IAAA,KAAW;IAGd3E,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAA0D,iBAAA,CAAAa,OAAA,CAAArC,YAAA,qCAAkC;IAExDlC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,SAAAmE,OAAA,CAAAvC,SAAA,CAAoB;IACpBhC,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,SAAAmE,OAAA,CAAA3B,KAAA,CAAgB;IAChB5C,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAAI,UAAA,SAAAmE,OAAA,CAAAzB,UAAA,CAAqB;;;;;IA7B1C9C,EADF,CAAAC,cAAA,UAA+B,aACZ;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAKpBH,EAHN,CAAAC,cAAA,cAAiC,kBACK,mBACpB,cAC2B;IACrCD,EAAA,CAAAqC,SAAA,kBAAoD;IAElDrC,EADF,CAAAC,cAAA,UAAK,cACkB;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9DH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,MAAA,kGAAe;IAKvDF,EALuD,CAAAG,YAAA,EAAM,EAC/C,EACF,EACO,EACP,EACN;IAENH,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAA0B,UAAA,KAAAkD,iDAAA,mBAAuF;IAoB3F5E,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA7B2BH,EAAA,CAAAO,SAAA,IAAmC;IAAnCP,EAAA,CAAAQ,kBAAA,8BAAAU,MAAA,CAAAiD,gBAAA,sCAAmC;IAS1CnE,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,YAAAc,MAAA,CAAA2D,gBAAA,GAAuB;;;;;;IAyB/C7E,EAAA,CAAAC,cAAA,iBAI2B;IAAzBD,EAAA,CAAAwB,UAAA,mBAAAsD,sEAAA;MAAA9E,EAAA,CAAAgB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAuB,WAAA,CAASL,MAAA,CAAA8D,YAAA,EAAc;IAAA,EAAC;IACxBhF,EAAA,CAAAqC,SAAA,kBAA6C;IAC7CrC,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAQTH,EAAA,CAAAC,cAAA,iBAKuB;IAArBD,EAAA,CAAAwB,UAAA,mBAAAyD,sEAAA;MAAAjF,EAAA,CAAAgB,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAuB,WAAA,CAASL,MAAA,CAAAiE,QAAA,EAAU;IAAA,EAAC;IACpBnF,EAAA,CAAAE,MAAA,2BACA;IAAAF,EAAA,CAAAqC,SAAA,kBAAgD;IAClDrC,EAAA,CAAAG,YAAA,EAAS;;;;IAJPH,EAAA,CAAAI,UAAA,cAAAc,MAAA,CAAAkE,UAAA,GAA0B;;;;;;IAM5BpF,EAAA,CAAAC,cAAA,iBAIiC;IAA/BD,EAAA,CAAAwB,UAAA,mBAAA6D,sEAAA;MAAArF,EAAA,CAAAgB,aAAA,CAAAsE,IAAA;MAAA,MAAApE,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAuB,WAAA,CAASL,MAAA,CAAAqE,kBAAA,EAAoB;IAAA,EAAC;IAC9BvF,EAAA,CAAAqC,SAAA,kBAA2C;IAC3CrC,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;AD9Lf,OAAM,MAAOqF,0BAA0B;EAarCC,YACUC,kBAAsC,EACtCC,SAAkD,EAClDC,UAAsB,EACtBC,gBAAkC;IAHlC,KAAAH,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAhBjB,KAAAC,WAAW,GAAW,CAAC;IACtB,KAAAC,aAAa,GAAG,IAAI3G,YAAY,EAA2B;IAErE,KAAA4G,WAAW,GAAW,CAAC;IACvB,KAAA3B,YAAY,GAA8B,EAAE;IAC5C,KAAA4B,OAAO,GAAY,KAAK;IAExB;IACA,KAAA5E,aAAa,GAAyF,EAAE;IACxG,KAAAoB,aAAa,GAA8B,EAAE;IAC7C,KAAAC,gBAAgB,GAAU,EAAE;EAOxB;EAEJwD,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA;EACAD,oBAAoBA,CAAA;IAClB,IAAI,CAACzD,gBAAgB,GAAG,IAAI,CAACkD,UAAU,CAACS,cAAc,CAACtG,aAAa,CAAC;IACrE,IAAI,CAACsB,aAAa,CAACiF,OAAO,GAAG,CAAC,CAAC,CAAC;IAChC,IAAI,CAACjF,aAAa,CAACsC,OAAO,GAAG,IAAI,CAAC,CAAC;IACnC,IAAI,CAACtC,aAAa,CAACuC,SAAS,GAAG,IAAI;IACnC,IAAI,CAACvC,aAAa,CAACa,YAAY,GAAG,EAAE;IACpC,IAAI,CAACb,aAAa,CAACW,SAAS,GAAG,EAAE;IACjC;IACA,IAAI,CAACX,aAAa,CAACQ,UAAU,GAAG,IAAI,CAACa,gBAAgB,CAAC6D,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC7F,KAAK,CAAC;EAC/E;EAEA;EACAyF,gBAAgBA,CAAA;IACd,IAAI,CAACP,gBAAgB,CAACY,qCAAqC,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEC,SAAS,CAAC;MACTC,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,IAAID,GAAG,CAACE,OAAO,EAAE;UACvC,IAAI,CAACtE,aAAa,GAAGoE,GAAG,CAACE,OAAO;UAChC;UACA,IAAI,IAAI,CAACjB,WAAW,IAAI,IAAI,CAACA,WAAW,GAAG,CAAC,EAAE;YAC5C,IAAI,CAACzE,aAAa,CAACC,YAAY,GAAG,IAAI,CAACwE,WAAW;UACpD,CAAC,MAAM,IAAI,IAAI,CAACrD,aAAa,CAAC6B,MAAM,GAAG,CAAC,EAAE;YACxC,IAAI,CAACjD,aAAa,CAACC,YAAY,GAAG,IAAI,CAACmB,aAAa,CAAC,CAAC,CAAC,CAACnC,GAAG;UAC7D;UACA;UACA,IAAI,IAAI,CAACe,aAAa,CAACC,YAAY,EAAE;YACnC,IAAI,CAAC0F,uBAAuB,EAAE;UAChC;QACF;MACF,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;KACD,CAAC;EACN;EAEA;EACAxF,iBAAiBA,CAAA;IACf,IAAI,CAACuF,uBAAuB,EAAE;EAChC;EAEA;EACAzE,QAAQA,CAAA;IACN,IAAI,CAACyE,uBAAuB,EAAE;EAChC;EAEA;EACA5E,WAAWA,CAAA;IACT,IAAI,CAAC+D,oBAAoB,EAAE;IAC3B,IAAI,IAAI,CAAC1D,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC6B,MAAM,GAAG,CAAC,EAAE;MACvD,IAAI,CAACjD,aAAa,CAACC,YAAY,GAAG,IAAI,CAACmB,aAAa,CAAC,CAAC,CAAC,CAACnC,GAAG;IAC7D;IACA,IAAI,CAAC0G,uBAAuB,EAAE;EAChC;EAEAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAC3F,aAAa,CAACC,YAAY,EAAE;MACpC;IACF;IAEA,IAAI,CAAC2E,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMkB,sBAAsB,GAA8B;MACxD7F,YAAY,EAAE,IAAI,CAACD,aAAa,CAACC,YAAY;MAC7CO,UAAU,EAAE,IAAI,CAACR,aAAa,CAACQ,UAAU;MACzCG,SAAS,EAAE,IAAI,CAACX,aAAa,CAACW,SAAS,IAAI,IAAI;MAC/CE,YAAY,EAAE,IAAI,CAACb,aAAa,CAACa,YAAY,IAAI,IAAI;MACrDoE,OAAO,EAAE,IAAI,CAACjF,aAAa,CAACiF,OAAO;MACnC3C,OAAO,EAAE,IAAI,CAACtC,aAAa,CAACsC,OAAO;MACnCC,SAAS,EAAE,IAAI,CAACvC,aAAa,CAACuC,SAAS;MACvCwD,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAED,IAAI,CAAC3B,kBAAkB,CAAC4B,8BAA8B,CAAC;MACrDZ,IAAI,EAAES;KACP,CAAC,CAACR,SAAS,CAAC;MACXC,IAAI,EAAGW,QAAwC,IAAI;QACjD,IAAI,CAACtB,OAAO,GAAG,KAAK;QACpB,IAAIsB,QAAQ,CAACT,UAAU,KAAK,CAAC,IAAIS,QAAQ,CAACR,OAAO,EAAE;UACjD,IAAI,CAAC1C,YAAY,GAAGkD,QAAQ,CAACR,OAAO,CAACR,GAAG,CAACiB,IAAI,KAAK;YAChD,GAAGA,IAAI;YACPrE,QAAQ,EAAE;WACX,CAAC,CAAC;QACL,CAAC,MAAM;UACL,IAAI,CAACkB,YAAY,GAAG,EAAE;QACxB;MACF,CAAC;MACD4C,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChB,OAAO,GAAG,KAAK;QACpB,IAAI,CAAC5B,YAAY,GAAG,EAAE;MACxB;KACD,CAAC;EACJ;EAEAjB,uBAAuBA,CAAA;IACrB;EAAA;EAGFyB,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACR,YAAY,CAACoD,MAAM,CAACD,IAAI,IAAIA,IAAI,CAACrE,QAAQ,CAAC;EACxD;EAEAiC,UAAUA,CAAA;IACR,QAAQ,IAAI,CAACY,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACnB,gBAAgB,EAAE,CAACP,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAa,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,UAAU,EAAE,IAAI,IAAI,CAACY,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAhB,YAAYA,CAAA;IACV,IAAI,IAAI,CAACgB,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEA0B,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG;MACpB,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;KACJ;IACD,OAAOA,aAAa,CAAC,IAAI,CAAC3B,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAT,kBAAkBA,CAAA;IAChB,MAAMqC,MAAM,GAA4B;MACtC9B,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B+B,aAAa,EAAE,IAAI,CAAChD,gBAAgB,EAAE;MACtCiD,UAAU,EAAE,IAAI,CAACjD,gBAAgB,EAAE,CAACP;KACrC;IAED,IAAI,CAACyB,aAAa,CAACgC,IAAI,CAACH,MAAM,CAAC;IAC/B,IAAI,CAACI,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACtC,SAAS,CAACqC,KAAK,EAAE;EACxB;EAEQC,eAAeA,CAAA;IACrB,IAAI,CAACjC,WAAW,GAAG,CAAC;IACpB,IAAI,CAAC3B,YAAY,CAAC6D,OAAO,CAACC,WAAW,IAAG;MACtCA,WAAW,CAAChF,QAAQ,GAAG,KAAK;IAC9B,CAAC,CAAC;EACJ;EAEAY,SAASA,CAAA;IACP,MAAMqE,WAAW,GAAG,IAAI,CAAC/D,YAAY,CAACgE,KAAK,CAACb,IAAI,IAAIA,IAAI,CAACrE,QAAQ,CAAC;IAClE,IAAI,CAACkB,YAAY,CAAC6D,OAAO,CAACV,IAAI,IAAG;MAC/BA,IAAI,CAACrE,QAAQ,GAAG,CAACiF,WAAW;IAC9B,CAAC,CAAC;EACJ;EAEAjE,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACU,gBAAgB,EAAE,CAACP,MAAM;EACvC;EAEAF,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,YAAY,CAACC,MAAM;EACjC;;;uCArMWkB,0BAA0B,EAAAxF,EAAA,CAAAsI,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAxI,EAAA,CAAAsI,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1I,EAAA,CAAAsI,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAA5I,EAAA,CAAAsI,iBAAA,CAAAO,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAA1BtD,0BAA0B;MAAAuD,SAAA;MAAAC,MAAA;QAAAlD,WAAA;MAAA;MAAAmD,OAAA;QAAAlD,aAAA;MAAA;MAAAmD,UAAA;MAAAC,QAAA,GAAAnJ,EAAA,CAAAoJ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5CnC1J,EAFJ,CAAAC,cAAA,iBAA4C,wBACgC,YACvD;UAAAD,EAAA,CAAAE,MAAA,2CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,gBAAsD;UAAlBD,EAAA,CAAAwB,UAAA,mBAAAoI,4DAAA;YAAA,OAASD,GAAA,CAAA3B,KAAA,EAAO;UAAA,EAAC;UACnDhI,EAAA,CAAAqC,SAAA,iBAAwC;UAE5CrC,EADE,CAAAG,YAAA,EAAS,EACM;UAEjBH,EAAA,CAAAC,cAAA,mBAAc;UAEZD,EAAA,CAAA0B,UAAA,IAAAmI,yCAAA,kBAA2D;UA4DrD7J,EAHN,CAAAC,cAAA,aAAqC,aACwB,cAC+B,cAC7D;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChCH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAC9BF,EAD8B,CAAAG,YAAA,EAAM,EAC9B;UACNH,EAAA,CAAAqC,SAAA,eAAmE;UAEjErC,EADF,CAAAC,cAAA,cAAoD,cACzB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChCH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAEhCF,EAFgC,CAAAG,YAAA,EAAM,EAC9B,EACF;UAEJH,EADF,CAAAC,cAAA,eAA4C,iBAChB;UAAAD,EAAA,CAAAE,MAAA,IAAuB;UAErDF,EAFqD,CAAAG,YAAA,EAAQ,EACrD,EACF;UA+ENH,EA5EA,CAAA0B,UAAA,KAAAoI,0CAAA,kBAA8C,KAAAC,0CAAA,mBAQH,KAAAC,0CAAA,mBAoEZ;UAuCjChK,EAAA,CAAAG,YAAA,EAAe;UAGbH,EADF,CAAAC,cAAA,0BAAuD,eAC3B;UACxBD,EAAA,CAAA0B,UAAA,KAAAuI,6CAAA,qBAI2B;UAI7BjK,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,eAA2B,kBAC6B;UAA/BD,EAAA,CAAAwB,UAAA,mBAAA0I,6DAAA;YAAA,OAASP,GAAA,CAAA3B,KAAA,EAAO;UAAA,EAAC;UACtChI,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAYTH,EAVA,CAAA0B,UAAA,KAAAyI,6CAAA,qBAKuB,KAAAC,6CAAA,qBASU;UAMvCpK,EAFI,CAAAG,YAAA,EAAM,EACS,EACT;;;UArO4BH,EAAA,CAAAO,SAAA,GAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAAuJ,GAAA,CAAA3D,WAAA,OAAuB;UA2DnChG,EAAA,CAAAO,SAAA,GAAiC;UAACP,EAAlC,CAAAqK,WAAA,WAAAV,GAAA,CAAA3D,WAAA,MAAiC,cAAA2D,GAAA,CAAA3D,WAAA,KAAoC;UAI3DhG,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAqK,WAAA,WAAAV,GAAA,CAAA3D,WAAA,KAAgC;UAC1ChG,EAAA,CAAAO,SAAA,EAAiC;UAAjCP,EAAA,CAAAqK,WAAA,WAAAV,GAAA,CAAA3D,WAAA,MAAiC;UAMzBhG,EAAA,CAAAO,SAAA,GAAuB;UAAvBP,EAAA,CAAA0D,iBAAA,CAAAiG,GAAA,CAAAjC,eAAA,GAAuB;UAK/C1H,EAAA,CAAAO,SAAA,EAAa;UAAbP,EAAA,CAAAI,UAAA,SAAAuJ,GAAA,CAAA1D,OAAA,CAAa;UAQbjG,EAAA,CAAAO,SAAA,EAAmC;UAAnCP,EAAA,CAAAI,UAAA,SAAAuJ,GAAA,CAAA3D,WAAA,WAAA2D,GAAA,CAAA1D,OAAA,CAAmC;UAoEnCjG,EAAA,CAAAO,SAAA,EAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAAuJ,GAAA,CAAA3D,WAAA,OAAuB;UA4CxBhG,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,SAAAuJ,GAAA,CAAA3D,WAAA,KAAqB;UAerBhG,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,SAAAuJ,GAAA,CAAA3D,WAAA,KAAqB;UAUrBhG,EAAA,CAAAO,SAAA,EAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAAuJ,GAAA,CAAA3D,WAAA,OAAuB;;;qBDrM5B3G,YAAY,EAAAiL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,YAAA,EACZpL,WAAW,EAAAqL,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXvL,YAAY,EAAAkJ,EAAA,CAAAsC,eAAA,EAAAtC,EAAA,CAAAuC,mBAAA,EAAAvC,EAAA,CAAAwC,qBAAA,EAAAxC,EAAA,CAAAyC,qBAAA,EACZ1L,cAAc,EAAAiJ,EAAA,CAAA0C,iBAAA,EACd1L,YAAY,EAAAgJ,EAAA,CAAA2C,eAAA,EACZ1L,gBAAgB,EAAA+I,EAAA,CAAA4C,mBAAA,EAChB1L,cAAc,EAAA8I,EAAA,CAAA6C,iBAAA,EAAA7C,EAAA,CAAA8C,iBAAA,EACd3L,cAAc,EACdC,aAAa,EAAA4I,EAAA,CAAA+C,gBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}