{"ast": null, "code": "import { FooterComponent } from '../../components/footer/footer.component';\nimport { HeaderComponent } from '../../components/header/header.component';\nimport { NbLayoutModule, NbSidebarModule } from '@nebular/theme';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nconst _c0 = [[[\"nb-menu\"]], [[\"router-outlet\"]]];\nconst _c1 = [\"nb-menu\", \"router-outlet\"];\nexport class OneColumnLayoutComponent {\n  static {\n    this.ɵfac = function OneColumnLayoutComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OneColumnLayoutComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OneColumnLayoutComponent,\n      selectors: [[\"ngx-one-column-layout\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 9,\n      vars: 0,\n      consts: [[\"windowMode\", \"\"], [\"fixed\", \"\"], [\"tag\", \"menu-sidebar\", \"responsive\", \"\", 1, \"menu-sidebar\"]],\n      template: function OneColumnLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"nb-layout\", 0)(1, \"nb-layout-header\", 1);\n          i0.ɵɵelement(2, \"ngx-header\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-sidebar\", 2);\n          i0.ɵɵprojection(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"nb-layout-column\");\n          i0.ɵɵprojection(6, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"nb-layout-footer\", 1);\n          i0.ɵɵelement(8, \"ngx-footer\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [NbLayoutModule, i1.NbLayoutComponent, i1.NbLayoutColumnComponent, i1.NbLayoutFooterComponent, i1.NbLayoutHeaderComponent, HeaderComponent, NbSidebarModule, i1.NbSidebarComponent, FooterComponent, RouterModule],\n      styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 141:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nUndefined variable.\\\\n   \\u2577\\\\n32 \\u2502 $gradient-success: linear-gradient(135deg, $success-light 0%, $success-base 100%);\\\\r\\\\n   \\u2502                                            ^^^^^^^^^^^^^^\\\\n   \\u2575\\\\n  src\\\\\\\\app\\\\\\\\@theme\\\\\\\\styles\\\\\\\\_colors.scss 32:44                      @import\\\\n  src\\\\\\\\app\\\\\\\\@theme\\\\\\\\styles\\\\\\\\themes.scss 6:9                         @import\\\\n  src\\\\\\\\app\\\\\\\\@theme\\\\\\\\layouts\\\\\\\\one-column\\\\\\\\one-column.layout.scss 1:9  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[141]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FooterComponent", "HeaderComponent", "NbLayoutModule", "NbSidebarModule", "RouterModule", "OneColumnLayoutComponent", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "ngContentSelectors", "_c1", "decls", "vars", "consts", "template", "OneColumnLayoutComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵprojection", "i1", "NbLayoutComponent", "NbLayoutColumnComponent", "NbLayoutFooterComponent", "NbLayoutHeaderComponent", "NbSidebarComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\layouts\\one-column\\one-column.layout.ts"], "sourcesContent": ["import { Component, NgModule } from '@angular/core';\r\nimport { FooterComponent } from '../../components/footer/footer.component';\r\nimport { HeaderComponent } from '../../components/header/header.component';\r\nimport { NbLayoutModule, NbSidebarModule } from '@nebular/theme';\r\nimport { NgFor } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\n\r\n@Component({\r\n  selector: 'ngx-one-column-layout',\r\n  styleUrls: ['./one-column.layout.scss'],\r\n  template: `\r\n    <nb-layout windowMode>\r\n      <nb-layout-header fixed>\r\n        <ngx-header></ngx-header>\r\n      </nb-layout-header>\r\n      <nb-sidebar class=\"menu-sidebar\" tag=\"menu-sidebar\" responsive>\r\n        <ng-content select=\"nb-menu\"></ng-content>\r\n      </nb-sidebar>\r\n      <!-- <ul class=\"space-y-2\">\r\n      <li *ngFor=\"let menu of menus\" class=\"group\">\r\n        <a \r\n          [routerLink]=\"[menu.url]\" \r\n          class=\"flex items-center text-base font-medium text-gray-900 rounded-lg hover:bg-gray-100 transition duration-300\"\r\n        >\r\n          <i [class]=\"menu.icon + ' mr-3 text-gray-500 group-hover:text-gray-900 transition duration-300'\"></i> \r\n          <span class=\"group-hover:text-gray-900 transition duration-300\">{{ menu.title }}</span>\r\n        </a>\r\n      </li>\r\n    </ul> -->\r\n\r\n      <nb-layout-column>\r\n        <ng-content select=\"router-outlet\"></ng-content>\r\n      </nb-layout-column>\r\n      <nb-layout-footer fixed>\r\n        <ngx-footer></ngx-footer>\r\n      </nb-layout-footer>\r\n    </nb-layout>\r\n  `,\r\n  standalone: true,\r\n  imports: [\r\n    NbLayoutModule,\r\n    HeaderComponent,\r\n    NbSidebarModule,\r\n    FooterComponent,\r\n    NgFor,\r\n    RouterModule],\r\n})\r\nexport class OneColumnLayoutComponent {}\r\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,0CAA0C;AAC1E,SAASC,eAAe,QAAQ,0CAA0C;AAC1E,SAASC,cAAc,EAAEC,eAAe,QAAQ,gBAAgB;AAEhE,SAASC,YAAY,QAAQ,iBAAiB;;;;;AA2C9C,OAAM,MAAOC,wBAAwB;;;uCAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA,wBAAwB;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,kBAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UAnC/BT,EADF,CAAAW,cAAA,mBAAsB,0BACI;UACtBX,EAAA,CAAAY,SAAA,iBAAyB;UAC3BZ,EAAA,CAAAa,YAAA,EAAmB;UACnBb,EAAA,CAAAW,cAAA,oBAA+D;UAC7DX,EAAA,CAAAc,YAAA,GAA0C;UAC5Cd,EAAA,CAAAa,YAAA,EAAa;UAabb,EAAA,CAAAW,cAAA,uBAAkB;UAChBX,EAAA,CAAAc,YAAA,MAAgD;UAClDd,EAAA,CAAAa,YAAA,EAAmB;UACnBb,EAAA,CAAAW,cAAA,0BAAwB;UACtBX,EAAA,CAAAY,SAAA,iBAAyB;UAE7BZ,EADE,CAAAa,YAAA,EAAmB,EACT;;;qBAIZpB,cAAc,EAAAsB,EAAA,CAAAC,iBAAA,EAAAD,EAAA,CAAAE,uBAAA,EAAAF,EAAA,CAAAG,uBAAA,EAAAH,EAAA,CAAAI,uBAAA,EACd3B,eAAe,EACfE,eAAe,EAAAqB,EAAA,CAAAK,kBAAA,EACf7B,eAAe,EAEfI,YAAY;MAAA0B,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}