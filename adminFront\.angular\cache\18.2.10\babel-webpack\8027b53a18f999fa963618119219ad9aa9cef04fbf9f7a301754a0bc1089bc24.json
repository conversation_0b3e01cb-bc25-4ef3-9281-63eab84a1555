{"ast": null, "code": "import { FooterComponent } from '../../components/footer/footer.component';\nimport { HeaderComponent } from '../../components/header/header.component';\nimport { NbLayoutModule, NbSidebarModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nconst _c0 = [[[\"nb-menu\"]], [[\"router-outlet\"]]];\nconst _c1 = [\"nb-menu\", \"router-outlet\"];\nexport class ThreeColumnsLayoutComponent {\n  static {\n    this.ɵfac = function ThreeColumnsLayoutComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ThreeColumnsLayoutComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ThreeColumnsLayoutComponent,\n      selectors: [[\"ngx-three-columns-layout\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 11,\n      vars: 0,\n      consts: [[\"windowMode\", \"\"], [\"fixed\", \"\"], [\"tag\", \"menu-sidebar\", \"responsive\", \"\", 1, \"menu-sidebar\"], [1, \"small\"]],\n      template: function ThreeColumnsLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"nb-layout\", 0)(1, \"nb-layout-header\", 1);\n          i0.ɵɵelement(2, \"ngx-header\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-sidebar\", 2);\n          i0.ɵɵprojection(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"nb-layout-column\", 3);\n          i0.ɵɵelementStart(6, \"nb-layout-column\");\n          i0.ɵɵprojection(7, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(8, \"nb-layout-column\", 3);\n          i0.ɵɵelementStart(9, \"nb-layout-footer\", 1);\n          i0.ɵɵelement(10, \"ngx-footer\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [NbLayoutModule, i1.NbLayoutComponent, i1.NbLayoutColumnComponent, i1.NbLayoutFooterComponent, i1.NbLayoutHeaderComponent, HeaderComponent, NbSidebarModule, i1.NbSidebarComponent, FooterComponent],\n      styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 297:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nUndefined variable.\\\\n   \\u2577\\\\n32 \\u2502 $gradient-success: linear-gradient(135deg, $success-light 0%, $success-base 100%);\\\\r\\\\n   \\u2502                                            ^^^^^^^^^^^^^^\\\\n   \\u2575\\\\n  src\\\\\\\\app\\\\\\\\@theme\\\\\\\\styles\\\\\\\\_colors.scss 32:44                            @import\\\\n  src\\\\\\\\app\\\\\\\\@theme\\\\\\\\styles\\\\\\\\themes.scss 6:9                               @import\\\\n  src\\\\\\\\app\\\\\\\\@theme\\\\\\\\layouts\\\\\\\\three-columns\\\\\\\\three-columns.layout.scss 1:9  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[297]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FooterComponent", "HeaderComponent", "NbLayoutModule", "NbSidebarModule", "ThreeColumnsLayoutComponent", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "ngContentSelectors", "_c1", "decls", "vars", "consts", "template", "ThreeColumnsLayoutComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵprojection", "i1", "NbLayoutComponent", "NbLayoutColumnComponent", "NbLayoutFooterComponent", "NbLayoutHeaderComponent", "NbSidebarComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\layouts\\three-columns\\three-columns.layout.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FooterComponent } from '../../components/footer/footer.component';\r\nimport { HeaderComponent } from '../../components/header/header.component';\r\nimport { NbLayoutModule, NbSidebarModule } from '@nebular/theme';\r\n\r\n@Component({\r\n    selector: 'ngx-three-columns-layout',\r\n    styleUrls: ['./three-columns.layout.scss'],\r\n    template: `\r\n    <nb-layout windowMode>\r\n      <nb-layout-header fixed>\r\n        <ngx-header></ngx-header>\r\n      </nb-layout-header>\r\n\r\n      <nb-sidebar class=\"menu-sidebar\" tag=\"menu-sidebar\" responsive>\r\n        <ng-content select=\"nb-menu\"></ng-content>\r\n      </nb-sidebar>\r\n\r\n      <nb-layout-column class=\"small\">\r\n      </nb-layout-column>\r\n\r\n      <nb-layout-column>\r\n        <ng-content select=\"router-outlet\"></ng-content>\r\n      </nb-layout-column>\r\n\r\n      <nb-layout-column class=\"small\">\r\n      </nb-layout-column>\r\n\r\n      <nb-layout-footer fixed>\r\n        <ngx-footer></ngx-footer>\r\n      </nb-layout-footer>\r\n    </nb-layout>\r\n  `,\r\n    standalone: true,\r\n    imports: [\r\n        NbLayoutModule,\r\n        HeaderComponent,\r\n        NbSidebarModule,\r\n        FooterComponent,\r\n    ],\r\n})\r\nexport class ThreeColumnsLayoutComponent {}\r\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,0CAA0C;AAC1E,SAASC,eAAe,QAAQ,0CAA0C;AAC1E,SAASC,cAAc,EAAEC,eAAe,QAAQ,gBAAgB;;;;;AAsChE,OAAM,MAAOC,2BAA2B;;;uCAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA,2BAA2B;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,kBAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UA/BlCT,EADF,CAAAW,cAAA,mBAAsB,0BACI;UACtBX,EAAA,CAAAY,SAAA,iBAAyB;UAC3BZ,EAAA,CAAAa,YAAA,EAAmB;UAEnBb,EAAA,CAAAW,cAAA,oBAA+D;UAC7DX,EAAA,CAAAc,YAAA,GAA0C;UAC5Cd,EAAA,CAAAa,YAAA,EAAa;UAEbb,EAAA,CAAAY,SAAA,0BACmB;UAEnBZ,EAAA,CAAAW,cAAA,uBAAkB;UAChBX,EAAA,CAAAc,YAAA,MAAgD;UAClDd,EAAA,CAAAa,YAAA,EAAmB;UAEnBb,EAAA,CAAAY,SAAA,0BACmB;UAEnBZ,EAAA,CAAAW,cAAA,0BAAwB;UACtBX,EAAA,CAAAY,SAAA,kBAAyB;UAE7BZ,EADE,CAAAa,YAAA,EAAmB,EACT;;;qBAIRnB,cAAc,EAAAqB,EAAA,CAAAC,iBAAA,EAAAD,EAAA,CAAAE,uBAAA,EAAAF,EAAA,CAAAG,uBAAA,EAAAH,EAAA,CAAAI,uBAAA,EACd1B,eAAe,EACfE,eAAe,EAAAoB,EAAA,CAAAK,kBAAA,EACf5B,eAAe;MAAA6B,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}