{"ast": null, "code": "import { RequestItemImportComponent } from './request-item-import.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nexport class RequestItemImportService {\n  constructor(dialogService) {\n    this.dialogService = dialogService;\n  }\n  /**\n   * 開啟需求項目匯入對話框\n   * @param config 配置選項\n   * @returns Observable 當用戶確認匯入時回傳配置，取消時回傳 null\n   */\n  openImportDialog(config) {\n    const dialogRef = this.dialogService.open(RequestItemImportComponent, {\n      context: {\n        buildCaseId: config?.buildCaseId || 0,\n        houseType: config?.houseType || []\n      },\n      closeOnBackdropClick: true,\n      closeOnEsc: true,\n      hasScroll: true,\n      autoFocus: false\n    });\n    // 監聽項目匯入事件\n    const itemsImported$ = dialogRef.componentRef.instance.itemsImported.asObservable();\n    // 當有項目匯入時，關閉對話框並回傳結果\n    itemsImported$.subscribe(config => {\n      dialogRef.close(config);\n    });\n    return dialogRef.onClose;\n  }\n  static {\n    this.ɵfac = function RequestItemImportService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequestItemImportService)(i0.ɵɵinject(i1.NbDialogService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RequestItemImportService,\n      factory: RequestItemImportService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["RequestItemImportComponent", "RequestItemImportService", "constructor", "dialogService", "openImportDialog", "config", "dialogRef", "open", "context", "buildCaseId", "houseType", "closeOnBackdropClick", "closeOnEsc", "hasScroll", "autoFocus", "itemsImported$", "componentRef", "instance", "itemsImported", "asObservable", "subscribe", "close", "onClose", "i0", "ɵɵinject", "i1", "NbDialogService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\request-item-import\\request-item-import.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { Observable } from 'rxjs';\r\nimport { RequestItemImportComponent, RequestItemImportConfig } from './request-item-import.component';\r\n\r\nexport interface RequestItemImportServiceConfig {\r\n  buildCaseId?: number;\r\n  houseType?: number[];\r\n  buttonText?: string;\r\n  buttonIcon?: string;\r\n  buttonClass?: string;\r\n  dialogTitle?: string;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class RequestItemImportService {\r\n\r\n  constructor(private dialogService: NbDialogService) { }\r\n\r\n  /**\r\n   * 開啟需求項目匯入對話框\r\n   * @param config 配置選項\r\n   * @returns Observable 當用戶確認匯入時回傳配置，取消時回傳 null\r\n   */\r\n  openImportDialog(config?: RequestItemImportServiceConfig): Observable<RequestItemImportConfig | null> {\r\n    const dialogRef = this.dialogService.open(RequestItemImportComponent, {\r\n      context: {\r\n        buildCaseId: config?.buildCaseId || 0,\r\n        houseType: config?.houseType || []\r\n      },\r\n      closeOnBackdropClick: true,\r\n      closeOnEsc: true,\r\n      hasScroll: true,\r\n      autoFocus: false\r\n    });\r\n\r\n    // 監聽項目匯入事件\r\n    const itemsImported$ = dialogRef.componentRef.instance.itemsImported.asObservable();\r\n\r\n    // 當有項目匯入時，關閉對話框並回傳結果\r\n    itemsImported$.subscribe((config: RequestItemImportConfig) => {\r\n      dialogRef.close(config);\r\n    });\r\n\r\n    return dialogRef.onClose;\r\n  }\r\n}\r\n"], "mappings": "AAGA,SAASA,0BAA0B,QAAiC,iCAAiC;;;AAcrG,OAAM,MAAOC,wBAAwB;EAEnCC,YAAoBC,aAA8B;IAA9B,KAAAA,aAAa,GAAbA,aAAa;EAAqB;EAEtD;;;;;EAKAC,gBAAgBA,CAACC,MAAuC;IACtD,MAAMC,SAAS,GAAG,IAAI,CAACH,aAAa,CAACI,IAAI,CAACP,0BAA0B,EAAE;MACpEQ,OAAO,EAAE;QACPC,WAAW,EAAEJ,MAAM,EAAEI,WAAW,IAAI,CAAC;QACrCC,SAAS,EAAEL,MAAM,EAAEK,SAAS,IAAI;OACjC;MACDC,oBAAoB,EAAE,IAAI;MAC1BC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE;KACZ,CAAC;IAEF;IACA,MAAMC,cAAc,GAAGT,SAAS,CAACU,YAAY,CAACC,QAAQ,CAACC,aAAa,CAACC,YAAY,EAAE;IAEnF;IACAJ,cAAc,CAACK,SAAS,CAAEf,MAA+B,IAAI;MAC3DC,SAAS,CAACe,KAAK,CAAChB,MAAM,CAAC;IACzB,CAAC,CAAC;IAEF,OAAOC,SAAS,CAACgB,OAAO;EAC1B;;;uCA9BWrB,wBAAwB,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;aAAxBzB,wBAAwB;MAAA0B,OAAA,EAAxB1B,wBAAwB,CAAA2B,IAAA;MAAAC,UAAA,EAFvB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}