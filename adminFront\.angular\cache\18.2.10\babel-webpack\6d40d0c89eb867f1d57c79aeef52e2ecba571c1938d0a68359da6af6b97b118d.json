{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule, NbIconModule, NbCheckboxModule, NbSelectModule, NbOptionModule, NbInputModule } from '@nebular/theme';\nimport { GetRequirement } from 'src/services/api/models';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services/requirement.service\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/enumHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nconst _c0 = (a0, a1, a2) => ({\n  \"active\": a0,\n  \"completed\": a1,\n  \"pending\": a2\n});\nfunction RequestItemImportComponent_div_12_nb_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r3.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r3.CBuildCaseName, \" \");\n  }\n}\nfunction RequestItemImportComponent_div_12_nb_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r4.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r4.label, \" \");\n  }\n}\nfunction RequestItemImportComponent_div_12_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43)(2, \"span\", 44);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 45);\n    i0.ɵɵtext(5, \"\\u8F09\\u5165\\u9700\\u6C42\\u9805\\u76EE\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RequestItemImportComponent_div_12_div_39_div_1_div_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const requirement_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5099\\u8A3B: \", requirement_r6.CRemark, \" \");\n  }\n}\nfunction RequestItemImportComponent_div_12_div_39_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"nb-checkbox\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestItemImportComponent_div_12_div_39_div_1_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const requirement_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      i0.ɵɵtwoWayBindingSet(requirement_r6.selected, $event) || (requirement_r6.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function RequestItemImportComponent_div_12_div_39_div_1_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onRequirementItemChange());\n    });\n    i0.ɵɵelementStart(2, \"div\", 51)(3, \"div\", 52);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 53);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 54);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 55);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, RequestItemImportComponent_div_12_div_39_div_1_div_1_div_12_Template, 2, 1, \"div\", 56);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const requirement_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", requirement_r6.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(requirement_r6.CRequirement || \"\\u672A\\u547D\\u540D\\u9700\\u6C42\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u4F4D\\u7F6E: \", requirement_r6.CLocation || \"\\u672A\\u6307\\u5B9A\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u55AE\\u4F4D: \", requirement_r6.CUnit || \"\\u672A\\u6307\\u5B9A\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u55AE\\u50F9: \", i0.ɵɵpipeBind4(11, 6, requirement_r6.CUnitPrice, \"TWD\", \"symbol-narrow\", \"1.0-0\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", requirement_r6.CRemark);\n  }\n}\nfunction RequestItemImportComponent_div_12_div_39_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, RequestItemImportComponent_div_12_div_39_div_1_div_1_Template, 13, 11, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.requirements);\n  }\n}\nfunction RequestItemImportComponent_div_12_div_39_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵelement(1, \"nb-icon\", 59);\n    i0.ɵɵtext(2, \" \\u6C92\\u6709\\u53EF\\u532F\\u5165\\u7684\\u9700\\u6C42\\u9805\\u76EE\\uFF0C\\u8ACB\\u8ABF\\u6574\\u641C\\u5C0B\\u689D\\u4EF6\\u6216\\u806F\\u7E6B\\u7BA1\\u7406\\u54E1 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequestItemImportComponent_div_12_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtemplate(1, RequestItemImportComponent_div_12_div_39_div_1_Template, 2, 1, \"div\", 47)(2, RequestItemImportComponent_div_12_div_39_ng_template_2_Template, 3, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const noRequirements_r7 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.requirements.length > 0)(\"ngIfElse\", noRequirements_r7);\n  }\n}\nfunction RequestItemImportComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"div\", 18);\n    i0.ɵɵelement(3, \"nb-icon\", 19);\n    i0.ɵɵtext(4, \"\\u641C\\u5C0B\\u689D\\u4EF6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 20)(6, \"div\", 21)(7, \"div\", 22)(8, \"label\", 23);\n    i0.ɵɵtext(9, \"\\u5EFA\\u6848\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nb-select\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestItemImportComponent_div_12_Template_nb_select_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchRequest.CBuildCaseID, $event) || (ctx_r1.searchRequest.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function RequestItemImportComponent_div_12_Template_nb_select_ngModelChange_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBuildCaseChange());\n    });\n    i0.ɵɵtemplate(11, RequestItemImportComponent_div_12_nb_option_11_Template, 2, 2, \"nb-option\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 22)(13, \"label\", 26);\n    i0.ɵɵtext(14, \"\\u623F\\u5C4B\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"nb-select\", 27);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestItemImportComponent_div_12_Template_nb_select_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchRequest.CHouseType, $event) || (ctx_r1.searchRequest.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(16, RequestItemImportComponent_div_12_nb_option_16_Template, 2, 2, \"nb-option\", 25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 21)(18, \"div\", 22)(19, \"label\", 28);\n    i0.ɵɵtext(20, \"\\u5340\\u57DF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestItemImportComponent_div_12_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchRequest.CLocation, $event) || (ctx_r1.searchRequest.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 22)(23, \"label\", 30);\n    i0.ɵɵtext(24, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"input\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestItemImportComponent_div_12_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchRequest.CRequirement, $event) || (ctx_r1.searchRequest.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 21)(27, \"div\", 32)(28, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_div_12_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resetSearch());\n    });\n    i0.ɵɵelement(29, \"nb-icon\", 34);\n    i0.ɵɵtext(30, \" \\u91CD\\u7F6E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_div_12_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearch());\n    });\n    i0.ɵɵelement(32, \"nb-icon\", 36);\n    i0.ɵɵtext(33, \" \\u641C\\u5C0B \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(34, \"div\", 37)(35, \"div\", 18);\n    i0.ɵɵelement(36, \"nb-icon\", 38);\n    i0.ɵɵtext(37, \"\\u9078\\u64C7\\u9700\\u6C42\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(38, RequestItemImportComponent_div_12_div_38_Template, 6, 0, \"div\", 39)(39, RequestItemImportComponent_div_12_div_39_Template, 4, 2, \"div\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchRequest.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.buildCaseList);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchRequest.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.houseTypeOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchRequest.CLocation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchRequest.CRequirement);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction RequestItemImportComponent_div_13_div_12_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5099\\u8A3B: \", item_r8.CRemark, \" \");\n  }\n}\nfunction RequestItemImportComponent_div_13_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 67)(2, \"h6\", 68);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 69)(5, \"div\", 70);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 71);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 72);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, RequestItemImportComponent_div_13_div_12_div_12_Template, 2, 1, \"div\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", i_r9 + 1, \". \", item_r8.CRequirement || \"\\u672A\\u547D\\u540D\\u9700\\u6C42\", \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u4F4D\\u7F6E: \", item_r8.CLocation || \"\\u672A\\u6307\\u5B9A\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u55AE\\u4F4D: \", item_r8.CUnit || \"\\u672A\\u6307\\u5B9A\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u55AE\\u50F9: \", i0.ɵɵpipeBind4(11, 6, item_r8.CUnitPrice, \"TWD\", \"symbol-narrow\", \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r8.CRemark);\n  }\n}\nfunction RequestItemImportComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 60)(2, \"div\", 18);\n    i0.ɵɵelement(3, \"nb-icon\", 61);\n    i0.ɵɵtext(4, \"\\u78BA\\u8A8D\\u532F\\u5165\\u8A73\\u60C5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 62)(6, \"div\", 63);\n    i0.ɵɵtext(7, \" \\u5C07\\u532F\\u5165 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" \\u500B\\u9700\\u6C42\\u9805\\u76EE\\u5230\\u76EE\\u524D\\u7684\\u5EFA\\u6848\\u4E2D \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 64);\n    i0.ɵɵtemplate(12, RequestItemImportComponent_div_13_div_12_Template, 13, 11, \"div\", 65);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.getSelectedCount());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getSelectedItems());\n  }\n}\nfunction RequestItemImportComponent_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStep());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 75);\n    i0.ɵɵtext(2, \" \\u4E0A\\u4E00\\u6B65 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequestItemImportComponent_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(1, \" \\u4E0B\\u4E00\\u6B65 \");\n    i0.ɵɵelement(2, \"nb-icon\", 77);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canProceed());\n  }\n}\nfunction RequestItemImportComponent_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function RequestItemImportComponent_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.importRequirements());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 79);\n    i0.ɵɵtext(2, \" \\u78BA\\u8A8D\\u532F\\u5165 \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class RequestItemImportComponent {\n  constructor(requirementService, dialogRef, enumHelper, buildCaseService) {\n    this.requirementService = requirementService;\n    this.dialogRef = dialogRef;\n    this.enumHelper = enumHelper;\n    this.buildCaseService = buildCaseService;\n    this.buildCaseId = 0;\n    this.itemsImported = new EventEmitter();\n    this.currentStep = 1;\n    this.requirements = [];\n    this.loading = false;\n    // 搜尋相關屬性\n    this.searchRequest = {};\n    this.buildCaseList = [];\n    this.houseTypeOptions = [];\n  }\n  ngOnInit() {\n    this.initializeSearchForm();\n    this.getBuildCaseList();\n  }\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    this.houseTypeOptions = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.searchRequest.CStatus = 1; // 預設只顯示啟用的項目\n    this.searchRequest.CIsShow = null; // 預設只顯示需要顯示的項目\n    this.searchRequest.CIsSimple = null;\n    this.searchRequest.CRequirement = '';\n    this.searchRequest.CLocation = '';\n    // 預設全選所有房屋類型\n    this.searchRequest.CHouseType = this.houseTypeOptions.map(type => type.value);\n  }\n  // 取得建案列表\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).subscribe({\n      next: res => {\n        if (res.StatusCode === 0 && res.Entries) {\n          this.buildCaseList = res.Entries;\n          // 如果有傳入建案ID，使用傳入的ID；否則使用第一個建案\n          if (this.buildCaseId && this.buildCaseId > 0) {\n            this.searchRequest.CBuildCaseID = this.buildCaseId;\n          } else if (this.buildCaseList.length > 0) {\n            this.searchRequest.CBuildCaseID = this.buildCaseList[0].cID;\n          }\n          // 載入需求項目\n          if (this.searchRequest.CBuildCaseID) {\n            this.loadRequirementsFromAPI();\n          }\n        }\n      },\n      error: error => {\n        console.error('載入建案列表失敗:', error);\n      }\n    });\n  }\n  // 建案切換事件\n  onBuildCaseChange() {\n    this.loadRequirementsFromAPI();\n  }\n  // 搜尋事件\n  onSearch() {\n    this.loadRequirementsFromAPI();\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\n      this.searchRequest.CBuildCaseID = this.buildCaseList[0].cID;\n    }\n    this.loadRequirementsFromAPI();\n  }\n  loadRequirementsFromAPI() {\n    if (!this.searchRequest.CBuildCaseID) {\n      return;\n    }\n    this.loading = true;\n    // 準備API請求參數\n    const getRequirementListArgs = {\n      CBuildCaseID: this.searchRequest.CBuildCaseID,\n      CHouseType: this.searchRequest.CHouseType,\n      CLocation: this.searchRequest.CLocation || null,\n      CRequirement: this.searchRequest.CRequirement || null,\n      CStatus: this.searchRequest.CStatus,\n      CIsShow: this.searchRequest.CIsShow,\n      CIsSimple: this.searchRequest.CIsSimple,\n      PageIndex: 1,\n      PageSize: 100\n    };\n    this.requirementService.apiRequirementGetRequestListForTemplatePost$Json({\n      body: getRequirementListArgs\n    }).subscribe({\n      next: response => {\n        this.loading = false;\n        if (response.StatusCode === 0 && response.Entries) {\n          this.requirements = response.Entries.map(item => ({\n            ...item,\n            selected: false\n          }));\n        } else {\n          this.requirements = [];\n        }\n      },\n      error: error => {\n        this.loading = false;\n        this.requirements = [];\n      }\n    });\n  }\n  onRequirementItemChange() {\n    // 當需求項目選擇變更時的處理\n  }\n  getSelectedItems() {\n    return this.requirements.filter(item => item.selected);\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.getSelectedItems().length > 0;\n      case 2:\n        return true;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (this.canProceed() && this.currentStep < 2) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  getProgressText() {\n    const progressTexts = {\n      1: '請選擇要匯入的需求項目',\n      2: '確認匯入詳情'\n    };\n    return progressTexts[this.currentStep] || '';\n  }\n  importRequirements() {\n    const selectedBuildCase = this.buildCaseList.find(bc => bc.cID === this.searchRequest.CBuildCaseID);\n    const config = {\n      buildCaseId: this.searchRequest.CBuildCaseID || 0,\n      buildCaseName: selectedBuildCase?.CBuildCaseName || '',\n      selectedItems: this.getSelectedItems(),\n      totalItems: this.getSelectedItems().length,\n      searchCriteria: {\n        CHouseType: this.searchRequest.CHouseType || undefined,\n        CLocation: this.searchRequest.CLocation || undefined,\n        CRequirement: this.searchRequest.CRequirement || undefined\n      }\n    };\n    this.itemsImported.emit(config);\n    this.close();\n  }\n  close() {\n    this.resetSelections();\n    this.dialogRef.close();\n  }\n  resetSelections() {\n    this.currentStep = 1;\n    this.requirements.forEach(requirement => {\n      requirement.selected = false;\n    });\n  }\n  selectAll() {\n    const allSelected = this.requirements.every(item => item.selected);\n    this.requirements.forEach(item => {\n      item.selected = !allSelected;\n    });\n  }\n  getSelectedCount() {\n    return this.getSelectedItems().length;\n  }\n  getTotalCount() {\n    return this.requirements.length;\n  }\n  static {\n    this.ɵfac = function RequestItemImportComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequestItemImportComponent)(i0.ɵɵdirectiveInject(i1.RequirementService), i0.ɵɵdirectiveInject(i2.NbDialogRef), i0.ɵɵdirectiveInject(i3.EnumHelper), i0.ɵɵdirectiveInject(i4.BuildCaseService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequestItemImportComponent,\n      selectors: [[\"app-request-item-import\"]],\n      inputs: {\n        buildCaseId: \"buildCaseId\"\n      },\n      outputs: {\n        itemsImported: \"itemsImported\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 21,\n      vars: 15,\n      consts: [[\"noRequirements\", \"\"], [1, \"request-item-import-dialog\"], [1, \"request-item-import-header\"], [1, \"request-item-import-title\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"close-btn\", 3, \"click\"], [\"icon\", \"close-outline\"], [1, \"request-item-import-body\"], [1, \"step-nav\"], [1, \"step-item\", 3, \"ngClass\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"request-item-import-footer\"], [1, \"step-buttons\"], [\"nbButton\", \"\", \"ghost\", \"\", \"class\", \"mr-2\", 3, \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"ghost\", \"\", 1, \"mr-2\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"click\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"search-section\", \"mb-4\"], [1, \"section-title\"], [\"icon\", \"search-outline\", 1, \"mr-2\"], [1, \"search-form\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-6\"], [\"for\", \"buildCase\", 1, \"label\", \"mb-2\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"houseType\", 1, \"label\", \"mb-2\"], [\"multiple\", \"\", \"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u623F\\u5C4B\\u985E\\u578B\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"location\", 1, \"label\", \"mb-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"location\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5340\\u57DF\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"requirement\", 1, \"label\", \"mb-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"requirement\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"text-right\"], [1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [\"icon\", \"refresh-outline\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [\"icon\", \"search-outline\"], [1, \"requirement-selection\"], [\"icon\", \"layers-outline\", 1, \"mr-2\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"requirement-list\", 4, \"ngIf\"], [3, \"value\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"sr-only\"], [1, \"mt-2\"], [1, \"requirement-list\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"requirement-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"requirement-item\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"requirement-info\"], [1, \"item-name\"], [1, \"item-code\"], [1, \"item-status\"], [1, \"item-type\"], [\"class\", \"item-remark\", 4, \"ngIf\"], [1, \"item-remark\"], [1, \"no-requirements\"], [\"icon\", \"info-outline\", 1, \"mr-2\"], [1, \"confirmation-area\"], [\"icon\", \"checkmark-circle-outline\", 1, \"mr-2\"], [1, \"selected-summary\"], [1, \"summary-text\"], [1, \"selected-requirements-details\"], [\"class\", \"requirement-detail-section\", 4, \"ngFor\", \"ngForOf\"], [1, \"requirement-detail-section\"], [1, \"requirement-detail-header\"], [1, \"requirement-name\"], [1, \"requirement-meta\"], [1, \"requirement-location\"], [1, \"requirement-unit\"], [1, \"requirement-price\"], [\"class\", \"requirement-remark\", 4, \"ngIf\"], [1, \"requirement-remark\"], [\"icon\", \"arrow-back-outline\", 1, \"mr-1\"], [\"nbButton\", \"\", \"status\", \"primary\", 3, \"click\", \"disabled\"], [\"icon\", \"arrow-forward-outline\", 1, \"mr-1\"], [\"nbButton\", \"\", \"status\", \"success\", 3, \"click\"], [\"icon\", \"download-outline\", 1, \"mr-1\"]],\n      template: function RequestItemImportComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\", 2)(2, \"div\", 3);\n          i0.ɵɵtext(3, \"\\u9700\\u6C42\\u9805\\u76EE\\u532F\\u5165\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function RequestItemImportComponent_Template_button_click_4_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵelement(5, \"nb-icon\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"nb-card-body\", 6)(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵtext(9, \"1. \\u9078\\u64C7\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 8);\n          i0.ɵɵtext(11, \"2. \\u78BA\\u8A8D\\u532F\\u5165\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, RequestItemImportComponent_div_12_Template, 40, 8, \"div\", 9)(13, RequestItemImportComponent_div_13_Template, 13, 2, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"nb-card-footer\", 10)(15, \"div\", 11);\n          i0.ɵɵtemplate(16, RequestItemImportComponent_button_16_Template, 3, 0, \"button\", 12);\n          i0.ɵɵelementStart(17, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function RequestItemImportComponent_Template_button_click_17_listener() {\n            return ctx.close();\n          });\n          i0.ɵɵtext(18, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, RequestItemImportComponent_button_19_Template, 3, 1, \"button\", 14)(20, RequestItemImportComponent_button_20_Template, 3, 0, \"button\", 15);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(7, _c0, ctx.currentStep === 1, ctx.currentStep > 1, ctx.currentStep < 1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(11, _c0, ctx.currentStep === 2, ctx.currentStep > 2, ctx.currentStep < 2));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        }\n      },\n      dependencies: [CommonModule, i5.NgClass, i5.NgForOf, i5.NgIf, i5.CurrencyPipe, FormsModule, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, NbCardModule, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, NbButtonModule, i2.NbButtonComponent, NbIconModule, i2.NbIconComponent, NbCheckboxModule, i2.NbCheckboxComponent, NbSelectModule, i2.NbSelectComponent, i2.NbOptionComponent, NbOptionModule, NbInputModule, i2.NbInputDirective],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.25em 0.4em;\\n  font-size: 75%;\\n  font-weight: 700;\\n  line-height: 1;\\n  text-align: center;\\n  white-space: nowrap;\\n  vertical-align: baseline;\\n  border-radius: 0.25rem;\\n}\\n.badge.badge-primary[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #007bff;\\n}\\n.badge.badge-success[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #28a745;\\n}\\n.badge.badge-info[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #17a2b8;\\n}\\n.badge.badge-secondary[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #6c757d;\\n}\\n\\n\\n\\n.request-item-import-dialog[_ngcontent-%COMP%] {\\n  min-width: 600px;\\n  max-width: 800px;\\n  min-height: 500px;\\n  max-height: 80vh;\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1.25rem 1.5rem;\\n  border-bottom: 1px solid var(--nb-color-basic-300);\\n  background-color: var(--nb-color-basic-100);\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .request-item-import-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--nb-color-text-basic);\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  padding: 0.25rem;\\n  min-width: auto;\\n  border: none;\\n  background: transparent;\\n  color: var(--nb-color-text-hint);\\n  transition: all 0.3s ease;\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  color: var(--nb-color-text-basic);\\n  background-color: var(--nb-color-basic-200);\\n  border-radius: 0.25rem;\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n.request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  overflow-y: auto;\\n  max-height: 60vh;\\n  background-color: var(--nb-color-basic-100);\\n}\\n\\n\\n\\n.step-content[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n  \\n\\n  \\n\\n  \\n\\n}\\n.step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: var(--nb-color-text-basic);\\n  margin-bottom: 1rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: var(--nb-color-primary-500);\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%] {\\n  background-color: var(--nb-color-basic-200);\\n  padding: 1.5rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid var(--nb-color-basic-300);\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--nb-color-text-basic);\\n  display: block;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%], \\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  min-width: 80px;\\n}\\n.step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  border: 1px solid var(--nb-color-basic-300);\\n  border-radius: 0.375rem;\\n  transition: all 0.3s ease;\\n  background-color: var(--nb-color-basic-100);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:hover {\\n  border-color: var(--nb-color-primary-300);\\n  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.15);\\n  background-color: var(--nb-color-basic-200);\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n  flex: 1;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--nb-color-text-basic);\\n  margin-bottom: 0.25rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--nb-color-text-hint);\\n  margin-bottom: 0.25rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--nb-color-text-hint);\\n  margin-bottom: 0.25rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--nb-color-text-hint);\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-remark[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--nb-color-info-600);\\n  font-style: italic;\\n  margin-top: 0.25rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .no-requirements[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  color: var(--nb-color-text-hint);\\n  background-color: var(--nb-color-basic-200);\\n  border-radius: 0.375rem;\\n  border: 1px solid var(--nb-color-basic-300);\\n}\\n.step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .no-requirements[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: var(--nb-color-info-500);\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--nb-color-primary-100) 0%, var(--nb-color-primary-50) 100%);\\n  border: 1px solid var(--nb-color-primary-300);\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%] {\\n  color: var(--nb-color-text-basic);\\n  font-weight: 500;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%]   .summary-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--nb-color-primary-600);\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%] {\\n  border: 1px solid var(--nb-color-basic-300);\\n  border-radius: 0.375rem;\\n  margin-bottom: 1rem;\\n  overflow: hidden;\\n  background-color: var(--nb-color-basic-100);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--nb-color-primary-100) 0%, var(--nb-color-primary-50) 100%);\\n  padding: 1rem;\\n  border-bottom: 1px solid var(--nb-color-basic-300);\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-name[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: var(--nb-color-text-basic);\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  flex-wrap: wrap;\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-meta[_ngcontent-%COMP%]   .requirement-location[_ngcontent-%COMP%], \\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-meta[_ngcontent-%COMP%]   .requirement-unit[_ngcontent-%COMP%], \\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-meta[_ngcontent-%COMP%]   .requirement-price[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--nb-color-text-hint);\\n}\\n.step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%]   .requirement-remark[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--nb-color-info-600);\\n  font-style: italic;\\n  margin-top: 0.5rem;\\n}\\n\\n\\n\\n.step-nav[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  justify-content: center !important;\\n  margin-bottom: 2rem !important;\\n  border-bottom: 1px solid #e4e9f2 !important;\\n  padding-bottom: 1rem !important;\\n  background: transparent !important;\\n}\\n.step-nav[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1.5rem !important;\\n  margin: 0 0.5rem !important;\\n  border-radius: 0.375rem !important;\\n  font-weight: 500 !important;\\n  font-size: 0.9rem !important;\\n  transition: all 0.3s ease !important;\\n  cursor: default !important;\\n  display: inline-block !important;\\n  text-align: center !important;\\n  min-width: 120px !important;\\n}\\n.step-nav[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;\\n  color: white !important;\\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3) !important;\\n  border: none !important;\\n}\\n.step-nav[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%] {\\n  background-color: #28a745 !important;\\n  color: white !important;\\n  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2) !important;\\n  border: none !important;\\n}\\n.step-nav[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa !important;\\n  color: #6c757d !important;\\n  border: 1px solid #dee2e6 !important;\\n}\\n\\n\\n\\n.request-item-import-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  align-items: center;\\n  padding: 1rem 1.5rem;\\n  border-top: 1px solid var(--nb-color-basic-300);\\n  background-color: var(--nb-color-basic-100);\\n}\\n.request-item-import-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .request-item-import-dialog[_ngcontent-%COMP%] {\\n    min-width: 95vw;\\n    max-width: 95vw;\\n    margin: 0.5rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n    padding: 0.4rem 0.8rem;\\n    margin: 0 0.25rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n    margin-bottom: 0.75rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], \\n   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], \\n   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%], \\n   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-remark[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-footer[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n    align-items: stretch;\\n  }\\n  .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-footer[_ngcontent-%COMP%]   .step-buttons[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n\\n\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%] {\\n  background-color: var(--nb-color-basic-800);\\n  border-bottom-color: var(--nb-color-basic-600);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .request-item-import-title[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .request-item-import-title[_ngcontent-%COMP%] {\\n  color: var(--nb-color-text-basic);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  color: var(--nb-color-text-hint);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover, .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-header[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  color: var(--nb-color-text-basic);\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%] {\\n  background-color: var(--nb-color-basic-800);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%] {\\n  border-bottom-color: #495057 !important;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%) !important;\\n  color: white !important;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.completed[_ngcontent-%COMP%] {\\n  background-color: #198754 !important;\\n  color: white !important;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-nav[_ngcontent-%COMP%]   .step-item.pending[_ngcontent-%COMP%] {\\n  background-color: #343a40 !important;\\n  color: #adb5bd !important;\\n  border-color: #495057 !important;\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  color: var(--nb-color-text-basic);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-form[_ngcontent-%COMP%] {\\n  background-color: var(--nb-color-basic-700);\\n  border-color: var(--nb-color-basic-600);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%] {\\n  background-color: var(--nb-color-basic-700);\\n  border-color: var(--nb-color-basic-600);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:hover, .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  color: var(--nb-color-text-basic);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%], \\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%], \\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .requirement-item[_ngcontent-%COMP%]   .requirement-info[_ngcontent-%COMP%]   .item-type[_ngcontent-%COMP%] {\\n  color: var(--nb-color-text-hint);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .no-requirements[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .requirement-selection[_ngcontent-%COMP%]   .requirement-list[_ngcontent-%COMP%]   .no-requirements[_ngcontent-%COMP%] {\\n  background-color: var(--nb-color-basic-700);\\n  border-color: var(--nb-color-basic-600);\\n  color: var(--nb-color-text-hint);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-summary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--nb-color-primary-800) 0%, var(--nb-color-primary-900) 100%);\\n  border-color: var(--nb-color-primary-600);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%] {\\n  background-color: var(--nb-color-basic-700);\\n  border-color: var(--nb-color-basic-600);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-body[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .confirmation-area[_ngcontent-%COMP%]   .selected-requirements-details[_ngcontent-%COMP%]   .requirement-detail-section[_ngcontent-%COMP%]   .requirement-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--nb-color-primary-800) 0%, var(--nb-color-primary-900) 100%);\\n  border-bottom-color: var(--nb-color-basic-600);\\n}\\n.nb-theme-dark[_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-footer[_ngcontent-%COMP%], .nb-theme-dark   [_nghost-%COMP%]   .request-item-import-dialog[_ngcontent-%COMP%]   .request-item-import-footer[_ngcontent-%COMP%] {\\n  background-color: var(--nb-color-basic-800);\\n  border-top-color: var(--nb-color-basic-600);\\n}\\n\\n\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\n\\n\\nnb-checkbox[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  width: 100%;\\n}\\nnb-checkbox[_ngcontent-%COMP%]   .customised-control-input[_ngcontent-%COMP%] {\\n  margin-right: 0.75rem;\\n  margin-top: 0.125rem;\\n}\\n\\n\\n\\n.mr-1[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.mr-2[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "NbIconModule", "NbCheckboxModule", "NbSelectModule", "NbOptionModule", "NbInputModule", "GetRequirement", "EnumHouseType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r3", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "type_r4", "value", "label", "requirement_r6", "CRemark", "ɵɵtwoWayListener", "RequestItemImportComponent_div_12_div_39_div_1_div_1_Template_nb_checkbox_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r5", "$implicit", "ɵɵtwoWayBindingSet", "selected", "ɵɵresetView", "ɵɵlistener", "ctx_r1", "ɵɵnextContext", "onRequirementItemChange", "ɵɵtemplate", "RequestItemImportComponent_div_12_div_39_div_1_div_1_div_12_Template", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "CRequirement", "CLocation", "CUnit", "ɵɵpipeBind4", "CUnitPrice", "RequestItemImportComponent_div_12_div_39_div_1_div_1_Template", "requirements", "ɵɵelement", "RequestItemImportComponent_div_12_div_39_div_1_Template", "RequestItemImportComponent_div_12_div_39_ng_template_2_Template", "ɵɵtemplateRefExtractor", "length", "noRequirements_r7", "RequestItemImportComponent_div_12_Template_nb_select_ngModelChange_10_listener", "_r1", "searchRequest", "CBuildCaseID", "onBuildCaseChange", "RequestItemImportComponent_div_12_nb_option_11_Template", "RequestItemImportComponent_div_12_Template_nb_select_ngModelChange_15_listener", "CHouseType", "RequestItemImportComponent_div_12_nb_option_16_Template", "RequestItemImportComponent_div_12_Template_input_ngModelChange_21_listener", "RequestItemImportComponent_div_12_Template_input_ngModelChange_25_listener", "RequestItemImportComponent_div_12_Template_button_click_28_listener", "resetSearch", "RequestItemImportComponent_div_12_Template_button_click_31_listener", "onSearch", "RequestItemImportComponent_div_12_div_38_Template", "RequestItemImportComponent_div_12_div_39_Template", "buildCaseList", "houseTypeOptions", "loading", "item_r8", "RequestItemImportComponent_div_13_div_12_div_12_Template", "ɵɵtextInterpolate2", "i_r9", "RequestItemImportComponent_div_13_div_12_Template", "getSelectedCount", "getSelectedItems", "RequestItemImportComponent_button_16_Template_button_click_0_listener", "_r10", "previousStep", "RequestItemImportComponent_button_19_Template_button_click_0_listener", "_r11", "nextStep", "canProceed", "RequestItemImportComponent_button_20_Template_button_click_0_listener", "_r12", "importRequirements", "RequestItemImportComponent", "constructor", "requirementService", "dialogRef", "enum<PERSON>elper", "buildCaseService", "buildCaseId", "itemsImported", "currentStep", "ngOnInit", "initializeSearchForm", "getBuildCaseList", "getEnumOptions", "CStatus", "CIsShow", "CIsSimple", "map", "type", "apiBuildCaseGetUserBuildCasePost$Json", "body", "subscribe", "next", "res", "StatusCode", "Entries", "loadRequirementsFromAPI", "error", "console", "getRequirementListArgs", "PageIndex", "PageSize", "apiRequirementGetRequestListForTemplatePost$Json", "response", "item", "filter", "getProgressText", "progressTexts", "selectedBuildCase", "find", "bc", "config", "buildCaseName", "selectedItems", "totalItems", "searchCriteria", "undefined", "emit", "close", "resetSelections", "for<PERSON>ach", "requirement", "selectAll", "allSelected", "every", "getTotalCount", "ɵɵdirectiveInject", "i1", "RequirementService", "i2", "NbDialogRef", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i4", "BuildCaseService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RequestItemImportComponent_Template", "rf", "ctx", "RequestItemImportComponent_Template_button_click_4_listener", "RequestItemImportComponent_div_12_Template", "RequestItemImportComponent_div_13_Template", "RequestItemImportComponent_button_16_Template", "RequestItemImportComponent_Template_button_click_17_listener", "RequestItemImportComponent_button_19_Template", "RequestItemImportComponent_button_20_Template", "ɵɵpureFunction3", "_c0", "i5", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "C<PERSON><PERSON>cyPipe", "i6", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbButtonComponent", "NbIconComponent", "NbCheckboxComponent", "NbSelectComponent", "NbOptionComponent", "NbInputDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\request-item-import\\request-item-import.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\request-item-import\\request-item-import.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport {\r\n  NbCardModule,\r\n  NbButtonModule,\r\n  NbIconModule,\r\n  NbCheckboxModule,\r\n  NbSelectModule,\r\n  NbOptionModule,\r\n  NbInputModule,\r\n  NbDialogRef\r\n} from '@nebular/theme';\r\nimport { RequirementService } from 'src/services/api/services/requirement.service';\r\nimport { BuildCaseService } from 'src/services/api/services';\r\nimport { GetListRequirementRequest, GetRequirement, GetRequirementListResponseBase, BuildCaseGetListReponse } from 'src/services/api/models';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\n\r\nexport interface ExtendedRequirementItem extends GetRequirement {\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface RequestItemImportConfig {\r\n  buildCaseId: number;\r\n  buildCaseName?: string;\r\n  selectedItems: ExtendedRequirementItem[];\r\n  totalItems: number;\r\n  searchCriteria?: {\r\n    CHouseType?: number[];\r\n    CLocation?: string;\r\n    CRequirement?: string;\r\n  };\r\n}\r\n\r\n@Component({\r\n  selector: 'app-request-item-import',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbIconModule,\r\n    NbCheckboxModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NbInputModule\r\n  ],\r\n  templateUrl: './request-item-import.component.html',\r\n  styleUrls: ['./request-item-import.component.scss']\r\n})\r\nexport class RequestItemImportComponent implements OnInit {\r\n  @Input() buildCaseId: number = 0;\r\n  @Output() itemsImported = new EventEmitter<RequestItemImportConfig>();\r\n\r\n  currentStep: number = 1;\r\n  requirements: ExtendedRequirementItem[] = [];\r\n  loading: boolean = false;\r\n\r\n  // 搜尋相關屬性\r\n  searchRequest: GetListRequirementRequest & { CIsShow?: boolean | null; CIsSimple?: boolean | null } = {};\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  houseTypeOptions: any[] = [];\r\n\r\n  constructor(\r\n    private requirementService: RequirementService,\r\n    private dialogRef: NbDialogRef<RequestItemImportComponent>,\r\n    private enumHelper: EnumHelper,\r\n    private buildCaseService: BuildCaseService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.initializeSearchForm();\r\n    this.getBuildCaseList();\r\n  }\r\n\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    this.houseTypeOptions = this.enumHelper.getEnumOptions(EnumHouseType);\r\n    this.searchRequest.CStatus = 1; // 預設只顯示啟用的項目\r\n    this.searchRequest.CIsShow = null; // 預設只顯示需要顯示的項目\r\n    this.searchRequest.CIsSimple = null;\r\n    this.searchRequest.CRequirement = '';\r\n    this.searchRequest.CLocation = '';\r\n    // 預設全選所有房屋類型\r\n    this.searchRequest.CHouseType = this.houseTypeOptions.map(type => type.value);\r\n  }\r\n\r\n  // 取得建案列表\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .subscribe({\r\n        next: (res) => {\r\n          if (res.StatusCode === 0 && res.Entries) {\r\n            this.buildCaseList = res.Entries;\r\n            // 如果有傳入建案ID，使用傳入的ID；否則使用第一個建案\r\n            if (this.buildCaseId && this.buildCaseId > 0) {\r\n              this.searchRequest.CBuildCaseID = this.buildCaseId;\r\n            } else if (this.buildCaseList.length > 0) {\r\n              this.searchRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n            }\r\n            // 載入需求項目\r\n            if (this.searchRequest.CBuildCaseID) {\r\n              this.loadRequirementsFromAPI();\r\n            }\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('載入建案列表失敗:', error);\r\n        }\r\n      });\r\n  }\r\n\r\n  // 建案切換事件\r\n  onBuildCaseChange() {\r\n    this.loadRequirementsFromAPI();\r\n  }\r\n\r\n  // 搜尋事件\r\n  onSearch() {\r\n    this.loadRequirementsFromAPI();\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      this.searchRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n    }\r\n    this.loadRequirementsFromAPI();\r\n  }\r\n\r\n  loadRequirementsFromAPI() {\r\n    if (!this.searchRequest.CBuildCaseID) {\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n\r\n    // 準備API請求參數\r\n    const getRequirementListArgs: GetListRequirementRequest = {\r\n      CBuildCaseID: this.searchRequest.CBuildCaseID,\r\n      CHouseType: this.searchRequest.CHouseType,\r\n      CLocation: this.searchRequest.CLocation || null,\r\n      CRequirement: this.searchRequest.CRequirement || null,\r\n      CStatus: this.searchRequest.CStatus,\r\n      CIsShow: this.searchRequest.CIsShow,\r\n      CIsSimple: this.searchRequest.CIsSimple,\r\n      PageIndex: 1,\r\n      PageSize: 100\r\n    };\r\n\r\n    this.requirementService.apiRequirementGetRequestListForTemplatePost$Json({\r\n      body: getRequirementListArgs\r\n    }).subscribe({\r\n      next: (response: GetRequirementListResponseBase) => {\r\n        this.loading = false;\r\n        if (response.StatusCode === 0 && response.Entries) {\r\n          this.requirements = response.Entries.map(item => ({\r\n            ...item,\r\n            selected: false\r\n          }));\r\n        } else {\r\n          this.requirements = [];\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.loading = false;\r\n        this.requirements = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  onRequirementItemChange() {\r\n    // 當需求項目選擇變更時的處理\r\n  }\r\n\r\n  getSelectedItems(): ExtendedRequirementItem[] {\r\n    return this.requirements.filter(item => item.selected);\r\n  }\r\n\r\n  canProceed(): boolean {\r\n    switch (this.currentStep) {\r\n      case 1:\r\n        return this.getSelectedItems().length > 0;\r\n      case 2:\r\n        return true;\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  nextStep() {\r\n    if (this.canProceed() && this.currentStep < 2) {\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  previousStep() {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  getProgressText(): string {\r\n    const progressTexts = {\r\n      1: '請選擇要匯入的需求項目',\r\n      2: '確認匯入詳情'\r\n    };\r\n    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';\r\n  }\r\n\r\n  importRequirements() {\r\n    const selectedBuildCase = this.buildCaseList.find(bc => bc.cID === this.searchRequest.CBuildCaseID);\r\n    const config: RequestItemImportConfig = {\r\n      buildCaseId: this.searchRequest.CBuildCaseID || 0,\r\n      buildCaseName: selectedBuildCase?.CBuildCaseName || '',\r\n      selectedItems: this.getSelectedItems(),\r\n      totalItems: this.getSelectedItems().length,\r\n      searchCriteria: {\r\n        CHouseType: this.searchRequest.CHouseType || undefined,\r\n        CLocation: this.searchRequest.CLocation || undefined,\r\n        CRequirement: this.searchRequest.CRequirement || undefined\r\n      }\r\n    };\r\n\r\n    this.itemsImported.emit(config);\r\n    this.close();\r\n  }\r\n\r\n  close() {\r\n    this.resetSelections();\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  private resetSelections() {\r\n    this.currentStep = 1;\r\n    this.requirements.forEach(requirement => {\r\n      requirement.selected = false;\r\n    });\r\n  }\r\n\r\n  selectAll() {\r\n    const allSelected = this.requirements.every(item => item.selected);\r\n    this.requirements.forEach(item => {\r\n      item.selected = !allSelected;\r\n    });\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.getSelectedItems().length;\r\n  }\r\n\r\n  getTotalCount(): number {\r\n    return this.requirements.length;\r\n  }\r\n}\r\n", "<nb-card class=\"request-item-import-dialog\">\r\n  <nb-card-header class=\"request-item-import-header\">\r\n    <div class=\"request-item-import-title\">需求項目匯入</div>\r\n    <button class=\"close-btn\" nbButton ghost (click)=\"close()\">\r\n      <nb-icon icon=\"close-outline\"></nb-icon>\r\n    </button>\r\n  </nb-card-header>\r\n\r\n  <nb-card-body class=\"request-item-import-body\">\r\n    <!-- 步驟導航 -->\r\n    <div class=\"step-nav\">\r\n      <div class=\"step-item\" [ngClass]=\"{\r\n        'active': currentStep === 1,\r\n        'completed': currentStep > 1,\r\n        'pending': currentStep < 1\r\n      }\">1. 選擇項目</div>\r\n      <div class=\"step-item\" [ngClass]=\"{\r\n        'active': currentStep === 2,\r\n        'completed': currentStep > 2,\r\n        'pending': currentStep < 2\r\n      }\">2. 確認匯入</div>\r\n    </div>\r\n    <!-- 步驟1: 選擇需求項目 -->\r\n    <div *ngIf=\"currentStep === 1\" class=\"step-content\">\r\n      <!-- 搜尋區塊 -->\r\n      <div class=\"search-section mb-4\">\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"search-outline\" class=\"mr-2\"></nb-icon>搜尋條件\r\n        </div>\r\n        <div class=\"search-form\">\r\n          <div class=\"row\">\r\n            <div class=\"form-group col-12 col-md-6\">\r\n              <label for=\"buildCase\" class=\"label mb-2\">建案</label>\r\n              <nb-select [(ngModel)]=\"searchRequest.CBuildCaseID\" (ngModelChange)=\"onBuildCaseChange()\"\r\n                placeholder=\"請選擇建案\">\r\n                <nb-option *ngFor=\"let case of buildCaseList\" [value]=\"case.cID\">\r\n                  {{ case.CBuildCaseName }}\r\n                </nb-option>\r\n              </nb-select>\r\n            </div>\r\n            <div class=\"form-group col-12 col-md-6\">\r\n              <label for=\"houseType\" class=\"label mb-2\">房屋類型</label>\r\n              <nb-select [(ngModel)]=\"searchRequest.CHouseType\" multiple placeholder=\"請選擇房屋類型\">\r\n                <nb-option *ngFor=\"let type of houseTypeOptions\" [value]=\"type.value\">\r\n                  {{ type.label }}\r\n                </nb-option>\r\n              </nb-select>\r\n            </div>\r\n          </div>\r\n          <div class=\"row\">\r\n            <div class=\"form-group col-12 col-md-6\">\r\n              <label for=\"location\" class=\"label mb-2\">區域</label>\r\n              <input type=\"text\" nbInput id=\"location\" placeholder=\"請輸入區域\" [(ngModel)]=\"searchRequest.CLocation\">\r\n            </div>\r\n            <div class=\"form-group col-12 col-md-6\">\r\n              <label for=\"requirement\" class=\"label mb-2\">工程項目</label>\r\n              <input type=\"text\" nbInput id=\"requirement\" placeholder=\"請輸入工程項目\"\r\n                [(ngModel)]=\"searchRequest.CRequirement\">\r\n            </div>\r\n          </div>\r\n          <div class=\"row\">\r\n            <div class=\"col-12 text-right\">\r\n              <button class=\"btn btn-secondary me-2\" (click)=\"resetSearch()\">\r\n                <nb-icon icon=\"refresh-outline\"></nb-icon>\r\n                重置\r\n              </button>\r\n              <button class=\"btn btn-primary\" (click)=\"onSearch()\">\r\n                <nb-icon icon=\"search-outline\"></nb-icon>\r\n                搜尋\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 需求項目選擇區域 -->\r\n      <div class=\"requirement-selection\">\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"layers-outline\" class=\"mr-2\"></nb-icon>選擇需求項目\r\n        </div>\r\n\r\n        <!-- 載入中 -->\r\n        <div *ngIf=\"loading\" class=\"text-center py-4\">\r\n          <div class=\"spinner-border text-primary\" role=\"status\">\r\n            <span class=\"sr-only\">載入中...</span>\r\n          </div>\r\n          <div class=\"mt-2\">載入需求項目中...</div>\r\n        </div>\r\n\r\n        <!-- 需求項目列表 -->\r\n        <div *ngIf=\"!loading\" class=\"requirement-list\">\r\n          <div *ngIf=\"requirements.length > 0; else noRequirements\">\r\n            <div *ngFor=\"let requirement of requirements\" class=\"requirement-item\">\r\n              <nb-checkbox [(ngModel)]=\"requirement.selected\" (ngModelChange)=\"onRequirementItemChange()\">\r\n                <div class=\"requirement-info\">\r\n                  <div class=\"item-name\">{{ requirement.CRequirement || '未命名需求' }}</div>\r\n                  <div class=\"item-code\">位置: {{ requirement.CLocation || '未指定' }}</div>\r\n                  <div class=\"item-status\">\r\n                    單位: {{ requirement.CUnit || '未指定' }}\r\n                  </div>\r\n                  <div class=\"item-type\">\r\n                    單價: {{ requirement.CUnitPrice | currency:'TWD':'symbol-narrow':'1.0-0' }}\r\n                  </div>\r\n                  <div *ngIf=\"requirement.CRemark\" class=\"item-remark\">\r\n                    備註: {{ requirement.CRemark }}\r\n                  </div>\r\n                </div>\r\n              </nb-checkbox>\r\n            </div>\r\n          </div>\r\n          <ng-template #noRequirements>\r\n            <div class=\"no-requirements\">\r\n              <nb-icon icon=\"info-outline\" class=\"mr-2\"></nb-icon>\r\n              沒有可匯入的需求項目，請調整搜尋條件或聯繫管理員\r\n            </div>\r\n          </ng-template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 步驟2: 確認匯入 -->\r\n    <div *ngIf=\"currentStep === 2\" class=\"step-content\">\r\n      <div class=\"confirmation-area\">\r\n        <div class=\"section-title\">\r\n          <nb-icon icon=\"checkmark-circle-outline\" class=\"mr-2\"></nb-icon>確認匯入詳情\r\n        </div>\r\n\r\n        <div class=\"selected-summary\">\r\n          <div class=\"summary-text\">\r\n            將匯入 <strong>{{ getSelectedCount() }}</strong> 個需求項目到目前的建案中\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"selected-requirements-details\">\r\n          <div *ngFor=\"let item of getSelectedItems(); let i = index\" class=\"requirement-detail-section\">\r\n            <div class=\"requirement-detail-header\">\r\n              <h6 class=\"requirement-name\">{{ i + 1 }}. {{ item.CRequirement || '未命名需求' }}</h6>\r\n              <div class=\"requirement-meta\">\r\n                <div class=\"requirement-location\">位置: {{ item.CLocation || '未指定' }}</div>\r\n                <div class=\"requirement-unit\">單位: {{ item.CUnit || '未指定' }}</div>\r\n                <div class=\"requirement-price\">單價: {{ item.CUnitPrice | currency:'TWD':'symbol-narrow':'1.0-0' }}</div>\r\n              </div>\r\n              <div *ngIf=\"item.CRemark\" class=\"requirement-remark\">\r\n                備註: {{ item.CRemark }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <nb-card-footer class=\"request-item-import-footer\">\r\n    <div class=\"step-buttons\">\r\n      <button *ngIf=\"currentStep > 1\" nbButton ghost (click)=\"previousStep()\" class=\"mr-2\">\r\n        <nb-icon icon=\"arrow-back-outline\" class=\"mr-1\"></nb-icon>\r\n        上一步\r\n      </button>\r\n\r\n      <button nbButton ghost (click)=\"close()\" class=\"mr-2\">\r\n        取消\r\n      </button>\r\n\r\n      <button *ngIf=\"currentStep < 2\" nbButton status=\"primary\" [disabled]=\"!canProceed()\" (click)=\"nextStep()\">\r\n        下一步\r\n        <nb-icon icon=\"arrow-forward-outline\" class=\"mr-1\"></nb-icon>\r\n      </button>\r\n\r\n      <button *ngIf=\"currentStep === 2\" nbButton status=\"success\" (click)=\"importRequirements()\">\r\n        <nb-icon icon=\"download-outline\" class=\"mr-1\"></nb-icon>\r\n        確認匯入\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,EACdC,aAAa,QAER,gBAAgB;AAGvB,SAAoCC,cAAc,QAAiE,yBAAyB;AAE5I,SAASC,aAAa,QAAQ,mCAAmC;;;;;;;;;;;;;;;ICkBjDC,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,GAAA,CAAkB;IAC9DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,cAAA,MACF;;;;;IAMAT,EAAA,CAAAC,cAAA,oBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAAC,KAAA,CAAoB;IACnEX,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,OAAA,CAAAE,KAAA,MACF;;;;;IAuCJZ,EAFJ,CAAAC,cAAA,cAA8C,cACW,eAC/B;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;IACNH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,oDAAU;IAC9BF,EAD8B,CAAAG,YAAA,EAAM,EAC9B;;;;;IAgBIH,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,oBAAAK,cAAA,CAAAC,OAAA,MACF;;;;;;IAZJd,EADF,CAAAC,cAAA,cAAuE,sBACuB;IAA/ED,EAAA,CAAAe,gBAAA,2BAAAC,mGAAAC,MAAA;MAAA,MAAAJ,cAAA,GAAAb,EAAA,CAAAkB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAR,cAAA,CAAAS,QAAA,EAAAL,MAAA,MAAAJ,cAAA,CAAAS,QAAA,GAAAL,MAAA;MAAA,OAAAjB,EAAA,CAAAuB,WAAA,CAAAN,MAAA;IAAA,EAAkC;IAACjB,EAAA,CAAAwB,UAAA,2BAAAR,mGAAA;MAAAhB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAAuB,WAAA,CAAiBE,MAAA,CAAAE,uBAAA,EAAyB;IAAA,EAAC;IAEvF3B,EADF,CAAAC,cAAA,cAA8B,cACL;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACtEH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACrEH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAA4B,UAAA,KAAAC,oEAAA,kBAAqD;IAK3D7B,EAFI,CAAAG,YAAA,EAAM,EACM,EACV;;;;IAfSH,EAAA,CAAAO,SAAA,EAAkC;IAAlCP,EAAA,CAAA8B,gBAAA,YAAAjB,cAAA,CAAAS,QAAA,CAAkC;IAEpBtB,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAA+B,iBAAA,CAAAlB,cAAA,CAAAmB,YAAA,qCAAyC;IACzChC,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAQ,kBAAA,mBAAAK,cAAA,CAAAoB,SAAA,6BAAwC;IAE7DjC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,oBAAAK,cAAA,CAAAqB,KAAA,8BACF;IAEElC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,oBAAAR,EAAA,CAAAmC,WAAA,QAAAtB,cAAA,CAAAuB,UAAA,wCACF;IACMpC,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAI,UAAA,SAAAS,cAAA,CAAAC,OAAA,CAAyB;;;;;IAZvCd,EAAA,CAAAC,cAAA,UAA0D;IACxDD,EAAA,CAAA4B,UAAA,IAAAS,6DAAA,oBAAuE;IAiBzErC,EAAA,CAAAG,YAAA,EAAM;;;;IAjByBH,EAAA,CAAAO,SAAA,EAAe;IAAfP,EAAA,CAAAI,UAAA,YAAAqB,MAAA,CAAAa,YAAA,CAAe;;;;;IAmB5CtC,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAuC,SAAA,kBAAoD;IACpDvC,EAAA,CAAAE,MAAA,yJACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAxBVH,EAAA,CAAAC,cAAA,cAA+C;IAoB7CD,EAnBA,CAAA4B,UAAA,IAAAY,uDAAA,kBAA0D,IAAAC,+DAAA,gCAAAzC,EAAA,CAAA0C,sBAAA,CAmB7B;IAM/B1C,EAAA,CAAAG,YAAA,EAAM;;;;;IAzBEH,EAAA,CAAAO,SAAA,EAA+B;IAAAP,EAA/B,CAAAI,UAAA,SAAAqB,MAAA,CAAAa,YAAA,CAAAK,MAAA,KAA+B,aAAAC,iBAAA,CAAmB;;;;;;IAjE1D5C,EAHJ,CAAAC,cAAA,cAAoD,cAEjB,cACJ;IACzBD,EAAA,CAAAuC,SAAA,kBAAsD;IAAAvC,EAAA,CAAAE,MAAA,gCACxD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIAH,EAHN,CAAAC,cAAA,cAAyB,cACN,cACyB,gBACI;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpDH,EAAA,CAAAC,cAAA,qBACsB;IADXD,EAAA,CAAAe,gBAAA,2BAAA8B,+EAAA5B,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAA4B,GAAA;MAAA,MAAArB,MAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA1B,EAAA,CAAAqB,kBAAA,CAAAI,MAAA,CAAAsB,aAAA,CAAAC,YAAA,EAAA/B,MAAA,MAAAQ,MAAA,CAAAsB,aAAA,CAAAC,YAAA,GAAA/B,MAAA;MAAA,OAAAjB,EAAA,CAAAuB,WAAA,CAAAN,MAAA;IAAA,EAAwC;IAACjB,EAAA,CAAAwB,UAAA,2BAAAqB,+EAAA;MAAA7C,EAAA,CAAAkB,aAAA,CAAA4B,GAAA;MAAA,MAAArB,MAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAAuB,WAAA,CAAiBE,MAAA,CAAAwB,iBAAA,EAAmB;IAAA,EAAC;IAEvFjD,EAAA,CAAA4B,UAAA,KAAAsB,uDAAA,wBAAiE;IAIrElD,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,eAAwC,iBACI;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAC,cAAA,qBAAiF;IAAtED,EAAA,CAAAe,gBAAA,2BAAAoC,+EAAAlC,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAA4B,GAAA;MAAA,MAAArB,MAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA1B,EAAA,CAAAqB,kBAAA,CAAAI,MAAA,CAAAsB,aAAA,CAAAK,UAAA,EAAAnC,MAAA,MAAAQ,MAAA,CAAAsB,aAAA,CAAAK,UAAA,GAAAnC,MAAA;MAAA,OAAAjB,EAAA,CAAAuB,WAAA,CAAAN,MAAA;IAAA,EAAsC;IAC/CjB,EAAA,CAAA4B,UAAA,KAAAyB,uDAAA,wBAAsE;IAK5ErD,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAiB,eACyB,iBACG;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnDH,EAAA,CAAAC,cAAA,iBAAmG;IAAtCD,EAAA,CAAAe,gBAAA,2BAAAuC,2EAAArC,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAA4B,GAAA;MAAA,MAAArB,MAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA1B,EAAA,CAAAqB,kBAAA,CAAAI,MAAA,CAAAsB,aAAA,CAAAd,SAAA,EAAAhB,MAAA,MAAAQ,MAAA,CAAAsB,aAAA,CAAAd,SAAA,GAAAhB,MAAA;MAAA,OAAAjB,EAAA,CAAAuB,WAAA,CAAAN,MAAA;IAAA,EAAqC;IACpGjB,EADE,CAAAG,YAAA,EAAmG,EAC/F;IAEJH,EADF,CAAAC,cAAA,eAAwC,iBACM;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxDH,EAAA,CAAAC,cAAA,iBAC2C;IAAzCD,EAAA,CAAAe,gBAAA,2BAAAwC,2EAAAtC,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAA4B,GAAA;MAAA,MAAArB,MAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA1B,EAAA,CAAAqB,kBAAA,CAAAI,MAAA,CAAAsB,aAAA,CAAAf,YAAA,EAAAf,MAAA,MAAAQ,MAAA,CAAAsB,aAAA,CAAAf,YAAA,GAAAf,MAAA;MAAA,OAAAjB,EAAA,CAAAuB,WAAA,CAAAN,MAAA;IAAA,EAAwC;IAE9CjB,EAHI,CAAAG,YAAA,EAC2C,EACvC,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAiB,eACgB,kBACkC;IAAxBD,EAAA,CAAAwB,UAAA,mBAAAgC,oEAAA;MAAAxD,EAAA,CAAAkB,aAAA,CAAA4B,GAAA;MAAA,MAAArB,MAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAAuB,WAAA,CAASE,MAAA,CAAAgC,WAAA,EAAa;IAAA,EAAC;IAC5DzD,EAAA,CAAAuC,SAAA,mBAA0C;IAC1CvC,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAqD;IAArBD,EAAA,CAAAwB,UAAA,mBAAAkC,oEAAA;MAAA1D,EAAA,CAAAkB,aAAA,CAAA4B,GAAA;MAAA,MAAArB,MAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAAuB,WAAA,CAASE,MAAA,CAAAkC,QAAA,EAAU;IAAA,EAAC;IAClD3D,EAAA,CAAAuC,SAAA,mBAAyC;IACzCvC,EAAA,CAAAE,MAAA,sBACF;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;IAIJH,EADF,CAAAC,cAAA,eAAmC,eACN;IACzBD,EAAA,CAAAuC,SAAA,mBAAsD;IAAAvC,EAAA,CAAAE,MAAA,6CACxD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAWNH,EARA,CAAA4B,UAAA,KAAAgC,iDAAA,kBAA8C,KAAAC,iDAAA,kBAQC;IA4BnD7D,EADE,CAAAG,YAAA,EAAM,EACF;;;;IArFeH,EAAA,CAAAO,SAAA,IAAwC;IAAxCP,EAAA,CAAA8B,gBAAA,YAAAL,MAAA,CAAAsB,aAAA,CAAAC,YAAA,CAAwC;IAErBhD,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAqB,MAAA,CAAAqC,aAAA,CAAgB;IAOnC9D,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAA8B,gBAAA,YAAAL,MAAA,CAAAsB,aAAA,CAAAK,UAAA,CAAsC;IACnBpD,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAI,UAAA,YAAAqB,MAAA,CAAAsC,gBAAA,CAAmB;IASY/D,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAA8B,gBAAA,YAAAL,MAAA,CAAAsB,aAAA,CAAAd,SAAA,CAAqC;IAKhGjC,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAA8B,gBAAA,YAAAL,MAAA,CAAAsB,aAAA,CAAAf,YAAA,CAAwC;IAyB1ChC,EAAA,CAAAO,SAAA,IAAa;IAAbP,EAAA,CAAAI,UAAA,SAAAqB,MAAA,CAAAuC,OAAA,CAAa;IAQbhE,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAI,UAAA,UAAAqB,MAAA,CAAAuC,OAAA,CAAc;;;;;IAoDdhE,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,oBAAAyD,OAAA,CAAAnD,OAAA,MACF;;;;;IARAd,EAFJ,CAAAC,cAAA,cAA+F,cACtD,aACR;IAAAD,EAAA,CAAAE,MAAA,GAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE/EH,EADF,CAAAC,cAAA,cAA8B,cACM;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzEH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjEH,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAE,MAAA,IAAkE;;IACnGF,EADmG,CAAAG,YAAA,EAAM,EACnG;IACNH,EAAA,CAAA4B,UAAA,KAAAsC,wDAAA,kBAAqD;IAIzDlE,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAV2BH,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAmE,kBAAA,KAAAC,IAAA,YAAAH,OAAA,CAAAjC,YAAA,yCAA+C;IAExChC,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAQ,kBAAA,mBAAAyD,OAAA,CAAAhC,SAAA,6BAAiC;IACrCjC,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAQ,kBAAA,mBAAAyD,OAAA,CAAA/B,KAAA,6BAA6B;IAC5BlC,EAAA,CAAAO,SAAA,GAAkE;IAAlEP,EAAA,CAAAQ,kBAAA,mBAAAR,EAAA,CAAAmC,WAAA,QAAA8B,OAAA,CAAA7B,UAAA,uCAAkE;IAE7FpC,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,SAAA6D,OAAA,CAAAnD,OAAA,CAAkB;;;;;IAnB9Bd,EAFJ,CAAAC,cAAA,cAAoD,cACnB,cACF;IACzBD,EAAA,CAAAuC,SAAA,kBAAgE;IAAAvC,EAAA,CAAAE,MAAA,4CAClE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,cAA8B,cACF;IACxBD,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,kFAChD;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAENH,EAAA,CAAAC,cAAA,eAA2C;IACzCD,EAAA,CAAA4B,UAAA,KAAAyC,iDAAA,oBAA+F;IAerGrE,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IApBcH,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAA+B,iBAAA,CAAAN,MAAA,CAAA6C,gBAAA,GAAwB;IAKhBtE,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,YAAAqB,MAAA,CAAA8C,gBAAA,GAAuB;;;;;;IAoBjDvE,EAAA,CAAAC,cAAA,iBAAqF;IAAtCD,EAAA,CAAAwB,UAAA,mBAAAgD,sEAAA;MAAAxE,EAAA,CAAAkB,aAAA,CAAAuD,IAAA;MAAA,MAAAhD,MAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAAuB,WAAA,CAASE,MAAA,CAAAiD,YAAA,EAAc;IAAA,EAAC;IACrE1E,EAAA,CAAAuC,SAAA,kBAA0D;IAC1DvC,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAMTH,EAAA,CAAAC,cAAA,iBAA0G;IAArBD,EAAA,CAAAwB,UAAA,mBAAAmD,sEAAA;MAAA3E,EAAA,CAAAkB,aAAA,CAAA0D,IAAA;MAAA,MAAAnD,MAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAAuB,WAAA,CAASE,MAAA,CAAAoD,QAAA,EAAU;IAAA,EAAC;IACvG7E,EAAA,CAAAE,MAAA,2BACA;IAAAF,EAAA,CAAAuC,SAAA,kBAA6D;IAC/DvC,EAAA,CAAAG,YAAA,EAAS;;;;IAHiDH,EAAA,CAAAI,UAAA,cAAAqB,MAAA,CAAAqD,UAAA,GAA0B;;;;;;IAKpF9E,EAAA,CAAAC,cAAA,iBAA2F;IAA/BD,EAAA,CAAAwB,UAAA,mBAAAuD,sEAAA;MAAA/E,EAAA,CAAAkB,aAAA,CAAA8D,IAAA;MAAA,MAAAvD,MAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAAuB,WAAA,CAASE,MAAA,CAAAwD,kBAAA,EAAoB;IAAA,EAAC;IACxFjF,EAAA,CAAAuC,SAAA,kBAAwD;IACxDvC,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADvHf,OAAM,MAAO+E,0BAA0B;EAarCC,YACUC,kBAAsC,EACtCC,SAAkD,EAClDC,UAAsB,EACtBC,gBAAkC;IAHlC,KAAAH,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAhBjB,KAAAC,WAAW,GAAW,CAAC;IACtB,KAAAC,aAAa,GAAG,IAAIrG,YAAY,EAA2B;IAErE,KAAAsG,WAAW,GAAW,CAAC;IACvB,KAAApD,YAAY,GAA8B,EAAE;IAC5C,KAAA0B,OAAO,GAAY,KAAK;IAExB;IACA,KAAAjB,aAAa,GAAyF,EAAE;IACxG,KAAAe,aAAa,GAA8B,EAAE;IAC7C,KAAAC,gBAAgB,GAAU,EAAE;EAOxB;EAEJ4B,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA;EACAD,oBAAoBA,CAAA;IAClB,IAAI,CAAC7B,gBAAgB,GAAG,IAAI,CAACuB,UAAU,CAACQ,cAAc,CAAC/F,aAAa,CAAC;IACrE,IAAI,CAACgD,aAAa,CAACgD,OAAO,GAAG,CAAC,CAAC,CAAC;IAChC,IAAI,CAAChD,aAAa,CAACiD,OAAO,GAAG,IAAI,CAAC,CAAC;IACnC,IAAI,CAACjD,aAAa,CAACkD,SAAS,GAAG,IAAI;IACnC,IAAI,CAAClD,aAAa,CAACf,YAAY,GAAG,EAAE;IACpC,IAAI,CAACe,aAAa,CAACd,SAAS,GAAG,EAAE;IACjC;IACA,IAAI,CAACc,aAAa,CAACK,UAAU,GAAG,IAAI,CAACW,gBAAgB,CAACmC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACxF,KAAK,CAAC;EAC/E;EAEA;EACAkF,gBAAgBA,CAAA;IACd,IAAI,CAACN,gBAAgB,CAACa,qCAAqC,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEC,SAAS,CAAC;MACTC,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,IAAID,GAAG,CAACE,OAAO,EAAE;UACvC,IAAI,CAAC5C,aAAa,GAAG0C,GAAG,CAACE,OAAO;UAChC;UACA,IAAI,IAAI,CAAClB,WAAW,IAAI,IAAI,CAACA,WAAW,GAAG,CAAC,EAAE;YAC5C,IAAI,CAACzC,aAAa,CAACC,YAAY,GAAG,IAAI,CAACwC,WAAW;UACpD,CAAC,MAAM,IAAI,IAAI,CAAC1B,aAAa,CAACnB,MAAM,GAAG,CAAC,EAAE;YACxC,IAAI,CAACI,aAAa,CAACC,YAAY,GAAG,IAAI,CAACc,aAAa,CAAC,CAAC,CAAC,CAACxD,GAAG;UAC7D;UACA;UACA,IAAI,IAAI,CAACyC,aAAa,CAACC,YAAY,EAAE;YACnC,IAAI,CAAC2D,uBAAuB,EAAE;UAChC;QACF;MACF,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;KACD,CAAC;EACN;EAEA;EACA3D,iBAAiBA,CAAA;IACf,IAAI,CAAC0D,uBAAuB,EAAE;EAChC;EAEA;EACAhD,QAAQA,CAAA;IACN,IAAI,CAACgD,uBAAuB,EAAE;EAChC;EAEA;EACAlD,WAAWA,CAAA;IACT,IAAI,CAACmC,oBAAoB,EAAE;IAC3B,IAAI,IAAI,CAAC9B,aAAa,IAAI,IAAI,CAACA,aAAa,CAACnB,MAAM,GAAG,CAAC,EAAE;MACvD,IAAI,CAACI,aAAa,CAACC,YAAY,GAAG,IAAI,CAACc,aAAa,CAAC,CAAC,CAAC,CAACxD,GAAG;IAC7D;IACA,IAAI,CAACqG,uBAAuB,EAAE;EAChC;EAEAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAC5D,aAAa,CAACC,YAAY,EAAE;MACpC;IACF;IAEA,IAAI,CAACgB,OAAO,GAAG,IAAI;IAEnB;IACA,MAAM8C,sBAAsB,GAA8B;MACxD9D,YAAY,EAAE,IAAI,CAACD,aAAa,CAACC,YAAY;MAC7CI,UAAU,EAAE,IAAI,CAACL,aAAa,CAACK,UAAU;MACzCnB,SAAS,EAAE,IAAI,CAACc,aAAa,CAACd,SAAS,IAAI,IAAI;MAC/CD,YAAY,EAAE,IAAI,CAACe,aAAa,CAACf,YAAY,IAAI,IAAI;MACrD+D,OAAO,EAAE,IAAI,CAAChD,aAAa,CAACgD,OAAO;MACnCC,OAAO,EAAE,IAAI,CAACjD,aAAa,CAACiD,OAAO;MACnCC,SAAS,EAAE,IAAI,CAAClD,aAAa,CAACkD,SAAS;MACvCc,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAED,IAAI,CAAC5B,kBAAkB,CAAC6B,gDAAgD,CAAC;MACvEZ,IAAI,EAAES;KACP,CAAC,CAACR,SAAS,CAAC;MACXC,IAAI,EAAGW,QAAwC,IAAI;QACjD,IAAI,CAAClD,OAAO,GAAG,KAAK;QACpB,IAAIkD,QAAQ,CAACT,UAAU,KAAK,CAAC,IAAIS,QAAQ,CAACR,OAAO,EAAE;UACjD,IAAI,CAACpE,YAAY,GAAG4E,QAAQ,CAACR,OAAO,CAACR,GAAG,CAACiB,IAAI,KAAK;YAChD,GAAGA,IAAI;YACP7F,QAAQ,EAAE;WACX,CAAC,CAAC;QACL,CAAC,MAAM;UACL,IAAI,CAACgB,YAAY,GAAG,EAAE;QACxB;MACF,CAAC;MACDsE,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC5C,OAAO,GAAG,KAAK;QACpB,IAAI,CAAC1B,YAAY,GAAG,EAAE;MACxB;KACD,CAAC;EACJ;EAEAX,uBAAuBA,CAAA;IACrB;EAAA;EAGF4C,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACjC,YAAY,CAAC8E,MAAM,CAACD,IAAI,IAAIA,IAAI,CAAC7F,QAAQ,CAAC;EACxD;EAEAwD,UAAUA,CAAA;IACR,QAAQ,IAAI,CAACY,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACnB,gBAAgB,EAAE,CAAC5B,MAAM,GAAG,CAAC;MAC3C,KAAK,CAAC;QACJ,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAkC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,UAAU,EAAE,IAAI,IAAI,CAACY,WAAW,GAAG,CAAC,EAAE;MAC7C,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEAhB,YAAYA,CAAA;IACV,IAAI,IAAI,CAACgB,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEA2B,eAAeA,CAAA;IACb,MAAMC,aAAa,GAAG;MACpB,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;KACJ;IACD,OAAOA,aAAa,CAAC,IAAI,CAAC5B,WAAyC,CAAC,IAAI,EAAE;EAC5E;EAEAT,kBAAkBA,CAAA;IAChB,MAAMsC,iBAAiB,GAAG,IAAI,CAACzD,aAAa,CAAC0D,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACnH,GAAG,KAAK,IAAI,CAACyC,aAAa,CAACC,YAAY,CAAC;IACnG,MAAM0E,MAAM,GAA4B;MACtClC,WAAW,EAAE,IAAI,CAACzC,aAAa,CAACC,YAAY,IAAI,CAAC;MACjD2E,aAAa,EAAEJ,iBAAiB,EAAE9G,cAAc,IAAI,EAAE;MACtDmH,aAAa,EAAE,IAAI,CAACrD,gBAAgB,EAAE;MACtCsD,UAAU,EAAE,IAAI,CAACtD,gBAAgB,EAAE,CAAC5B,MAAM;MAC1CmF,cAAc,EAAE;QACd1E,UAAU,EAAE,IAAI,CAACL,aAAa,CAACK,UAAU,IAAI2E,SAAS;QACtD9F,SAAS,EAAE,IAAI,CAACc,aAAa,CAACd,SAAS,IAAI8F,SAAS;QACpD/F,YAAY,EAAE,IAAI,CAACe,aAAa,CAACf,YAAY,IAAI+F;;KAEpD;IAED,IAAI,CAACtC,aAAa,CAACuC,IAAI,CAACN,MAAM,CAAC;IAC/B,IAAI,CAACO,KAAK,EAAE;EACd;EAEAA,KAAKA,CAAA;IACH,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAAC7C,SAAS,CAAC4C,KAAK,EAAE;EACxB;EAEQC,eAAeA,CAAA;IACrB,IAAI,CAACxC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACpD,YAAY,CAAC6F,OAAO,CAACC,WAAW,IAAG;MACtCA,WAAW,CAAC9G,QAAQ,GAAG,KAAK;IAC9B,CAAC,CAAC;EACJ;EAEA+G,SAASA,CAAA;IACP,MAAMC,WAAW,GAAG,IAAI,CAAChG,YAAY,CAACiG,KAAK,CAACpB,IAAI,IAAIA,IAAI,CAAC7F,QAAQ,CAAC;IAClE,IAAI,CAACgB,YAAY,CAAC6F,OAAO,CAAChB,IAAI,IAAG;MAC/BA,IAAI,CAAC7F,QAAQ,GAAG,CAACgH,WAAW;IAC9B,CAAC,CAAC;EACJ;EAEAhE,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACC,gBAAgB,EAAE,CAAC5B,MAAM;EACvC;EAEA6F,aAAaA,CAAA;IACX,OAAO,IAAI,CAAClG,YAAY,CAACK,MAAM;EACjC;;;uCA5MWuC,0BAA0B,EAAAlF,EAAA,CAAAyI,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAA3I,EAAA,CAAAyI,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA7I,EAAA,CAAAyI,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAA/I,EAAA,CAAAyI,iBAAA,CAAAO,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAA1B/D,0BAA0B;MAAAgE,SAAA;MAAAC,MAAA;QAAA3D,WAAA;MAAA;MAAA4D,OAAA;QAAA3D,aAAA;MAAA;MAAA4D,UAAA;MAAAC,QAAA,GAAAtJ,EAAA,CAAAuJ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClDnC7J,EAFJ,CAAAC,cAAA,iBAA4C,wBACS,aACV;UAAAD,EAAA,CAAAE,MAAA,2CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACnDH,EAAA,CAAAC,cAAA,gBAA2D;UAAlBD,EAAA,CAAAwB,UAAA,mBAAAuI,4DAAA;YAAA,OAASD,GAAA,CAAA7B,KAAA,EAAO;UAAA,EAAC;UACxDjI,EAAA,CAAAuC,SAAA,iBAAwC;UAE5CvC,EADE,CAAAG,YAAA,EAAS,EACM;UAKbH,EAHJ,CAAAC,cAAA,sBAA+C,aAEvB,aAKjB;UAAAD,EAAA,CAAAE,MAAA,kCAAO;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChBH,EAAA,CAAAC,cAAA,cAIG;UAAAD,EAAA,CAAAE,MAAA,mCAAO;UACZF,EADY,CAAAG,YAAA,EAAM,EACZ;UAoGNH,EAlGA,CAAA4B,UAAA,KAAAoI,0CAAA,kBAAoD,KAAAC,0CAAA,kBAkGA;UA6BtDjK,EAAA,CAAAG,YAAA,EAAe;UAGbH,EADF,CAAAC,cAAA,0BAAmD,eACvB;UACxBD,EAAA,CAAA4B,UAAA,KAAAsI,6CAAA,qBAAqF;UAKrFlK,EAAA,CAAAC,cAAA,kBAAsD;UAA/BD,EAAA,CAAAwB,UAAA,mBAAA2I,6DAAA;YAAA,OAASL,GAAA,CAAA7B,KAAA,EAAO;UAAA,EAAC;UACtCjI,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAOTH,EALA,CAAA4B,UAAA,KAAAwI,6CAAA,qBAA0G,KAAAC,6CAAA,qBAKf;UAMjGrK,EAFI,CAAAG,YAAA,EAAM,EACS,EACT;;;UAnKmBH,EAAA,CAAAO,SAAA,GAIrB;UAJqBP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAsK,eAAA,IAAAC,GAAA,EAAAT,GAAA,CAAApE,WAAA,QAAAoE,GAAA,CAAApE,WAAA,MAAAoE,GAAA,CAAApE,WAAA,MAIrB;UACqB1F,EAAA,CAAAO,SAAA,GAIrB;UAJqBP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAsK,eAAA,KAAAC,GAAA,EAAAT,GAAA,CAAApE,WAAA,QAAAoE,GAAA,CAAApE,WAAA,MAAAoE,GAAA,CAAApE,WAAA,MAIrB;UAGE1F,EAAA,CAAAO,SAAA,GAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAA0J,GAAA,CAAApE,WAAA,OAAuB;UAkGvB1F,EAAA,CAAAO,SAAA,EAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAA0J,GAAA,CAAApE,WAAA,OAAuB;UAiClB1F,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,SAAA0J,GAAA,CAAApE,WAAA,KAAqB;UASrB1F,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,SAAA0J,GAAA,CAAApE,WAAA,KAAqB;UAKrB1F,EAAA,CAAAO,SAAA,EAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAA0J,GAAA,CAAApE,WAAA,OAAuB;;;qBDjIlCrG,YAAY,EAAAmL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,YAAA,EACZtL,WAAW,EAAAuL,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXzL,YAAY,EAAAqJ,EAAA,CAAAqC,eAAA,EAAArC,EAAA,CAAAsC,mBAAA,EAAAtC,EAAA,CAAAuC,qBAAA,EAAAvC,EAAA,CAAAwC,qBAAA,EACZ5L,cAAc,EAAAoJ,EAAA,CAAAyC,iBAAA,EACd5L,YAAY,EAAAmJ,EAAA,CAAA0C,eAAA,EACZ5L,gBAAgB,EAAAkJ,EAAA,CAAA2C,mBAAA,EAChB5L,cAAc,EAAAiJ,EAAA,CAAA4C,iBAAA,EAAA5C,EAAA,CAAA6C,iBAAA,EACd7L,cAAc,EACdC,aAAa,EAAA+I,EAAA,CAAA8C,gBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}