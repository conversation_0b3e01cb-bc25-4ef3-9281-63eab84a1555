{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class FooterComponent {\n  static {\n    this.ɵfac = function FooterComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FooterComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FooterComponent,\n      selectors: [[\"ngx-footer\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function FooterComponent_Template(rf, ctx) {},\n      styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 429:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nUndefined variable.\\\\n   \\u2577\\\\n32 \\u2502 $gradient-success: linear-gradient(135deg, $success-light 0%, $success-base 100%);\\\\r\\\\n   \\u2502                                            ^^^^^^^^^^^^^^\\\\n   \\u2575\\\\n  src\\\\\\\\app\\\\\\\\@theme\\\\\\\\styles\\\\\\\\_colors.scss 32:44                    @import\\\\n  src\\\\\\\\app\\\\\\\\@theme\\\\\\\\styles\\\\\\\\themes.scss 6:9                       @import\\\\n  src\\\\\\\\app\\\\\\\\@theme\\\\\\\\components\\\\\\\\footer\\\\\\\\footer.component.scss 1:9  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[429]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FooterComponent", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "template", "FooterComponent_Template", "rf", "ctx", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\components\\footer\\footer.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n    selector: 'ngx-footer',\r\n    styleUrls: ['./footer.component.scss'],\r\n    template: ``,\r\n    standalone: true,\r\n})\r\nexport class FooterComponent {\r\n}\r\n"], "mappings": ";AAQA,OAAM,MAAOA,eAAe;;;uCAAfA,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}