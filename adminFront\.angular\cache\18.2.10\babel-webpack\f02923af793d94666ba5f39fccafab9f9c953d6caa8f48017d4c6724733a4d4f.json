{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { QUOTATION_TEMPLATE } from 'src/assets/template/quotation-template';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { concatMap, tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\nimport { EnumQuotationStatus } from 'src/app/shared/enum/enumQuotationStatus';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport { CQuotationItemType } from 'src/app/models/quotation.model';\nimport { SpaceTemplateSelectorButtonComponent } from 'src/app/shared/components/space-template-selector/space-template-selector-button.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"src/app/shared/services/event.service\";\nimport * as i10 from \"src/app/shared/services/utility.service\";\nimport * as i11 from \"src/app/services/quotation.service\";\nimport * as i12 from \"src/app/shared/components/space-template-selector/space-template-selector.service\";\nimport * as i13 from \"@angular/common\";\nimport * as i14 from \"@angular/forms\";\nimport * as i15 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i16 from \"../components/breadcrumb/breadcrumb.component\";\nimport * as i17 from \"../../@theme/directives/label.directive\";\nconst _c0 = [\"fileInput\"];\nfunction HouseholdManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r3.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r5.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r6.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r7.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r8.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r9.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_button_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_button_74_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogHouseholdMain_r12 = i0.ɵɵreference(117);\n      return i0.ɵɵresetView(ctx_r10.openModel(dialogHouseholdMain_r12));\n    });\n    i0.ɵɵelement(1, \"i\", 56);\n    i0.ɵɵtext(2, \"\\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_111_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_111_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const item_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogUpdateHousehold_r16 = i0.ɵɵreference(115);\n      return i0.ɵɵresetView(ctx_r10.openModelDetail(dialogUpdateHousehold_r16, item_r15));\n    });\n    i0.ɵɵelement(1, \"i\", 66);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_111_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 57);\n    i0.ɵɵtemplate(20, HouseholdManagementComponent_tr_111_button_20_Template, 3, 0, \"button\", 58);\n    i0.ɵɵelementStart(21, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_111_Template_button_click_21_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"customer-change-picture\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵelement(22, \"i\", 60);\n    i0.ɵɵtext(23, \"\\u6D3D\\u8AC7\\u7D00\\u9304 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_111_Template_button_click_24_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"sample-selection-result\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵelement(25, \"i\", 61);\n    i0.ɵɵtext(26, \"\\u5BA2\\u8B8A\\u78BA\\u8A8D\\u5716\\u8AAA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_111_Template_button_click_27_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"finaldochouse_management\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵelement(28, \"i\", 62);\n    i0.ɵɵtext(29, \"\\u7C3D\\u7F72\\u6587\\u4EF6\\u6B77\\u7A0B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_111_Template_button_click_30_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.resetSecureKey(item_r15));\n    });\n    i0.ɵɵelement(31, \"i\", 63);\n    i0.ɵɵtext(32, \"\\u91CD\\u7F6E\\u5BC6\\u78BC \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_111_Template_button_click_33_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogQuotation_r17 = i0.ɵɵreference(119);\n      return i0.ɵɵresetView(ctx_r10.openQuotation(dialogQuotation_r17, item_r15));\n    });\n    i0.ɵɵelement(34, \"i\", 64);\n    i0.ɵɵtext(35, \"\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", item_r15.CHouseType === 2 ? \"\\u92B7\\u552E\\u6236\" : \"\", \" \", item_r15.CHouseType === 1 ? \"\\u5730\\u4E3B\\u6236\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CIsChange === true ? \"\\u5BA2\\u8B8A\" : item_r15.CIsChange === false ? \"\\u6A19\\u6E96\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CProgressName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \", item_r15.CPayStatus === 0 ? \"\\u672A\\u4ED8\\u6B3E\" : \"\", \" \", item_r15.CPayStatus === 1 ? \"\\u5DF2\\u4ED8\\u6B3E\" : \"\", \" \", item_r15.CPayStatus === 2 ? \"\\u7121\\u9808\\u4ED8\\u6B3E\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CSignStatus === 0 || item_r15.CSignStatus == null ? \"\\u672A\\u7C3D\\u56DE\" : \"\\u5DF2\\u7C3D\\u56DE\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r10.getQuotationStatusText(item_r15.CQuotationStatus));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CIsEnable ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isUpdate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_nb_card_body_1_nb_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r20);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r20.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_nb_card_body_1_nb_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r21);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r21.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_35_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r23);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r23.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"label\", 102);\n    i0.ɵɵtext(2, \" \\u4ED8\\u6B3E\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 103);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_35_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CPayStatusSelected, $event) || (ctx_r10.detailSelected.CPayStatusSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_35_nb_option_4_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CPayStatusSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.payStatusOptions);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_36_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r25 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r25);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r25.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"label\", 104);\n    i0.ɵɵtext(2, \" \\u9032\\u5EA6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 105);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_36_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CProgressSelected, $event) || (ctx_r10.detailSelected.CProgressSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_36_nb_option_4_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CProgressSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.progressOptions);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card-body\", 72)(1, \"div\", 73)(2, \"label\", 74);\n    i0.ɵɵtext(3, \" \\u5EFA\\u6848\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-select\", 75);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_nb_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CBuildCaseSelected, $event) || (ctx_r10.detailSelected.CBuildCaseSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(5, HouseholdManagementComponent_ng_template_114_nb_card_body_1_nb_option_5_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 73)(7, \"label\", 76);\n    i0.ɵɵtext(8, \" \\u6236\\u578B\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 77);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CHousehold, $event) || (ctx_r10.houseDetail.CHousehold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 73)(11, \"label\", 78);\n    i0.ɵɵtext(12, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 79);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CFloor, $event) || (ctx_r10.houseDetail.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 73)(15, \"label\", 80);\n    i0.ɵɵtext(16, \" \\u5BA2\\u6236\\u59D3\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 81);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CCustomerName, $event) || (ctx_r10.houseDetail.CCustomerName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 73)(19, \"label\", 82);\n    i0.ɵɵtext(20, \" \\u8EAB\\u5206\\u8B49\\u5B57\\u865F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 83);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CNationalId, $event) || (ctx_r10.houseDetail.CNationalId = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 73)(23, \"label\", 84);\n    i0.ɵɵtext(24, \" \\u96FB\\u5B50\\u90F5\\u4EF6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"input\", 85);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CMail, $event) || (ctx_r10.houseDetail.CMail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 73)(27, \"label\", 86);\n    i0.ɵɵtext(28, \" \\u806F\\u7D61\\u96FB\\u8A71 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"input\", 87);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CPhone, $event) || (ctx_r10.houseDetail.CPhone = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 73)(31, \"label\", 88);\n    i0.ɵɵtext(32, \" \\u6236\\u5225\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-select\", 89);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_nb_select_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CHouseTypeSelected, $event) || (ctx_r10.detailSelected.CHouseTypeSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(34, HouseholdManagementComponent_ng_template_114_nb_card_body_1_nb_option_34_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_35_Template, 5, 2, \"div\", 90)(36, HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_36_Template, 5, 2, \"div\", 90);\n    i0.ɵɵelementStart(37, \"div\", 73)(38, \"label\", 91);\n    i0.ɵɵtext(39, \" \\u662F\\u5426\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"nb-checkbox\", 92);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_nb_checkbox_checkedChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CIsChange, $event) || (ctx_r10.houseDetail.CIsChange = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(41, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 73)(43, \"label\", 93);\n    i0.ɵɵtext(44, \" \\u662F\\u5426\\u555F\\u7528 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"nb-checkbox\", 92);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_nb_checkbox_checkedChange_45_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CIsEnable, $event) || (ctx_r10.houseDetail.CIsEnable = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(46, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 94)(48, \"label\", 95);\n    i0.ɵɵtext(49, \" \\u5BA2\\u8B8A\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 96)(51, \"nb-form-field\", 97);\n    i0.ɵɵelement(52, \"nb-icon\", 98);\n    i0.ɵɵelementStart(53, \"input\", 99);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_53_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.changeStartDate, $event) || (ctx_r10.houseDetail.changeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(54, \"nb-datepicker\", 100, 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"nb-form-field\", 97);\n    i0.ɵɵelement(57, \"nb-icon\", 98);\n    i0.ɵɵelementStart(58, \"input\", 101);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_58_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.changeEndDate, $event) || (ctx_r10.houseDetail.changeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(59, \"nb-datepicker\", 100, 5);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const StartDate_r26 = i0.ɵɵreference(55);\n    const EndDate_r27 = i0.ɵɵreference(60);\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CBuildCaseSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.userBuildCaseOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CHousehold);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CFloor);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CCustomerName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CNationalId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CMail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CPhone);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CHouseTypeSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.houseTypeOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isChangePayStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isChangeProgress);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r10.houseDetail.CIsChange);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r10.houseDetail.CIsEnable);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"nbDatepicker\", StartDate_r26);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.changeStartDate);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"nbDatepicker\", EndDate_r27);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.changeEndDate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 106);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_114_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ref_r28 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onSubmitDetail(ref_r28));\n    });\n    i0.ɵɵelement(1, \"i\", 107);\n    i0.ɵɵtext(2, \"\\u9001\\u51FA \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_114_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 67);\n    i0.ɵɵtemplate(1, HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template, 61, 18, \"nb-card-body\", 68);\n    i0.ɵɵelementStart(2, \"nb-card-footer\", 53)(3, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_114_Template_button_click_3_listener() {\n      const ref_r28 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r28));\n    });\n    i0.ɵɵelement(4, \"i\", 70);\n    i0.ɵɵtext(5, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, HouseholdManagementComponent_ng_template_114_button_6_Template, 3, 0, \"button\", 71);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.houseDetail);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isCreate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_116_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_116_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ref_r31 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.addHouseHoldMain(ref_r31));\n    });\n    i0.ɵɵelement(1, \"i\", 117);\n    i0.ɵɵtext(2, \"\\u5132\\u5B58 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_116_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 67)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6236\\u5225\\u7BA1\\u7406 \\u300B\\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 72)(4, \"div\", 73)(5, \"label\", 108);\n    i0.ɵɵtext(6, \" \\u68DF\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 109);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_116_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CBuildingName, $event) || (ctx_r10.houseHoldMain.CBuildingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 73)(9, \"label\", 110);\n    i0.ɵɵtext(10, \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 111);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_116_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CHouseHoldCount, $event) || (ctx_r10.houseHoldMain.CHouseHoldCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 73)(13, \"label\", 112);\n    i0.ɵɵtext(14, \"\\u672C\\u68E0\\u7E3D\\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 113);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_116_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CFloor, $event) || (ctx_r10.houseHoldMain.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"nb-card-footer\", 53)(17, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_116_Template_button_click_17_listener() {\n      const ref_r31 = i0.ɵɵrestoreView(_r30).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r31));\n    });\n    i0.ɵɵelement(18, \"i\", 70);\n    i0.ɵɵtext(19, \"\\u95DC\\u9589 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, HouseholdManagementComponent_ng_template_116_button_20_Template, 3, 0, \"button\", 115);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CBuildingName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CHouseHoldCount);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CFloor);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isCreate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 156)(1, \"div\", 157)(2, \"app-space-template-selector-button\", 158);\n    i0.ɵɵlistener(\"templateApplied\", function HouseholdManagementComponent_ng_template_118_div_4_Template_app_space_template_selector_button_templateApplied_2_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.onSpaceTemplateApplied($event));\n    })(\"error\", function HouseholdManagementComponent_ng_template_118_div_4_Template_app_space_template_selector_button_error_2_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.onTemplateError($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 159);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_div_4_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.addQuotationItem());\n    });\n    i0.ɵɵelement(4, \"i\", 160);\n    i0.ɵɵtext(5, \"\\u65B0\\u589E\\u81EA\\u5B9A\\u7FA9\\u9805\\u76EE \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\")(7, \"button\", 161);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_div_4_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.loadDefaultItems());\n    });\n    i0.ɵɵelement(8, \"i\", 162);\n    i0.ɵɵtext(9, \"\\u8F09\\u5165\\u5BA2\\u8B8A\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_div_4_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.loadRegularItems());\n    });\n    i0.ɵɵelement(11, \"i\", 163);\n    i0.ɵɵtext(12, \"\\u8F09\\u5165\\u9078\\u6A23\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"buildCaseId\", (ctx_r10.searchQuery.CBuildCaseSelected == null ? null : ctx_r10.searchQuery.CBuildCaseSelected.cID == null ? null : ctx_r10.searchQuery.CBuildCaseSelected.cID.toString()) || \"\")(\"text\", \"\\u6A21\\u677F\\u65B0\\u589E\")(\"icon\", \"fas fa-layer-group\")(\"buttonClass\", \"btn btn-warning btn-sm me-2\")(\"disabled\", !(ctx_r10.searchQuery.CBuildCaseSelected == null ? null : ctx_r10.searchQuery.CBuildCaseSelected.cID))(\"CTemplateType\", 2);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 164);\n    i0.ɵɵelement(1, \"i\", 165);\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3, \"\\u5831\\u50F9\\u55AE\\u5DF2\\u9396\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" - \\u6B64\\u5831\\u50F9\\u55AE\\u5DF2\\u9396\\u5B9A\\uFF0C\\u7121\\u6CD5\\u9032\\u884C\\u4FEE\\u6539\\u3002\\u60A8\\u53EF\\u4EE5\\u5217\\u5370\\u6B64\\u5831\\u50F9\\u55AE\\u6216\\u7522\\u751F\\u65B0\\u7684\\u5831\\u50F9\\u55AE\\u3002 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_tr_27_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 175);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_tr_27_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const i_r38 = i0.ɵɵnextContext().index;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.removeQuotationItem(i_r38));\n    });\n    i0.ɵɵelement(1, \"i\", 176);\n    i0.ɵɵtext(2, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_tr_27_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 177);\n    i0.ɵɵelement(1, \"i\", 178);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_tr_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"input\", 166);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_2_listener($event) {\n      const item_r36 = i0.ɵɵrestoreView(_r35).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r36.cLocation, $event) || (item_r36.cLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\")(4, \"input\", 167);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_4_listener($event) {\n      const item_r36 = i0.ɵɵrestoreView(_r35).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r36.cItemName, $event) || (item_r36.cItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\")(6, \"input\", 168);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_6_listener($event) {\n      const item_r36 = i0.ɵɵrestoreView(_r35).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r36.cUnitPrice, $event) || (item_r36.cUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_6_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.calculateTotal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\")(8, \"input\", 169);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_8_listener($event) {\n      const item_r36 = i0.ɵɵrestoreView(_r35).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r36.cUnit, $event) || (item_r36.cUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\")(10, \"input\", 170);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_10_listener($event) {\n      const item_r36 = i0.ɵɵrestoreView(_r35).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r36.cCount, $event) || (item_r36.cCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_10_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.calculateTotal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"td\", 171);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\")(14, \"span\", 172);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtemplate(17, HouseholdManagementComponent_ng_template_118_tr_27_button_17_Template, 3, 0, \"button\", 173)(18, HouseholdManagementComponent_ng_template_118_tr_27_span_18_Template, 2, 0, \"span\", 174);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r36 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-light\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 1 || item_r36.CQuotationItemType === 3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r36.cLocation);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 1 || item_r36.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-light\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 1 || item_r36.CQuotationItemType === 3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r36.cItemName);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 1 || item_r36.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r36.cUnitPrice);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-light\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r36.cUnit);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r36.cCount);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.formatCurrency(item_r36.cUnitPrice * item_r36.cCount), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"badge-primary\", item_r36.CQuotationItemType === 1)(\"badge-info\", item_r36.CQuotationItemType === 3)(\"badge-secondary\", item_r36.CQuotationItemType !== 1 && item_r36.CQuotationItemType !== 3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getQuotationTypeText(item_r36.CQuotationItemType), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isQuotationEditable);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 179);\n    i0.ɵɵtext(2, \" \\u8ACB\\u9EDE\\u64CA\\u300C\\u65B0\\u589E\\u81EA\\u5B9A\\u7FA9\\u9805\\u76EE\\u300D\\u6216\\u300C\\u8F09\\u5165\\u5BA2\\u8B8A\\u9700\\u6C42\\u300D\\u958B\\u59CB\\u5EFA\\u7ACB\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_button_65_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 180);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_button_65_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.createNewQuotation());\n    });\n    i0.ɵɵelement(1, \"i\", 160);\n    i0.ɵɵtext(2, \" \\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_button_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 181);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_button_70_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ref_r40 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.lockQuotation(ref_r40));\n    });\n    i0.ɵɵelement(1, \"i\", 182);\n    i0.ɵɵtext(2, \"\\u9001\\u51FA\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.quotationItems.length === 0);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_button_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 183);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_button_71_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const ref_r40 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.saveQuotation(ref_r40));\n    });\n    i0.ɵɵelement(1, \"i\", 117);\n    i0.ɵɵtext(2, \"\\u66AB\\u5B58\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.quotationItems.length === 0);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_118_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 118)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\");\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_118_div_4_Template, 13, 6, \"div\", 119)(5, HouseholdManagementComponent_ng_template_118_div_5_Template, 5, 0, \"div\", 120);\n    i0.ɵɵelementStart(6, \"div\", 121)(7, \"table\", 122)(8, \"thead\", 49)(9, \"tr\")(10, \"th\", 123);\n    i0.ɵɵtext(11, \"\\u5340\\u57DF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 124);\n    i0.ɵɵtext(13, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 123);\n    i0.ɵɵtext(15, \"\\u55AE\\u50F9 (\\u5143)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 125);\n    i0.ɵɵtext(17, \"\\u55AE\\u4F4D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 125);\n    i0.ɵɵtext(19, \"\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 126);\n    i0.ɵɵtext(21, \"\\u5C0F\\u8A08 (\\u5143)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"th\", 127);\n    i0.ɵɵtext(23, \"\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"th\", 127);\n    i0.ɵɵtext(25, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"tbody\");\n    i0.ɵɵtemplate(27, HouseholdManagementComponent_ng_template_118_tr_27_Template, 19, 26, \"tr\", 52)(28, HouseholdManagementComponent_ng_template_118_tr_28_Template, 3, 0, \"tr\", 128);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 129)(30, \"div\", 130)(31, \"div\", 131)(32, \"div\", 132)(33, \"span\", 133);\n    i0.ɵɵtext(34, \"\\u5C0F\\u8A08\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"span\", 134);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 135)(38, \"div\", 136)(39, \"div\", 137);\n    i0.ɵɵelement(40, \"i\", 138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\")(42, \"span\", 139);\n    i0.ɵɵtext(43, \"\\u71DF\\u696D\\u7A05\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"span\", 140);\n    i0.ɵɵtext(45, \"5%\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 141);\n    i0.ɵɵelement(47, \"i\", 142);\n    i0.ɵɵtext(48, \" \\u56FA\\u5B9A\\u70BA\\u5C0F\\u8A08\\u91D1\\u984D\\u76845% \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 143)(50, \"div\", 144);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 145);\n    i0.ɵɵtext(53, \"\\u542B\\u7A05\\u91D1\\u984D\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(54, \"hr\", 146);\n    i0.ɵɵelementStart(55, \"div\", 147)(56, \"span\", 134);\n    i0.ɵɵtext(57, \"\\u7E3D\\u91D1\\u984D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"span\", 148);\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(60, \"nb-card-footer\", 149)(61, \"div\")(62, \"button\", 150);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_Template_button_click_62_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.printQuotation());\n    });\n    i0.ɵɵelement(63, \"i\", 151);\n    i0.ɵɵtext(64, \" \\u5217\\u5370\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(65, HouseholdManagementComponent_ng_template_118_button_65_Template, 3, 0, \"button\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"div\")(67, \"button\", 153);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_118_Template_button_click_67_listener() {\n      const ref_r40 = i0.ɵɵrestoreView(_r33).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r40));\n    });\n    i0.ɵɵelement(68, \"i\", 70);\n    i0.ɵɵtext(69, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(70, HouseholdManagementComponent_ng_template_118_button_70_Template, 3, 1, \"button\", 154)(71, HouseholdManagementComponent_ng_template_118_button_71_Template, 3, 1, \"button\", 155);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u5831\\u50F9\\u55AE - \", ctx_r10.currentHouse == null ? null : ctx_r10.currentHouse.CHouseHold, \" (\", ctx_r10.currentHouse == null ? null : ctx_r10.currentHouse.CFloor, \"\\u6A13) \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.quotationItems);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.quotationItems.length === 0);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r10.formatCurrency(ctx_r10.totalAmount));\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r10.formatCurrency(ctx_r10.additionalFeeAmount));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r10.formatCurrency(ctx_r10.finalTotalAmount));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.quotationItems.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n  }\n}\nexport class HouseholdManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, _houseService, _houseHoldMainService, _buildCaseService, pettern, router, _eventService, _ultilityService, quotationService, templateService, spaceTemplateSelectorService) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._houseHoldMainService = _houseHoldMainService;\n    this._buildCaseService = _buildCaseService;\n    this.pettern = pettern;\n    this.router = router;\n    this._eventService = _eventService;\n    this._ultilityService = _ultilityService;\n    this.quotationService = quotationService;\n    this.templateService = templateService;\n    this.spaceTemplateSelectorService = spaceTemplateSelectorService;\n    this.tempBuildCaseID = -1;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.statusOptions = [{\n      value: 0,\n      key: 'allow',\n      label: '允許'\n    }, {\n      value: 1,\n      key: 'not allowed',\n      label: '不允許'\n    }];\n    this.cIsEnableOptions = [{\n      value: null,\n      key: 'all',\n      label: '全部'\n    }, {\n      value: true,\n      key: 'enable',\n      label: '啟用'\n    }, {\n      value: false,\n      key: 'deactivate',\n      label: '停用'\n    }];\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.houseHoldOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.progressOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.houseTypeOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.payStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.signStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.quotationStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.options = {\n      progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\n      payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\n      houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),\n      quotationStatusOptions: this.enumHelper.getEnumOptions(EnumQuotationStatus)\n    };\n    this.initDetail = {\n      CHouseID: 0,\n      CMail: \"\",\n      CIsChange: false,\n      CPayStatus: 0,\n      CIsEnable: false,\n      CCustomerName: \"\",\n      CNationalID: \"\",\n      CProgress: \"\",\n      CHouseType: 0,\n      CHouseHold: \"\",\n      CPhone: \"\"\n    };\n    // 報價單相關\n    this.quotationItems = [];\n    this.totalAmount = 0;\n    // 新增：百分比費用設定\n    this.additionalFeeName = '營業稅'; // 固定名稱\n    this.additionalFeePercentage = 5; // 固定5%\n    this.additionalFeeAmount = 0; // 百分比費用金額\n    this.finalTotalAmount = 0; // 最終總金額（含百分比費用）\n    this.enableAdditionalFee = true; // 固定啟用營業稅\n    this.currentHouse = null;\n    this.currentQuotationId = 0;\n    this.isQuotationEditable = true; // 報價單是否可編輯\n    this.selectedFile = null;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n        this.tempBuildCaseID = res.payload;\n      }\n    })).subscribe();\n  }\n  // 模板匯入相關屬性 - 使用共用元件後簡化\n  // templateList: any[] = [];\n  // templateDetailList: any[] = [];\n  // selectedTemplateForImport: any = null;\n  // templateSearchKeyword: string = '';\n  // templateCurrentPage: number = 1;\n  // templatePageSize: number = 5;\n  // paginatedTemplateList: any[] = [];\n  // templateTotalItems: number = 0;\n  ngOnInit() {\n    this.progressOptions = [...this.progressOptions, ...this.enumHelper.getEnumOptions(EnumHouseProgress)];\n    this.houseTypeOptions = [...this.houseTypeOptions, ...this.enumHelper.getEnumOptions(EnumHouseType)];\n    this.payStatusOptions = [...this.payStatusOptions, ...this.enumHelper.getEnumOptions(EnumPayStatus)];\n    this.signStatusOptions = [...this.signStatusOptions, ...this.enumHelper.getEnumOptions(EnumSignStatus)];\n    this.quotationStatusOptions = [...this.quotationStatusOptions, ...this.enumHelper.getEnumOptions(EnumQuotationStatus)];\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\n        //   : this.buildingSelectedOptions[0],\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value) : this.houseHoldOptions[0],\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value) : this.houseTypeOptions[0],\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value) : this.payStatusOptions[0],\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value) : this.progressOptions[0],\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value) : this.signStatusOptions[0],\n        CQuotationStatusSelected: previous_search.CQuotationStatusSelected != null && previous_search.CQuotationStatusSelected != undefined ? this.quotationStatusOptions.find(x => x.value == previous_search.CQuotationStatusSelected.value) : this.quotationStatusOptions[0],\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value) : this.cIsEnableOptions[0],\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined ? previous_search.CFrom : '',\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined ? previous_search.CTo : ''\n      };\n    } else {\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        // CBuildingNameSelected: this.buildingSelectedOptions[0],\n        CHouseHoldSelected: this.houseHoldOptions[0],\n        CHouseTypeSelected: this.houseTypeOptions[0],\n        CPayStatusSelected: this.payStatusOptions[0],\n        CProgressSelected: this.progressOptions[0],\n        CSignStatusSelected: this.signStatusOptions[0],\n        CQuotationStatusSelected: this.quotationStatusOptions[0],\n        CIsEnableSeleted: this.cIsEnableOptions[0],\n        CFrom: '',\n        CTo: ''\n      };\n    }\n    this.getListBuildCase();\n  }\n  onSearch() {\n    let sessionSave = {\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\n      CFrom: this.searchQuery.CFrom,\n      CTo: this.searchQuery.CTo,\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\n      CProgressSelected: this.searchQuery.CProgressSelected,\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected,\n      CQuotationStatusSelected: this.searchQuery.CQuotationStatusSelected\n    };\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\n    this.getHouseList().subscribe();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getHouseList().subscribe();\n  }\n  exportHouse() {\n    if (this.searchQuery.CBuildCaseSelected.cID) {\n      this._houseService.apiHouseExportHousePost$Json({\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this._ultilityService.downloadExcelFile(res.Entries, '戶別資訊範本');\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  triggerFileInput() {\n    this.fileInput.nativeElement.click();\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files && input.files.length > 0) {\n      this.selectedFile = input.files[0];\n      this.importExcel();\n    }\n  }\n  importExcel() {\n    if (this.selectedFile) {\n      const formData = new FormData();\n      formData.append('CFile', this.selectedFile);\n      this._houseService.apiHouseImportHousePost$Json({\n        body: {\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n          CFile: this.selectedFile\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(res.Message);\n          this.getHouseList().subscribe();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  getListHouseHold() {\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\n      body: {\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldOptions = [{\n          value: '',\n          label: '全部'\n        }, ...res.Entries.map(e => {\n          return {\n            value: e,\n            label: e\n          };\n        })];\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\n            let index = this.houseHoldOptions.findIndex(x => x.value == previous_search.CHouseHoldSelected.value);\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index];\n          } else {\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0];\n          }\n        }\n      }\n    });\n  }\n  formatQuery() {\n    this.bodyRequest = {\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\n      this.bodyRequest['CFloor'] = {\n        CFrom: this.searchQuery.CFrom,\n        CTo: this.searchQuery.CTo\n      };\n    }\n    if (this.searchQuery.CHouseHoldSelected) {\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value;\n    }\n    if (this.searchQuery.CHouseTypeSelected.value) {\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value;\n    }\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value;\n    }\n    if (this.searchQuery.CPayStatusSelected.value) {\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value;\n    }\n    if (this.searchQuery.CProgressSelected.value) {\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value;\n    }\n    if (this.searchQuery.CSignStatusSelected.value) {\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value;\n    }\n    if (this.searchQuery.CQuotationStatusSelected.value) {\n      this.bodyRequest['CQuotationStatus'] = this.searchQuery.CQuotationStatusSelected.value;\n    }\n    return this.bodyRequest;\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    return this._houseService.apiHouseGetHouseListPost$Json({\n      body: this.formatQuery()\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      }\n    }));\n  }\n  onSelectionChangeBuildCase() {\n    // this.getListBuilding()\n    this.getListHouseHold();\n    this.getHouseList().subscribe();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\n          return {\n            CBuildCaseName: res.CBuildCaseName,\n            cID: res.cID\n          };\n        }) : [];\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n          if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.cID == previous_search.CBuildCaseSelected.cID);\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index];\n          } else {\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n          }\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n        }\n      }\n    }), tap(() => {\n      // this.getListBuilding()\n      this.getListHouseHold();\n      setTimeout(() => {\n        this.getHouseList().subscribe();\n      }, 500);\n    })).subscribe();\n  }\n  getHouseById(CID, ref) {\n    this.detailSelected = {};\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: CID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseDetail = {\n          ...res.Entries,\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined\n        };\n        if (res.Entries.CBuildCaseId) {\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId);\n        }\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus);\n        if (res.Entries.CHouseType) {\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType);\n        } else {\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1];\n        }\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress);\n        if (res.Entries.CBuildCaseId) {\n          if (this.houseHoldMain) {\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId;\n          }\n        }\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  findItemInArray(array, key, value) {\n    return array.find(item => item[key] === value);\n  }\n  openModelDetail(ref, item) {\n    this.getHouseById(item.CID, ref);\n  }\n  openModel(ref) {\n    this.houseHoldMain = {\n      CBuildingName: '',\n      CFloor: undefined,\n      CHouseHoldCount: undefined\n    };\n    this.dialogService.open(ref);\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmitDetail(ref) {\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '', this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '', this.editHouseArgsParam = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\n      CChangeStartDate: this.houseDetail.CChangeStartDate,\n      CChangeEndDate: this.houseDetail.CChangeEndDate\n    };\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseService.apiHouseEditHousePost$Json({\n      body: this.editHouseArgsParam\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message);\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  }\n  onSubmit(ref) {\n    let bodyReq = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.houseDetail.CHouseType,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.houseDetail.CPayStatus,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.houseDetail.CProgress\n    };\n    this._houseService.apiHouseEditHousePost$Json({\n      body: bodyReq\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onNavidateId(type, id) {\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID;\n    this.router.navigate([`/pages/household-management/${type}`, idURL]);\n  }\n  onNavidateBuildCaseIdHouseId(type, buildCaseId, houseId) {\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId]);\n  }\n  resetSecureKey(item) {\n    if (confirm(\"您想重設密碼嗎？\")) {\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\n        body: item.CID\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        }\n      });\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建案名稱]', this.houseDetail.CId);\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold);\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50);\n    this.valid.required('[樓層]', this.houseDetail.CFloor);\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50);\n    // if (this.editHouseArgsParam.CNationalID) {\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\n    // }\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern);\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone);\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress);\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value);\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value);\n    if (this.houseDetail.CChangeStartDate) {\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate);\n    }\n    if (this.houseDetail.CChangeEndDate) {\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate);\n    }\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '');\n  }\n  validationHouseHoldMain() {\n    this.valid.clear();\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID);\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName);\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10);\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100);\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100);\n  }\n  addHouseHoldMain(ref) {\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID, this.validationHouseHoldMain();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\n      body: this.houseHoldMain\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  } // 開啟報價單對話框\n  openQuotation(dialog, item) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.currentHouse = item;\n      _this.quotationItems = [];\n      _this.totalAmount = 0;\n      _this.currentQuotationId = 0; // 重置報價單ID\n      _this.isQuotationEditable = true; // 預設可編輯\n      // 重置百分比費用設定（固定營業稅5%）\n      _this.additionalFeeName = '營業稅';\n      _this.additionalFeePercentage = 5;\n      _this.additionalFeeAmount = 0;\n      _this.finalTotalAmount = 0;\n      _this.enableAdditionalFee = true;\n      // 載入現有報價資料\n      try {\n        const response = yield _this.quotationService.getQuotationByHouseId(item.CID).toPromise();\n        if (response && response.StatusCode === 0 && response.Entries) {\n          // 保存當前的報價單ID\n          _this.currentQuotationId = response.Entries.CQuotationVersionId || 0;\n          // 根據 cQuotationStatus 決定是否可編輯\n          if (response.Entries.CQuotationStatus === 2) {\n            // 2: 已報價\n            _this.isQuotationEditable = false;\n          } else {\n            _this.isQuotationEditable = true;\n          }\n          // 載入額外費用設定（固定營業稅5%，不從後端載入）\n          _this.enableAdditionalFee = true;\n          _this.additionalFeeName = '營業稅';\n          _this.additionalFeePercentage = 5;\n          // 檢查 Entries 是否有 Items 陣列\n          if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\n            // 將 API 回傳的資料轉換為 QuotationItem 格式\n            _this.quotationItems = response.Entries.Items.map(entry => ({\n              cHouseID: response.Entries.CHouseID || item.CID,\n              cQuotationID: response.Entries.CQuotationID,\n              cItemName: entry.CItemName || '',\n              cLocation: entry.CLocation || '',\n              cUnit: entry.CUnit || '',\n              cUnitPrice: entry.CUnitPrice || 0,\n              cCount: entry.CCount || 1,\n              cStatus: entry.CStatus || 1,\n              CQuotationItemType: entry.CQuotationItemType && entry.CQuotationItemType > 0 ? entry.CQuotationItemType : CQuotationItemType.自定義,\n              cRemark: entry.CRemark || '',\n              cQuotationStatus: entry.CQuotationStatus\n            }));\n            _this.calculateTotal();\n          } else {}\n        } else {}\n      } catch (error) {\n        console.error('載入報價資料失敗:', error);\n      }\n      _this.dialogService.open(dialog, {\n        context: item,\n        closeOnBackdropClick: false\n      });\n    })();\n  }\n  // 產生新報價單\n  createNewQuotation() {\n    this.currentQuotationId = 0;\n    this.quotationItems = [];\n    this.isQuotationEditable = true;\n    this.totalAmount = 0;\n    this.finalTotalAmount = 0;\n    this.additionalFeeAmount = 0;\n    this.enableAdditionalFee = true;\n    // 顯示成功訊息\n    this.message.showSucessMSG('已產生新報價單，可開始編輯');\n  }\n  // 新增自定義報價項目\n  addQuotationItem() {\n    this.quotationItems.push({\n      cHouseID: this.currentHouse?.CID || 0,\n      cItemName: '',\n      cLocation: '',\n      cUnit: '',\n      cUnitPrice: 0,\n      cCount: 1,\n      cStatus: 1,\n      CQuotationItemType: CQuotationItemType.自定義,\n      cRemark: ''\n    });\n  }\n  // 需求項目匯入處理\n  onRequestItemsImported(config) {\n    console.log('匯入需求項目配置:', config);\n    if (!this.currentHouse) {\n      this.message.showErrorMSG('請先選擇戶別');\n      return;\n    }\n    if (!config.selectedItems || config.selectedItems.length === 0) {\n      this.message.showErrorMSG('未選擇任何需求項目');\n      return;\n    }\n    // 將需求項目轉換為報價項目\n    const newQuotationItems = config.selectedItems.map(item => {\n      return {\n        cHouseID: this.currentHouse.CID,\n        cItemName: item.CRequirement || '未命名需求',\n        cLocation: item.CLocation || '',\n        cUnit: item.CUnit || '',\n        cUnitPrice: item.CUnitPrice || 0,\n        cCount: 1,\n        cStatus: 1,\n        CQuotationItemType: CQuotationItemType.客變需求,\n        cRemark: item.CRemark || ''\n      };\n    });\n    // 將新項目加入到現有報價單中\n    this.quotationItems = [...this.quotationItems, ...newQuotationItems];\n    // 重新計算總金額\n    this.calculateTotal();\n    this.message.showSucessMSG(`成功匯入 ${config.selectedItems.length} 個需求項目`);\n  }\n  onRequestImportError(errorMessage) {\n    this.message.showErrorMSG(errorMessage);\n  }\n  // 載入客變需求\n  loadDefaultItems() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this2.currentHouse?.CID) {\n          _this2.message.showErrorMSG('請先選擇戶別');\n          return;\n        }\n        const request = {\n          CBuildCaseID: _this2.searchQuery?.CBuildCaseSelected?.cID || 0,\n          CHouseID: _this2.currentHouse.CID\n        };\n        const response = yield _this2.quotationService.loadDefaultItems(request).toPromise();\n        if (response?.success && response.data) {\n          const defaultItems = response.data.map(x => ({\n            cHouseID: _this2.currentHouse?.CID,\n            cItemName: x.cItemName,\n            cLocation: x.cLocation || '',\n            cUnit: x.cUnit || '',\n            cUnitPrice: x.cUnitPrice,\n            cCount: x.cCount,\n            cStatus: x.cStatus,\n            CQuotationItemType: CQuotationItemType.客變需求,\n            cRemark: x.cRemark\n          }));\n          _this2.quotationItems.push(...defaultItems);\n          _this2.calculateTotal();\n          _this2.message.showSucessMSG('載入客變需求成功');\n        } else {\n          _this2.message.showErrorMSG(response?.message || '載入客變需求失敗');\n        }\n      } catch (error) {\n        console.error('載入客變需求錯誤:', error);\n        _this2.message.showErrorMSG('載入客變需求失敗');\n      }\n    })();\n  }\n  // 載入選樣資料\n  loadRegularItems() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this3.currentHouse?.CID) {\n          _this3.message.showErrorMSG('請先選擇戶別');\n          return;\n        }\n        const request = {\n          CBuildCaseID: _this3.searchQuery?.CBuildCaseSelected?.cID || 0,\n          CHouseID: _this3.currentHouse.CID\n        };\n        const response = yield _this3.quotationService.loadRegularItems(request).toPromise();\n        if (response?.success && response.data) {\n          const regularItems = response.data.map(x => ({\n            cHouseID: _this3.currentHouse?.CID,\n            cItemName: x.cItemName,\n            cLocation: x.cLocation || '',\n            cUnit: x.cUnit || '',\n            cUnitPrice: x.cUnitPrice,\n            cCount: x.cCount,\n            cStatus: x.cStatus,\n            CQuotationItemType: CQuotationItemType.選樣,\n            // 選樣資料\n            cRemark: x.cRemark || ''\n          }));\n          _this3.quotationItems.push(...regularItems);\n          _this3.calculateTotal();\n          _this3.message.showSucessMSG('載入選樣資料成功');\n        } else {\n          _this3.message.showErrorMSG(response?.message || '載入選樣資料失敗');\n        }\n      } catch (error) {\n        console.error('載入選樣資料錯誤:', error);\n        _this3.message.showErrorMSG('載入選樣資料失敗');\n      }\n    })();\n  }\n  // 移除報價項目\n  removeQuotationItem(index) {\n    const item = this.quotationItems[index];\n    this.quotationItems.splice(index, 1);\n    this.calculateTotal();\n  }\n  // 計算總金額\n  calculateTotal() {\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\n      return sum + item.cUnitPrice * item.cCount;\n    }, 0);\n    this.calculateFinalTotal();\n  }\n  // 計算百分比費用和最終總金額（固定營業稅5%）\n  calculateFinalTotal() {\n    // 固定計算營業稅5%\n    this.additionalFeeAmount = Math.round(this.totalAmount * 0.05);\n    this.finalTotalAmount = this.totalAmount + this.additionalFeeAmount;\n  }\n  // 格式化金額\n  formatCurrency(amount) {\n    return new Intl.NumberFormat('zh-TW', {\n      style: 'currency',\n      currency: 'TWD',\n      minimumFractionDigits: 0\n    }).format(amount);\n  }\n  // 儲存報價單\n  saveQuotation(ref) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (_this4.quotationItems.length === 0) {\n        _this4.message.showErrorMSG('請先新增報價項目');\n        return;\n      }\n      // 驗證必填欄位 (調整：允許單價和數量為負數)\n      const invalidNameItems = _this4.quotationItems.filter(item => !item.cItemName.trim());\n      if (invalidNameItems.length > 0) {\n        _this4.message.showErrorMSG('請確認所有項目名稱都已正確填寫');\n        return;\n      }\n      // 驗證單位欄位必填\n      const invalidUnitItems = _this4.quotationItems.filter(item => !item.cUnit || !item.cUnit.trim());\n      if (invalidUnitItems.length > 0) {\n        _this4.message.showErrorMSG('請確認所有項目的單位都已正確填寫');\n        return;\n      }\n      try {\n        const request = {\n          houseId: _this4.currentHouse.CID,\n          items: _this4.quotationItems,\n          quotationId: _this4.currentQuotationId,\n          // 傳遞當前的報價單ID\n          // 額外費用相關欄位\n          cShowOther: _this4.enableAdditionalFee,\n          // 啟用額外費用\n          cOtherName: _this4.additionalFeeName,\n          // 額外費用名稱\n          cOtherPercent: _this4.additionalFeePercentage // 額外費用百分比\n        };\n        const response = yield _this4.quotationService.saveQuotation(request).toPromise();\n        if (response?.success) {\n          _this4.message.showSucessMSG('報價單儲存成功');\n          ref.close();\n        } else {\n          _this4.message.showErrorMSG(response?.message || '儲存失敗');\n        }\n      } catch (error) {\n        _this4.message.showErrorMSG('報價單儲存失敗');\n      }\n    })();\n  }\n  // 匯出報價單\n  exportQuotation() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield _this5.quotationService.exportQuotation(_this5.currentHouse.CID).toPromise();\n        if (blob) {\n          const url = window.URL.createObjectURL(blob);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = `報價單_${_this5.currentHouse.CHouseHold}_${_this5.currentHouse.CFloor}樓.pdf`;\n          link.click();\n          window.URL.revokeObjectURL(url);\n        } else {\n          _this5.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\n        }\n      } catch (error) {\n        _this5.message.showErrorMSG('匯出報價單失敗');\n      }\n    })();\n  }\n  // 列印報價單\n  printQuotation() {\n    if (this.quotationItems.length === 0) {\n      this.message.showErrorMSG('沒有可列印的報價項目');\n      return;\n    }\n    try {\n      // 建立列印內容\n      const printContent = this.generatePrintContent();\n      // 建立新的視窗進行列印\n      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\n      if (printWindow) {\n        printWindow.document.open();\n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        // 等待內容載入完成後列印\n        printWindow.onload = function () {\n          setTimeout(() => {\n            printWindow.print();\n            // 列印後不自動關閉視窗，讓使用者可以預覽\n          }, 500);\n        };\n      } else {\n        this.message.showErrorMSG('無法開啟列印視窗，請檢查瀏覽器是否阻擋彈出視窗');\n      }\n    } catch (error) {\n      console.error('列印報價單錯誤:', error);\n      this.message.showErrorMSG('列印報價單時發生錯誤');\n    }\n  }\n  // 產生列印內容\n  generatePrintContent() {\n    // 使用導入的模板\n    const template = QUOTATION_TEMPLATE;\n    // 準備數據\n    const currentDate = new Date().toLocaleDateString('zh-TW');\n    const buildCaseName = this.searchQuery.CBuildCaseSelected?.CBuildCaseName || '';\n    // 生成項目HTML\n    let itemsHtml = '';\n    this.quotationItems.forEach((item, index) => {\n      const subtotal = item.cUnitPrice * item.cCount;\n      const quotationType = this.getQuotationTypeText(item.CQuotationItemType);\n      const unit = item.cUnit || '';\n      const location = item.cLocation || '';\n      itemsHtml += `\n          <tr>\n            <td class=\"text-center\">${index + 1}</td>\n            <td class=\"text-center\">${location}</td>\n            <td>${item.cItemName}</td>\n            <td class=\"text-right\">${this.formatCurrency(item.cUnitPrice)}</td>\n            <td class=\"text-center\">${unit}</td>\n            <td class=\"text-center\">${item.cCount}</td>\n            <td class=\"text-right\">${this.formatCurrency(subtotal)}</td>\n            <td class=\"text-center\">${quotationType}</td>\n          </tr>\n        `;\n    });\n    // 生成額外費用HTML\n    const additionalFeeHtml = this.enableAdditionalFee ? `\n        <div class=\"additional-fee\">\n          ${this.additionalFeeName} (${this.additionalFeePercentage}%)：${this.formatCurrency(this.additionalFeeAmount)}\n        </div>\n      ` : '';\n    // 替換模板中的占位符\n    const html = template.replace(/{{buildCaseName}}/g, buildCaseName).replace(/{{houseHold}}/g, this.currentHouse?.CHouseHold || '').replace(/{{floor}}/g, this.currentHouse?.CFloor || '').replace(/{{customerName}}/g, this.currentHouse?.CCustomerName || '').replace(/{{printDate}}/g, currentDate).replace(/{{itemsHtml}}/g, itemsHtml).replace(/{{subtotalAmount}}/g, this.formatCurrency(this.totalAmount)).replace(/{{additionalFeeHtml}}/g, additionalFeeHtml).replace(/{{totalAmount}}/g, this.formatCurrency(this.finalTotalAmount)).replace(/{{printDateTime}}/g, new Date().toLocaleString('zh-TW'));\n    return html;\n  }\n  // 鎖定報價單\n  lockQuotation(ref) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (_this6.quotationItems.length === 0) {\n        _this6.message.showErrorMSG('請先新增報價項目');\n        return;\n      }\n      // 驗證必填欄位\n      const invalidNameItems = _this6.quotationItems.filter(item => !item.cItemName.trim());\n      if (invalidNameItems.length > 0) {\n        _this6.message.showErrorMSG('請確認所有項目名稱都已正確填寫');\n        return;\n      }\n      // 驗證單位欄位必填\n      const invalidUnitItems = _this6.quotationItems.filter(item => !item.cUnit || !item.cUnit.trim());\n      if (invalidUnitItems.length > 0) {\n        _this6.message.showErrorMSG('請確認所有項目的單位都已正確填寫');\n        return;\n      }\n      if (!_this6.currentQuotationId) {\n        _this6.message.showErrorMSG('無效的報價單ID');\n        return;\n      }\n      try {\n        const response = yield _this6.quotationService.lockQuotation(_this6.currentQuotationId).toPromise();\n        if (response.success) {\n          _this6.message.showSucessMSG('報價單已成功鎖定');\n          console.log('報價單鎖定成功:', {\n            quotationId: _this6.currentQuotationId,\n            message: response.message\n          });\n        } else {\n          _this6.message.showErrorMSG(response.message || '報價單鎖定失敗');\n          console.error('報價單鎖定失敗:', response.message);\n        }\n        ref.close();\n      } catch (error) {\n        _this6.message.showErrorMSG('報價單鎖定失敗');\n        console.error('鎖定報價單錯誤:', error);\n      }\n    })();\n  }\n  // 取得報價類型文字\n  getQuotationTypeText(quotationType) {\n    switch (quotationType) {\n      case CQuotationItemType.客變需求:\n        return '客變需求';\n      case CQuotationItemType.自定義:\n        return '客變項目';\n      case CQuotationItemType.選樣:\n        return '選樣';\n      default:\n        return '未知';\n    }\n  }\n  // 空間模板相關方法 - 使用共用元件\n  onSpaceTemplateApplied(config) {\n    console.log('套用空間模板配置:', config);\n    if (!this.currentHouse) {\n      this.message.showErrorMSG('請先選擇戶別');\n      return;\n    }\n    // 將模板項目轉換為報價單項目\n    this.convertTemplatesToQuotationItems(config.selectedItems, config.templateDetails);\n  }\n  onTemplateError(errorMessage) {\n    this.message.showErrorMSG(errorMessage);\n  }\n  // 將模板項目轉換為報價單項目\n  convertTemplatesToQuotationItems(selectedTemplates, templateDetails) {\n    if (!selectedTemplates || selectedTemplates.length === 0) {\n      this.message.showErrorMSG('未選擇任何模板項目');\n      return;\n    }\n    let newQuotationItems = [];\n    // 處理每個模板的明細項目\n    selectedTemplates.forEach(template => {\n      const details = templateDetails.get(template.CTemplateId);\n      if (details && details.length > 0) {\n        details.forEach(detail => {\n          const newItem = {\n            cHouseID: this.currentHouse.CID,\n            // 設定戶別ID\n            cItemName: detail.CRequirement || detail.CPart || '',\n            // 項目名稱，優先使用 CRequirement，否則使用 CPart\n            cLocation: detail.CLocation || '',\n            // 區域欄位對應\n            cUnitPrice: detail.CUnitPrice || 0,\n            cCount: 1,\n            // 預設數量為1\n            cUnit: detail.CUnit || '',\n            CQuotationItemType: CQuotationItemType.客變需求,\n            // 從模板來的項目標記為客變需求\n            cRemark: detail.CRemark || ''\n          };\n          newQuotationItems.push(newItem);\n        });\n      }\n    });\n    if (newQuotationItems.length === 0) {\n      this.message.showErrorMSG('所選模板中沒有有效的項目');\n      return;\n    }\n    // 將新項目加入到現有報價單中\n    this.quotationItems = [...this.quotationItems, ...newQuotationItems];\n    // 重新計算總金額\n    this.calculateTotal();\n    const templateCount = selectedTemplates.length;\n    const itemCount = newQuotationItems.length;\n    this.message.showSucessMSG(`成功載入 ${templateCount} 個模板，共 ${itemCount} 個項目`);\n    console.log('模板轉換完成:', {\n      templateCount,\n      itemCount,\n      newItems: newQuotationItems\n    });\n  }\n  getQuotationStatusText(status) {\n    switch (status) {\n      case EnumQuotationStatus.待報價:\n        return '待報價';\n      case EnumQuotationStatus.已報價:\n        return '已報價';\n      case EnumQuotationStatus.已簽回:\n        return '已簽回';\n      default:\n        return '未知';\n    }\n  }\n  static {\n    this.ɵfac = function HouseholdManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.HouseService), i0.ɵɵdirectiveInject(i6.HouseHoldMainService), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i9.EventService), i0.ɵɵdirectiveInject(i10.UtilityService), i0.ɵɵdirectiveInject(i11.QuotationService), i0.ɵɵdirectiveInject(i6.TemplateService), i0.ɵɵdirectiveInject(i12.SpaceTemplateSelectorService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdManagementComponent,\n      selectors: [[\"ngx-household-management\"]],\n      viewQuery: function HouseholdManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 120,\n      vars: 23,\n      consts: [[\"fileInput\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialogHouseholdMain\", \"\"], [\"dialogQuotation\", \"\"], [\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"cHouseType\", 1, \"label\", \"col-3\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"type\", \"text\", \"id\", \"CFrom\", \"nbInput\", \"\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloorTo\", 1, \"label\", \"col-1\"], [1, \"mr-3\"], [\"type\", \"text\", \"id\", \"CTo\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u6236\\u578B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPayStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7E73\\u6B3E\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cSignStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7C3D\\u56DE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cQuotationStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5831\\u50F9\\u55AE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"me-1\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-building\", \"me-1\"], [1, \"fas\", \"fa-file-export\", \"me-1\"], [\"type\", \"file\", \"accept\", \".xlsx, .xls\", 2, \"display\", \"none\", 3, \"change\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-file-import\", \"me-1\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", 2, \"min-width\", \"1000px\"], [1, \"table-header\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"fas\", \"fa-users-plus\", \"me-1\"], [1, \"w-32\", \"px-0\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"m-[2px]\", 3, \"click\"], [1, \"fas\", \"fa-comments\", \"me-1\"], [1, \"fas\", \"fa-file-image\", \"me-1\"], [1, \"fas\", \"fa-file-signature\", \"me-1\"], [1, \"fas\", \"fa-key\", \"me-1\"], [1, \"fas\", \"fa-file-invoice-dollar\", \"me-1\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"me-1\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [\"class\", \"px-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", \"px-8\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [\"class\", \"btn btn-primary m-2 bg-[#169BD5] px-8\", 3, \"click\", 4, \"ngIf\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"cBuildCaseId\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5EFA\\u6848\\u540D\\u7A31\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u6236\\u578B\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u6A13\\u5C64\", \"min\", \"1\", \"max\", \"100\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cCustomerName\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5BA2\\u6236\\u59D3\\u540D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cNationalId\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8EAB\\u5206\\u8B49\\u5B57\\u865F\", \"maxlength\", \"20\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cMail\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u96FB\\u5B50\\u90F5\\u4EF6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPhone\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u806F\\u7D61\\u96FB\\u8A71\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHouseType\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u6236\\u5225\\u985E\\u578B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"class\", \"form-group\", 4, \"ngIf\"], [\"for\", \"cIsChange\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [1, \"form-group\", \"flex\", \"flex-row\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", \"content-center\", 2, \"min-width\", \"75px\"], [1, \"max-w-xs\", \"flex\", \"flex-row\"], [1, \"w-1/2\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"mr-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"ml-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"cPayStatus\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u4ED8\\u6B3E\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"m-2\", \"bg-[#169BD5]\", \"px-8\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"me-1\"], [\"for\", \"cBuildingName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u68DF\\u5225\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CHouseHoldCount\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u672C\\u68E0\\u7E3D\\u6A13\\u5C64\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"mr-4\", 3, \"click\"], [\"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-save\", \"me-1\"], [2, \"width\", \"1200px\", \"max-height\", \"95vh\"], [\"class\", \"mb-4 d-flex justify-content-between\", 4, \"ngIf\"], [\"class\", \"mb-4 alert alert-warning\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\"], [\"width\", \"12%\"], [\"width\", \"20%\"], [\"width\", \"8%\"], [\"width\", \"15%\"], [\"width\", \"10%\"], [4, \"ngIf\"], [1, \"mt-4\"], [1, \"card\", \"border-0\", \"shadow-sm\"], [1, \"card-body\", \"p-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"h6\", \"mb-0\", \"text-muted\"], [1, \"h5\", \"mb-0\", \"text-dark\", \"fw-bold\"], [1, \"tax-section\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\", \"p-3\", \"bg-light\", \"rounded\"], [1, \"d-flex\", \"align-items-center\"], [1, \"tax-icon-wrapper\", \"me-3\"], [1, \"fas\", \"fa-receipt\", \"text-info\"], [1, \"fw-medium\", \"text-dark\"], [1, \"tax-percentage\", \"ms-1\", \"badge\", \"bg-info\", \"text-white\"], [1, \"small\", \"text-muted\", \"mt-1\"], [1, \"fas\", \"fa-info-circle\", \"me-1\"], [1, \"text-end\"], [1, \"tax-amount\", \"h6\", \"mb-0\", \"text-info\", \"fw-bold\"], [1, \"small\", \"text-muted\"], [1, \"my-3\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"h4\", \"mb-0\", \"text-primary\", \"fw-bold\"], [1, \"d-flex\", \"justify-content-between\"], [\"title\", \"\\u5217\\u5370\\u5831\\u50F9\\u55AE\", 1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"me-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-print\", \"me-1\"], [\"class\", \"btn btn-outline-success btn-sm me-2\", \"title\", \"\\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [\"class\", \"btn btn-warning m-2\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary m-2\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"mb-4\", \"d-flex\", \"justify-content-between\"], [1, \"d-flex\"], [3, \"templateApplied\", \"error\", \"buildCaseId\", \"text\", \"icon\", \"buttonClass\", \"disabled\", \"CTemplateType\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-list-alt\", \"me-1\"], [1, \"fas\", \"fa-palette\", \"me-1\"], [1, \"mb-4\", \"alert\", \"alert-warning\"], [1, \"fas\", \"fa-lock\", \"me-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5340\\u57DF\", \"maxlength\", \"20\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"text\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"number\", \"nbInput\", \"\", \"min\", \"0\", \"step\", \"0.01\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"number\", \"nbInput\", \"\", \"step\", \"0.01\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"text-right\"], [1, \"badge\"], [\"class\", \"btn btn-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-trash\", \"me-1\"], [1, \"text-muted\"], [1, \"fas\", \"fa-lock\"], [\"colspan\", \"8\", 1, \"text-muted\", \"py-4\"], [\"title\", \"\\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE\", 1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-warning\", \"m-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-paper-plane\", \"me-1\"], [1, \"btn\", \"btn-primary\", \"m-2\", 3, \"click\", \"disabled\"]],\n      template: function HouseholdManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 6)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 7);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 8)(7, \"div\", 9)(8, \"div\", 10)(9, \"label\", 11);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function HouseholdManagementComponent_Template_nb_select_selectedChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectionChangeBuildCase());\n          });\n          i0.ɵɵtemplate(12, HouseholdManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\", 10)(15, \"label\", 14);\n          i0.ɵɵtext(16, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseTypeSelected, $event) || (ctx.searchQuery.CHouseTypeSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(18, HouseholdManagementComponent_nb_option_18_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"div\", 16)(21, \"label\", 17);\n          i0.ɵɵtext(22, \"\\u6A13 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"nb-form-field\", 18)(24, \"input\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_24_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"label\", 20);\n          i0.ɵɵtext(26, \"~ \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-form-field\", 21)(28, \"input\", 22);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(29, \"div\", 9);\n          i0.ɵɵelementStart(30, \"div\", 9)(31, \"div\", 10)(32, \"label\", 23);\n          i0.ɵɵtext(33, \" \\u6236\\u578B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"nb-select\", 24);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseHoldSelected, $event) || (ctx.searchQuery.CHouseHoldSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(35, HouseholdManagementComponent_nb_option_35_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 9)(37, \"div\", 10)(38, \"label\", 25);\n          i0.ɵɵtext(39, \" \\u7E73\\u6B3E\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"nb-select\", 26);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_40_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CPayStatusSelected, $event) || (ctx.searchQuery.CPayStatusSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(41, HouseholdManagementComponent_nb_option_41_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(42, \"div\", 9)(43, \"div\", 10)(44, \"label\", 27);\n          i0.ɵɵtext(45, \" \\u9032\\u5EA6 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"nb-select\", 28);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_46_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CProgressSelected, $event) || (ctx.searchQuery.CProgressSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(47, HouseholdManagementComponent_nb_option_47_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"div\", 9)(49, \"div\", 10)(50, \"label\", 29);\n          i0.ɵɵtext(51, \" \\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"nb-select\", 30);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_52_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CIsEnableSeleted, $event) || (ctx.searchQuery.CIsEnableSeleted = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(53, HouseholdManagementComponent_nb_option_53_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"div\", 9)(55, \"div\", 10)(56, \"label\", 31);\n          i0.ɵɵtext(57, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"nb-select\", 32);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_58_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CSignStatusSelected, $event) || (ctx.searchQuery.CSignStatusSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(59, HouseholdManagementComponent_nb_option_59_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(60, \"div\", 9)(61, \"div\", 10)(62, \"label\", 33);\n          i0.ɵɵtext(63, \" \\u5831\\u50F9\\u55AE\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"nb-select\", 34);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_64_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CQuotationStatusSelected, $event) || (ctx.searchQuery.CQuotationStatusSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(65, HouseholdManagementComponent_nb_option_65_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(66, \"div\", 9);\n          i0.ɵɵelementStart(67, \"div\", 35)(68, \"div\", 36)(69, \"button\", 37);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_69_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelement(70, \"i\", 38);\n          i0.ɵɵtext(71, \"\\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(72, \"div\", 35)(73, \"div\", 39);\n          i0.ɵɵtemplate(74, HouseholdManagementComponent_button_74_Template, 3, 0, \"button\", 40);\n          i0.ɵɵelementStart(75, \"button\", 41);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_75_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onNavidateId(\"modify-floor-plan\"));\n          });\n          i0.ɵɵelement(76, \"i\", 42);\n          i0.ɵɵtext(77, \"\\u4FEE\\u6539\\u6A13\\u5C64\\u6236\\u578B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"button\", 41);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_78_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.exportHouse());\n          });\n          i0.ɵɵelement(79, \"i\", 43);\n          i0.ɵɵtext(80, \"\\u532F\\u51FA\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"input\", 44, 0);\n          i0.ɵɵlistener(\"change\", function HouseholdManagementComponent_Template_input_change_81_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelected($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"button\", 45);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_83_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.triggerFileInput());\n          });\n          i0.ɵɵelement(84, \"i\", 46);\n          i0.ɵɵtext(85, \"\\u532F\\u5165\\u66F4\\u65B0\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(86, \"div\", 47)(87, \"table\", 48)(88, \"thead\", 49)(89, \"tr\")(90, \"th\", 50);\n          i0.ɵɵtext(91, \"\\u6236\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"th\", 50);\n          i0.ɵɵtext(93, \"\\u6A13\\u5C64\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"th\", 50);\n          i0.ɵɵtext(95, \"\\u6236\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"th\", 50);\n          i0.ɵɵtext(97, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"th\", 50);\n          i0.ɵɵtext(99, \"\\u9032\\u5EA6\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(100, \"th\", 50);\n          i0.ɵɵtext(101, \"\\u7E73\\u6B3E\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"th\", 50);\n          i0.ɵɵtext(103, \"\\u7C3D\\u56DE\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"th\", 50);\n          i0.ɵɵtext(105, \"\\u5831\\u50F9\\u55AE\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(106, \"th\", 50);\n          i0.ɵɵtext(107, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"th\", 51);\n          i0.ɵɵtext(109, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(110, \"tbody\");\n          i0.ɵɵtemplate(111, HouseholdManagementComponent_tr_111_Template, 36, 13, \"tr\", 52);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(112, \"nb-card-footer\", 53)(113, \"ngb-pagination\", 54);\n          i0.ɵɵtwoWayListener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_113_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_113_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(114, HouseholdManagementComponent_ng_template_114_Template, 7, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(116, HouseholdManagementComponent_ng_template_116_Template, 21, 4, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(118, HouseholdManagementComponent_ng_template_118_Template, 72, 13, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseTypeSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseTypeOptions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CFrom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CTo);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseHoldSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseHoldOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CPayStatusSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.payStatusOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CProgressSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.progressOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CIsEnableSeleted);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.cIsEnableOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CSignStatusSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.signStatusOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CQuotationStatusSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.quotationStatusOptions);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(37);\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseList);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i13.NgForOf, i13.NgIf, SharedModule, i14.DefaultValueAccessor, i14.NumberValueAccessor, i14.NgControlStatus, i14.MaxLengthValidator, i14.MinValidator, i14.MaxValidator, i14.NgModel, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, i3.NbCheckboxComponent, i3.NbInputDirective, i3.NbSelectComponent, i3.NbOptionComponent, i3.NbFormFieldComponent, i3.NbPrefixDirective, i3.NbIconComponent, i3.NbDatepickerDirective, i3.NbDatepickerComponent, i15.NgbPagination, i16.BreadcrumbComponent, i17.BaseLabelDirective, NbDatepickerModule, NbDateFnsDateModule, SpaceTemplateSelectorButtonComponent],\n      styles: [\".card[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;\\n}\\n\\n.tax-section[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa !important;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  position: relative;\\n}\\n.tax-section[_ngcontent-%COMP%]:hover {\\n  background-color: #f3f4f6 !important;\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.tax-section[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background-color: #0891b2;\\n  border-radius: 4px 0 0 4px;\\n  transition: width 0.2s ease;\\n}\\n.tax-section[_ngcontent-%COMP%]:hover::before {\\n  width: 6px;\\n}\\n\\n.tax-icon-wrapper[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background-color: #0891b2;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 2px 8px rgba(8, 145, 178, 0.3);\\n  transition: all 0.2s ease;\\n}\\n.tax-icon-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: white !important;\\n}\\n.tax-icon-wrapper[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  background-color: #0e7490;\\n  box-shadow: 0 4px 12px rgba(8, 145, 178, 0.4);\\n}\\n\\n.tax-percentage[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n.tax-amount[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n  font-weight: 600;\\n}\\n.tax-amount[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.02);\\n  color: #0e7490 !important;\\n}\\n\\n.text-primary[_ngcontent-%COMP%] {\\n  color: #2563eb !important;\\n}\\n\\n.text-info[_ngcontent-%COMP%] {\\n  color: #0891b2 !important;\\n}\\n\\nhr[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e5e7eb;\\n  opacity: 1;\\n  margin: 1rem 0;\\n}\\n\\n.h5[_ngcontent-%COMP%], \\n.h6[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n  font-weight: 600;\\n}\\n\\n.text-primary.fw-bold[_ngcontent-%COMP%] {\\n  color: #2563eb !important;\\n  font-weight: 700 !important;\\n  transition: all 0.2s ease;\\n}\\n.text-primary.fw-bold[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.01);\\n  color: #1d4ed8 !important;\\n}\\n\\n.fa-info-circle[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n  transition: opacity 0.2s ease;\\n}\\n.fa-info-circle[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n\\n@media (max-width: 768px) {\\n  .card-body[_ngcontent-%COMP%] {\\n    padding: 1.5rem !important;\\n  }\\n  .h4[_ngcontent-%COMP%], \\n   .h5[_ngcontent-%COMP%], \\n   .h6[_ngcontent-%COMP%] {\\n    font-size: 1rem !important;\\n  }\\n  .tax-icon-wrapper[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .tax-icon-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .tax-section[_ngcontent-%COMP%] {\\n    padding: 1rem !important;\\n  }\\n  .tax-section[_ngcontent-%COMP%]::before {\\n    width: 3px;\\n  }\\n  .tax-section[_ngcontent-%COMP%]:hover::before {\\n    width: 4px;\\n  }\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  border-bottom: 2px solid #e9ecef;\\n  padding-bottom: 0.5rem;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-group-text[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-color: #ced4da;\\n  color: #6c757d;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-color: #d1d5db;\\n  transition: all 0.2s ease;\\n  border-radius: 6px;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #2563eb;\\n  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);\\n  outline: none;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%] {\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n  background-color: #fff;\\n  min-height: 400px;\\n  max-height: 500px;\\n  overflow-y: auto;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1rem;\\n  border-bottom: 1px solid #f1f3f4;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  transform: translateX(4px);\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item.selected[_ngcontent-%COMP%] {\\n  background-color: #eff6ff;\\n  border-left: 4px solid #2563eb;\\n  transform: translateX(4px);\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item.selected[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%] {\\n  color: #2563eb;\\n  font-weight: 600;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 0.25rem;\\n  transition: color 0.3s ease;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-info[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .template-item.selected[_ngcontent-%COMP%]   .template-actions[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.template-selection-section[_ngcontent-%COMP%]   .template-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n}\\n\\n.template-details-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  border-bottom: 2px solid #e9ecef;\\n  padding-bottom: 0.5rem;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%] {\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n  background-color: #fff;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1rem;\\n  border-bottom: 1px solid #f1f3f4;\\n  display: flex;\\n  align-items: center;\\n  transition: background-color 0.3s ease;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-checkbox[_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n  flex-shrink: 0;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-name[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n  color: #212529;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  flex-wrap: wrap;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  display: flex;\\n  align-items: center;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-meta[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n  opacity: 0.7;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .details-list[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .bulk-actions[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  background-color: #f8f9fa;\\n  border-radius: 0.375rem;\\n  border: 1px solid #e9ecef;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .bulk-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .bulk-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n}\\n.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.875rem;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  border-radius: 0.375rem;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.template-details-section[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .pagination-current[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n  font-size: 0.875rem;\\n  white-space: nowrap;\\n}\\n\\n.btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n  font-size: 0.875rem;\\n}\\n.btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.btn.btn-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.btn.btn-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:hover {\\n  color: white;\\n}\\n.btn.btn-outline-success[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  margin-right: 0.25rem;\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.btn.btn-danger[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.btn.btn-warning[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.btn.btn-primary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.btn.btn-outline-secondary[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  margin: 1px;\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  margin-right: 0.25rem;\\n}\\n\\n.d-flex[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%] {\\n  box-shadow: 0 2px 4px rgba(23, 162, 184, 0.2);\\n  transition: all 0.3s ease;\\n}\\n.d-flex[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.2);\\n  transition: all 0.3s ease;\\n}\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n  border-color: #545b62;\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);\\n}\\n\\nnb-card-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1.5rem;\\n  font-weight: 500;\\n  border-radius: 0.375rem;\\n  transition: all 0.3s ease;\\n}\\nnb-card-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  margin-right: 0.5rem;\\n}\\nnb-card-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "CommonModule", "QUOTATION_TEMPLATE", "NbDatepickerModule", "BaseComponent", "concatMap", "tap", "NbDateFnsDateModule", "moment", "EEvent", "EnumHouseProgress", "EnumHouseType", "EnumPayStatus", "EnumSignStatus", "EnumQuotationStatus", "LocalStorageService", "STORAGE_KEY", "CQuotationItemType", "SpaceTemplateSelectorButtonComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "case_r3", "label", "case_r4", "case_r5", "case_r6", "case_r7", "case_r8", "case_r9", "ɵɵlistener", "HouseholdManagementComponent_button_74_Template_button_click_0_listener", "ɵɵrestoreView", "_r10", "ctx_r10", "ɵɵnextContext", "dialogHouseholdMain_r12", "ɵɵreference", "ɵɵresetView", "openModel", "ɵɵelement", "HouseholdManagementComponent_tr_111_button_20_Template_button_click_0_listener", "_r14", "item_r15", "$implicit", "dialogUpdateHousehold_r16", "openModelDetail", "ɵɵtemplate", "HouseholdManagementComponent_tr_111_button_20_Template", "HouseholdManagementComponent_tr_111_Template_button_click_21_listener", "_r13", "onNavidateBuildCaseIdHouseId", "searchQuery", "CBuildCaseSelected", "cID", "CID", "HouseholdManagementComponent_tr_111_Template_button_click_24_listener", "HouseholdManagementComponent_tr_111_Template_button_click_27_listener", "HouseholdManagementComponent_tr_111_Template_button_click_30_listener", "resetSecureKey", "HouseholdManagementComponent_tr_111_Template_button_click_33_listener", "dialogQuotation_r17", "openQuotation", "ɵɵtextInterpolate", "CHouseHold", "CFloor", "ɵɵtextInterpolate2", "CHouseType", "CIsChange", "CProgressName", "ɵɵtextInterpolate3", "CPayStatus", "CSignStatus", "getQuotationStatusText", "CQuotationStatus", "CIsEnable", "isUpdate", "status_r20", "status_r21", "status_r23", "ɵɵtwoWayListener", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_35_Template_nb_select_ngModelChange_3_listener", "$event", "_r22", "ɵɵtwoWayBindingSet", "detailSelected", "CPayStatusSelected", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_35_nb_option_4_Template", "ɵɵtwoWayProperty", "options", "payStatusOptions", "status_r25", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_36_Template_nb_select_ngModelChange_3_listener", "_r24", "CProgressSelected", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_36_nb_option_4_Template", "progressOptions", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_nb_select_ngModelChange_4_listener", "_r19", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_nb_option_5_Template", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_9_listener", "houseDetail", "CHousehold", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_13_listener", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_17_listener", "CCustomerName", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_21_listener", "CNationalId", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_25_listener", "CMail", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_29_listener", "CPhone", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_nb_select_ngModelChange_33_listener", "CHouseTypeSelected", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_nb_option_34_Template", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_35_Template", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_div_36_Template", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_nb_checkbox_checkedChange_40_listener", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_nb_checkbox_checkedChange_45_listener", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_53_listener", "changeStartDate", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template_input_ngModelChange_58_listener", "changeEndDate", "userBuildCaseOptions", "houseTypeOptions", "isChangePayStatus", "isChangeProgress", "StartDate_r26", "EndDate_r27", "HouseholdManagementComponent_ng_template_114_button_6_Template_button_click_0_listener", "_r29", "ref_r28", "dialogRef", "onSubmitDetail", "HouseholdManagementComponent_ng_template_114_nb_card_body_1_Template", "HouseholdManagementComponent_ng_template_114_Template_button_click_3_listener", "_r18", "onClose", "HouseholdManagementComponent_ng_template_114_button_6_Template", "isCreate", "HouseholdManagementComponent_ng_template_116_button_20_Template_button_click_0_listener", "_r32", "ref_r31", "addHouseHoldMain", "HouseholdManagementComponent_ng_template_116_Template_input_ngModelChange_7_listener", "_r30", "houseHoldMain", "CBuildingName", "HouseholdManagementComponent_ng_template_116_Template_input_ngModelChange_11_listener", "CHouseHoldCount", "HouseholdManagementComponent_ng_template_116_Template_input_ngModelChange_15_listener", "HouseholdManagementComponent_ng_template_116_Template_button_click_17_listener", "HouseholdManagementComponent_ng_template_116_button_20_Template", "HouseholdManagementComponent_ng_template_118_div_4_Template_app_space_template_selector_button_templateApplied_2_listener", "_r34", "onSpaceTemplateApplied", "HouseholdManagementComponent_ng_template_118_div_4_Template_app_space_template_selector_button_error_2_listener", "onTemplateError", "HouseholdManagementComponent_ng_template_118_div_4_Template_button_click_3_listener", "addQuotationItem", "HouseholdManagementComponent_ng_template_118_div_4_Template_button_click_7_listener", "loadDefaultItems", "HouseholdManagementComponent_ng_template_118_div_4_Template_button_click_10_listener", "loadRegularItems", "toString", "HouseholdManagementComponent_ng_template_118_tr_27_button_17_Template_button_click_0_listener", "_r37", "i_r38", "index", "removeQuotationItem", "HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_2_listener", "item_r36", "_r35", "cLocation", "HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_4_listener", "cItemName", "HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_6_listener", "cUnitPrice", "calculateTotal", "HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_8_listener", "cUnit", "HouseholdManagementComponent_ng_template_118_tr_27_Template_input_ngModelChange_10_listener", "cCount", "HouseholdManagementComponent_ng_template_118_tr_27_button_17_Template", "HouseholdManagementComponent_ng_template_118_tr_27_span_18_Template", "ɵɵclassProp", "isQuotationEditable", "formatCurrency", "getQuotationTypeText", "HouseholdManagementComponent_ng_template_118_button_65_Template_button_click_0_listener", "_r39", "createNewQuotation", "HouseholdManagementComponent_ng_template_118_button_70_Template_button_click_0_listener", "_r41", "ref_r40", "lockQuotation", "quotationItems", "length", "HouseholdManagementComponent_ng_template_118_button_71_Template_button_click_0_listener", "_r42", "saveQuotation", "HouseholdManagementComponent_ng_template_118_div_4_Template", "HouseholdManagementComponent_ng_template_118_div_5_Template", "HouseholdManagementComponent_ng_template_118_tr_27_Template", "HouseholdManagementComponent_ng_template_118_tr_28_Template", "HouseholdManagementComponent_ng_template_118_Template_button_click_62_listener", "_r33", "printQuotation", "HouseholdManagementComponent_ng_template_118_button_65_Template", "HouseholdManagementComponent_ng_template_118_Template_button_click_67_listener", "HouseholdManagementComponent_ng_template_118_button_70_Template", "HouseholdManagementComponent_ng_template_118_button_71_Template", "currentHouse", "totalAmount", "additionalFeeAmount", "finalTotalAmount", "HouseholdManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "_houseService", "_houseHoldMainService", "_buildCaseService", "pettern", "router", "_eventService", "_ultilityService", "quotationService", "templateService", "spaceTemplateSelectorService", "tempBuildCaseID", "pageFirst", "pageSize", "pageIndex", "totalRecords", "statusOptions", "value", "key", "cIsEnableOptions", "buildCaseOptions", "houseHoldOptions", "signStatusOptions", "quotationStatusOptions", "getEnumOptions", "initDetail", "CHouseID", "CNationalID", "CProgress", "additionalFeeName", "additionalFeePercentage", "enableAdditionalFee", "currentQuotationId", "selectedFile", "buildingSelectedOptions", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "GetSessionStorage", "HOUSE_SEARCH", "undefined", "previous_search", "JSON", "parse", "CHouseHoldSelected", "find", "x", "CSignStatusSelected", "CQuotationStatusSelected", "CIsEnableSeleted", "CFrom", "CTo", "getListBuildCase", "onSearch", "sessionSave", "AddSessionStorage", "stringify", "getHouseList", "pageChanged", "newPage", "exportHouse", "apiHouseExportHousePost$Json", "CBuildCaseID", "Entries", "StatusCode", "downloadExcelFile", "showErrorMSG", "Message", "triggerFileInput", "fileInput", "nativeElement", "click", "onFileSelected", "event", "input", "target", "files", "importExcel", "formData", "FormData", "append", "apiHouseImportHousePost$Json", "body", "CFile", "showSucessMSG", "getListHouseHold", "apiHouseGetListHouseHoldPost$Json", "map", "e", "findIndex", "formatQuery", "bodyRequest", "PageIndex", "PageSize", "sortByFloorDescending", "arr", "sort", "a", "b", "apiHouseGetHouseListPost$Json", "houseList", "TotalItems", "onSelectionChangeBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "CIsPagi", "CStatus", "setTimeout", "getHouseById", "ref", "apiHouseGetHouseByIdPost$Json", "CChangeStartDate", "Date", "CChangeEndDate", "CBuildCaseId", "findItemInArray", "open", "array", "item", "formatDate", "CChangeDate", "format", "editHouseArgsParam", "CId", "validation", "errorMessages", "showErrorMSGs", "apiHouseEditHousePost$Json", "close", "onSubmit", "bodyReq", "onNavidateId", "type", "id", "idURL", "navigate", "buildCaseId", "houseId", "confirm", "apiHouseResetHouseSecureKeyPost$Json", "clear", "required", "isStringMaxLength", "pattern", "MailPettern", "isPhoneNumber", "checkStartBeforeEnd", "validationHouseHoldMain", "isNaturalNumberInRange", "apiHouseHoldMainAddHouseHoldMainPost$Json", "dialog", "_this", "_asyncToGenerator", "response", "getQuotationByHouseId", "to<PERSON>romise", "CQuotationVersionId", "Items", "Array", "isArray", "entry", "cHouseID", "cQuotationID", "CQuotationID", "CItemName", "CLocation", "CUnit", "CUnitPrice", "CCount", "cStatus", "自定義", "cRemark", "CRemark", "cQuotationStatus", "error", "console", "context", "closeOnBackdropClick", "push", "onRequestItemsImported", "config", "log", "selectedItems", "newQuotationItems", "CRequirement", "客變需求", "onRequestImportError", "errorMessage", "_this2", "request", "success", "data", "defaultItems", "_this3", "regularItems", "選樣", "splice", "reduce", "sum", "calculateFinalTotal", "Math", "round", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "_this4", "invalidNameItems", "filter", "trim", "invalidUnitItems", "items", "quotationId", "cShowOther", "cOtherName", "cOtherPercent", "exportQuotation", "_this5", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "revokeObjectURL", "printContent", "generatePrintContent", "printWindow", "write", "onload", "print", "template", "currentDate", "toLocaleDateString", "buildCaseName", "itemsHtml", "for<PERSON>ach", "subtotal", "quotationType", "unit", "location", "additionalFeeHtml", "html", "replace", "toLocaleString", "_this6", "convertTemplatesToQuotationItems", "templateDetails", "selectedTemplates", "details", "get", "CTemplateId", "detail", "newItem", "<PERSON>art", "templateCount", "itemCount", "newItems", "status", "待報價", "已報價", "已簽回", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "HouseService", "HouseHoldMainService", "BuildCaseService", "i7", "PetternHelper", "i8", "Router", "i9", "EventService", "i10", "UtilityService", "i11", "QuotationService", "TemplateService", "i12", "SpaceTemplateSelectorService", "selectors", "viewQuery", "HouseholdManagementComponent_Query", "rf", "ctx", "HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "HouseholdManagementComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "HouseholdManagementComponent_nb_option_12_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener", "HouseholdManagementComponent_nb_option_18_Template", "HouseholdManagementComponent_Template_input_ngModelChange_24_listener", "HouseholdManagementComponent_Template_input_ngModelChange_28_listener", "HouseholdManagementComponent_Template_nb_select_ngModelChange_34_listener", "HouseholdManagementComponent_nb_option_35_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_40_listener", "HouseholdManagementComponent_nb_option_41_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_46_listener", "HouseholdManagementComponent_nb_option_47_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_52_listener", "HouseholdManagementComponent_nb_option_53_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_58_listener", "HouseholdManagementComponent_nb_option_59_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_64_listener", "HouseholdManagementComponent_nb_option_65_Template", "HouseholdManagementComponent_Template_button_click_69_listener", "HouseholdManagementComponent_button_74_Template", "HouseholdManagementComponent_Template_button_click_75_listener", "HouseholdManagementComponent_Template_button_click_78_listener", "HouseholdManagementComponent_Template_input_change_81_listener", "HouseholdManagementComponent_Template_button_click_83_listener", "HouseholdManagementComponent_tr_111_Template", "HouseholdManagementComponent_Template_ngb_pagination_pageChange_113_listener", "HouseholdManagementComponent_ng_template_114_Template", "ɵɵtemplateRefExtractor", "HouseholdManagementComponent_ng_template_116_Template", "HouseholdManagementComponent_ng_template_118_Template", "i13", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i14", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "MinValidator", "MaxValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i15", "NgbPagination", "i16", "BreadcrumbComponent", "i17", "BaseLabelDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\household-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\household-management.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { QUOTATION_TEMPLATE } from 'src/assets/template/quotation-template';\r\nimport { NbDatepickerModule, NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseHoldMainService, HouseService, TemplateService } from 'src/services/api/services';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\n// import { TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { AddHouseHoldMain, EditHouseArgs, GetHouseListArgs, GetHouseListRes, TblHouse, TemplateGetListArgs, TemplateDetailItem, GetTemplateDetailByIdArgs } from 'src/services/api/models';\r\nimport { concatMap, tap } from 'rxjs';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport * as moment from 'moment';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\r\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\r\nimport { EnumQuotationStatus } from 'src/app/shared/enum/enumQuotationStatus';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { QuotationItem, CQuotationItemType } from 'src/app/models/quotation.model';\r\nimport { QuotationService } from 'src/app/services/quotation.service';\r\nimport { SpaceTemplateSelectorService } from 'src/app/shared/components/space-template-selector/space-template-selector.service';\r\nimport { SpaceTemplateConfig } from 'src/app/shared/components/space-template-selector/space-template-selector.component';\r\nimport { SpaceTemplateSelectorButtonComponent } from 'src/app/shared/components/space-template-selector/space-template-selector-button.component';\r\nimport { RequestItemImportButtonComponent } from 'src/app/shared/components/request-item-import/request-item-import-button.component';\r\nimport { RequestItemImportConfig } from 'src/app/shared/components/request-item-import/request-item-import.component';\r\n\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\n// interface HouseDetailExtension {\r\n//   changeStartDate: string;\r\n//   changeEndDate: string;\r\n// }\r\nexport interface SearchQuery {\r\n  CBuildCaseSelected?: any | null;\r\n  CHouseTypeSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CHouseHoldSelected?: any | null;\r\n  CPayStatusSelected?: any | null;\r\n  CProgressSelected?: any | null;\r\n  CSignStatusSelected?: any | null;\r\n  CQuotationStatusSelected?: any | null;\r\n  CIsEnableSeleted?: any | null;\r\n  CFrom?: any | null;\r\n  CTo?: any | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-household-management',\r\n  templateUrl: './household-management.component.html',\r\n  styleUrls: ['./household-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbDatepickerModule, NbDateFnsDateModule, SpaceTemplateSelectorButtonComponent, RequestItemImportButtonComponent],\r\n})\r\n\r\nexport class HouseholdManagementComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseID: number = -1\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _houseHoldMainService: HouseHoldMainService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private _eventService: EventService,\r\n    private _ultilityService: UtilityService,\r\n    private quotationService: QuotationService,\r\n    private templateService: TemplateService,\r\n    private spaceTemplateSelectorService: SpaceTemplateSelectorService\r\n  ) {\r\n    super(_allow)\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.tempBuildCaseID = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  cIsEnableOptions = [\r\n    {\r\n      value: null,\r\n      key: 'all',\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: true,\r\n      key: 'enable',\r\n      label: '啟用',\r\n    },\r\n    {\r\n      value: false,\r\n      key: 'deactivate',\r\n      label: '停用',\r\n    }\r\n  ]\r\n\r\n  searchQuery: SearchQuery\r\n  detailSelected: SearchQuery\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n  houseHoldOptions: any[] = [{ label: '全部', value: '' }]\r\n  progressOptions: any[] = [{ label: '全部', value: -1 }]\r\n  houseTypeOptions: any[] = [{ label: '全部', value: -1 }]\r\n  payStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n  signStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n  quotationStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n\r\n  options = {\r\n    progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\r\n    payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\r\n    houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),\r\n    quotationStatusOptions: this.enumHelper.getEnumOptions(EnumQuotationStatus),\r\n  }\r\n\r\n  userBuildCaseOptions: any\r\n  initDetail = {\r\n    CHouseID: 0,\r\n    CMail: \"\",\r\n    CIsChange: false,\r\n    CPayStatus: 0,\r\n    CIsEnable: false,\r\n    CCustomerName: \"\",\r\n    CNationalID: \"\",\r\n    CProgress: \"\",\r\n    CHouseType: 0,\r\n    CHouseHold: \"\",\r\n    CPhone: \"\"\r\n  }\r\n  // 報價單相關\r\n  quotationItems: QuotationItem[] = [];\r\n  totalAmount: number = 0;\r\n  // 新增：百分比費用設定\r\n  additionalFeeName: string = '營業稅';  // 固定名稱\r\n  additionalFeePercentage: number = 5;   // 固定5%\r\n  additionalFeeAmount: number = 0;       // 百分比費用金額\r\n  finalTotalAmount: number = 0;          // 最終總金額（含百分比費用）\r\n  enableAdditionalFee: boolean = true;   // 固定啟用營業稅\r\n  currentHouse: any = null;\r\n  currentQuotationId: number = 0;\r\n  isQuotationEditable: boolean = true; // 報價單是否可編輯\r\n\r\n  // 模板匯入相關屬性 - 使用共用元件後簡化\r\n  // templateList: any[] = [];\r\n  // templateDetailList: any[] = [];\r\n  // selectedTemplateForImport: any = null;\r\n  // templateSearchKeyword: string = '';\r\n  // templateCurrentPage: number = 1;\r\n  // templatePageSize: number = 5;\r\n  // paginatedTemplateList: any[] = [];\r\n  // templateTotalItems: number = 0;\r\n\r\n  override ngOnInit(): void {\r\n    this.progressOptions = [\r\n      ...this.progressOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseProgress)\r\n    ]\r\n    this.houseTypeOptions = [\r\n      ...this.houseTypeOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseType)\r\n    ]\r\n    this.payStatusOptions = [\r\n      ...this.payStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumPayStatus)\r\n    ]\r\n    this.signStatusOptions = [\r\n      ...this.signStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumSignStatus)\r\n    ]\r\n    this.quotationStatusOptions = [\r\n      ...this.quotationStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumQuotationStatus)\r\n    ]\r\n\r\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\r\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\r\n        //   : this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined\r\n          ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value)\r\n          : this.houseHoldOptions[0],\r\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined\r\n          ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value)\r\n          : this.houseTypeOptions[0],\r\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined\r\n          ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value)\r\n          : this.payStatusOptions[0],\r\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined\r\n          ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value)\r\n          : this.progressOptions[0],\r\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined\r\n          ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value)\r\n          : this.signStatusOptions[0],\r\n        CQuotationStatusSelected: previous_search.CQuotationStatusSelected != null && previous_search.CQuotationStatusSelected != undefined\r\n          ? this.quotationStatusOptions.find(x => x.value == previous_search.CQuotationStatusSelected.value)\r\n          : this.quotationStatusOptions[0],\r\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined\r\n          ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value)\r\n          : this.cIsEnableOptions[0],\r\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined\r\n          ? previous_search.CFrom\r\n          : '',\r\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined\r\n          ? previous_search.CTo\r\n          : ''\r\n      }\r\n    }\r\n    else {\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        // CBuildingNameSelected: this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: this.houseHoldOptions[0],\r\n        CHouseTypeSelected: this.houseTypeOptions[0],\r\n        CPayStatusSelected: this.payStatusOptions[0],\r\n        CProgressSelected: this.progressOptions[0],\r\n        CSignStatusSelected: this.signStatusOptions[0],\r\n        CQuotationStatusSelected: this.quotationStatusOptions[0],\r\n        CIsEnableSeleted: this.cIsEnableOptions[0],\r\n        CFrom: '',\r\n        CTo: ''\r\n      }\r\n    }\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  onSearch() {\r\n    let sessionSave = {\r\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\r\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\r\n      CFrom: this.searchQuery.CFrom,\r\n      CTo: this.searchQuery.CTo,\r\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\r\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\r\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\r\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\r\n      CProgressSelected: this.searchQuery.CProgressSelected,\r\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected,\r\n      CQuotationStatusSelected: this.searchQuery.CQuotationStatusSelected\r\n    }\r\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  exportHouse() {\r\n    if (this.searchQuery.CBuildCaseSelected.cID) {\r\n      this._houseService.apiHouseExportHousePost$Json({\r\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this._ultilityService.downloadExcelFile(\r\n            res.Entries, '戶別資訊範本'\r\n          )\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  selectedFile: File | null = null;\r\n  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;\r\n\r\n\r\n  triggerFileInput(): void {\r\n    this.fileInput.nativeElement.click();\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      this.selectedFile = input.files[0];\r\n      this.importExcel();\r\n    }\r\n  }\r\n\r\n  importExcel(): void {\r\n    if (this.selectedFile) {\r\n      const formData = new FormData();\r\n      formData.append('CFile', this.selectedFile);\r\n      this._houseService.apiHouseImportHousePost$Json({\r\n        body: {\r\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n          CFile: this.selectedFile\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(res.Message!);\r\n          this.getHouseList().subscribe()\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  getListHouseHold() {\r\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\r\n      body: { CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseHoldOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\r\n            let index = this.houseHoldOptions.findIndex((x: any) => x.value == previous_search.CHouseHoldSelected.value)\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index]\r\n          } else {\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0]\r\n          }\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  houseList: any\r\n  bodyRequest: GetHouseListArgs\r\n\r\n  formatQuery() {\r\n    this.bodyRequest = {\r\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    }\r\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\r\n      this.bodyRequest['CFloor'] = { CFrom: this.searchQuery.CFrom, CTo: this.searchQuery.CTo }\r\n    }\r\n    if (this.searchQuery.CHouseHoldSelected) {\r\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value\r\n    }\r\n    if (this.searchQuery.CHouseTypeSelected.value) {\r\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value\r\n    }\r\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\r\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value\r\n    }\r\n    if (this.searchQuery.CPayStatusSelected.value) {\r\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value\r\n    }\r\n    if (this.searchQuery.CProgressSelected.value) {\r\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value\r\n    }\r\n    if (this.searchQuery.CSignStatusSelected.value) {\r\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value\r\n    }\r\n    if (this.searchQuery.CQuotationStatusSelected.value) {\r\n      this.bodyRequest['CQuotationStatus'] = this.searchQuery.CQuotationStatusSelected.value\r\n    }\r\n\r\n    return this.bodyRequest\r\n  }\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    return this._houseService.apiHouseGetHouseListPost$Json({\r\n      body: this.formatQuery()\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseList = res.Entries;\r\n          this.totalRecords = res.TotalItems!;\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n\r\n  userBuildCaseSelected: any\r\n  onSelectionChangeBuildCase() {\r\n    // this.getListBuilding()\r\n    this.getListHouseHold()\r\n    this.getHouseList().subscribe()\r\n  }\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\r\n            return {\r\n              CBuildCaseName: res.CBuildCaseName,\r\n              cID: res.cID\r\n            }\r\n          }) : []\r\n\r\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n            if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\r\n              let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == previous_search.CBuildCaseSelected.cID)\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index]\r\n            } else {\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n            }\r\n          }\r\n          else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n          }\r\n        }\r\n      }),\r\n      tap(() => {\r\n        // this.getListBuilding()\r\n        this.getListHouseHold()\r\n        setTimeout(() => {\r\n          this.getHouseList().subscribe();\r\n        }, 500)\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  buildingSelected: any\r\n\r\n  buildingSelectedOptions: any[] = [\r\n    {\r\n      value: '', label: '全部'\r\n    }\r\n  ]\r\n\r\n  // getListBuilding() {\r\n  //   this._houseService.apiHouseGetListBuildingPost$Json({\r\n  //     body: {\r\n  //       CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n  //     }\r\n  //   }).subscribe(res => {\r\n  //     if (res.Entries && res.StatusCode == 0) {\r\n  //       this.buildingSelectedOptions = [{\r\n  //         value: '', label: '全部'\r\n  //       }, ...res.Entries.map(e => {\r\n  //         return { value: e, label: e }\r\n  //       })]\r\n  //       if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n  //         let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n  //         if (previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined) {\r\n  //           let index = this.buildingSelectedOptions.findIndex((x: any) => x.value == previous_search.CBuildingNameSelected.value)\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[index]\r\n  //         } else {\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //         }\r\n  //       }\r\n  //       else {\r\n  //         this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //       }\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  houseDetail: TblHouse & {\r\n    changeStartDate?: any;\r\n    changeEndDate?: any\r\n  }\r\n\r\n  getHouseById(CID: any, ref: any) {\r\n    this.detailSelected = {}\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: { CHouseID: CID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseDetail = {\r\n          ...res.Entries,\r\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\r\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined,\r\n        }\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId)\r\n        }\r\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus)\r\n        if (res.Entries.CHouseType) {\r\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType)\r\n        } else {\r\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1]\r\n        }\r\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress)\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          if (this.houseHoldMain) {\r\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId\r\n          }\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n\r\n    })\r\n  }\r\n\r\n\r\n  findItemInArray(array: any[], key: string, value: any) {\r\n    return array.find(item => item[key] === value);\r\n  }\r\n\r\n\r\n  openModelDetail(ref: any, item: any) {\r\n    this.getHouseById(item.CID, ref)\r\n  }\r\n\r\n  openModel(ref: any) {\r\n    this.houseHoldMain = {\r\n      CBuildingName: '',\r\n      CFloor: undefined,\r\n      CHouseHoldCount: undefined\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  editHouseArgsParam: EditHouseArgs\r\n\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmitDetail(ref: any) {\r\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '',\r\n      this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '',\r\n\r\n      this.editHouseArgsParam = {\r\n        CCustomerName: this.houseDetail.CCustomerName,\r\n        CHouseHold: this.houseDetail.CHousehold,\r\n        CHouseID: this.houseDetail.CId,\r\n        CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\r\n        CIsChange: this.houseDetail.CIsChange,\r\n        CIsEnable: this.houseDetail.CIsEnable,\r\n        CMail: this.houseDetail.CMail,\r\n        CNationalID: this.houseDetail.CNationalId,\r\n        CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\r\n        CPhone: this.houseDetail.CPhone,\r\n        CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\r\n        CChangeStartDate: this.houseDetail.CChangeStartDate,\r\n        CChangeEndDate: this.houseDetail.CChangeEndDate\r\n      }\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: this.editHouseArgsParam\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }\r\n\r\n\r\n  onSubmit(ref: any) {\r\n    let bodyReq: EditHouseArgs = {\r\n      CCustomerName: this.houseDetail.CCustomerName,\r\n      CHouseHold: this.houseDetail.CHousehold,\r\n      CHouseID: this.houseDetail.CId,\r\n      CHouseType: this.houseDetail.CHouseType,\r\n      CIsChange: this.houseDetail.CIsChange,\r\n      CIsEnable: this.houseDetail.CIsEnable,\r\n      CMail: this.houseDetail.CMail,\r\n      CNationalID: this.houseDetail.CNationalId,\r\n      CPayStatus: this.houseDetail.CPayStatus,\r\n      CPhone: this.houseDetail.CPhone,\r\n      CProgress: this.houseDetail.CProgress,\r\n    }\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: bodyReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      }\r\n    })\r\n\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  onNavidateId(type: any, id?: any) {\r\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID\r\n    this.router.navigate([`/pages/household-management/${type}`, idURL])\r\n  }\r\n\r\n  onNavidateBuildCaseIdHouseId(type: any, buildCaseId: any, houseId: any) {\r\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId])\r\n  }\r\n\r\n  resetSecureKey(item: any) {\r\n    if (confirm(\"您想重設密碼嗎？\")) {\r\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\r\n        body: item.CID\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  houseHoldMain: AddHouseHoldMain\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案名稱]', this.houseDetail.CId)\r\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold)\r\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50)\r\n    this.valid.required('[樓層]', this.houseDetail.CFloor)\r\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50)\r\n    // if (this.editHouseArgsParam.CNationalID) {\r\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\r\n    // }\r\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern)\r\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone)\r\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress)\r\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value)\r\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value)\r\n    if (this.houseDetail.CChangeStartDate) {\r\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate)\r\n    }\r\n    if (this.houseDetail.CChangeEndDate) {\r\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate)\r\n    }\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '')\r\n  }\r\n\r\n  validationHouseHoldMain() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID)\r\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName)\r\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10)\r\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100)\r\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100)\r\n  }\r\n\r\n\r\n  addHouseHoldMain(ref: any) {\r\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID,\r\n      this.validationHouseHoldMain()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\r\n      body: this.houseHoldMain\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }  // 開啟報價單對話框\r\n  async openQuotation(dialog: any, item: any) {\r\n    this.currentHouse = item;\r\n    this.quotationItems = [];\r\n    this.totalAmount = 0;\r\n    this.currentQuotationId = 0; // 重置報價單ID\r\n    this.isQuotationEditable = true; // 預設可編輯\r\n    // 重置百分比費用設定（固定營業稅5%）\r\n    this.additionalFeeName = '營業稅';\r\n    this.additionalFeePercentage = 5;\r\n    this.additionalFeeAmount = 0;\r\n    this.finalTotalAmount = 0;\r\n    this.enableAdditionalFee = true;\r\n\r\n    // 載入現有報價資料\r\n    try {\r\n      const response = await this.quotationService.getQuotationByHouseId(item.CID).toPromise();\r\n\r\n      if (response && response.StatusCode === 0 && response.Entries) {\r\n        // 保存當前的報價單ID\r\n        this.currentQuotationId = response.Entries.CQuotationVersionId || 0;\r\n        // 根據 cQuotationStatus 決定是否可編輯\r\n        if (response.Entries.CQuotationStatus === 2) { // 2: 已報價\r\n          this.isQuotationEditable = false;\r\n        } else {\r\n          this.isQuotationEditable = true;\r\n        }\r\n\r\n        // 載入額外費用設定（固定營業稅5%，不從後端載入）\r\n        this.enableAdditionalFee = true;\r\n        this.additionalFeeName = '營業稅';\r\n        this.additionalFeePercentage = 5;\r\n\r\n        // 檢查 Entries 是否有 Items 陣列\r\n        if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\r\n          // 將 API 回傳的資料轉換為 QuotationItem 格式\r\n          this.quotationItems = response.Entries.Items.map((entry: any) => ({\r\n            cHouseID: response.Entries.CHouseID || item.CID,\r\n            cQuotationID: response.Entries.CQuotationID,\r\n            cItemName: entry.CItemName || '',\r\n            cLocation: entry.CLocation || '',\r\n            cUnit: entry.CUnit || '',\r\n            cUnitPrice: entry.CUnitPrice || 0,\r\n            cCount: entry.CCount || 1,\r\n            cStatus: entry.CStatus || 1,\r\n            CQuotationItemType: entry.CQuotationItemType && entry.CQuotationItemType > 0 ? entry.CQuotationItemType : CQuotationItemType.自定義,\r\n            cRemark: entry.CRemark || '',\r\n            cQuotationStatus: entry.CQuotationStatus\r\n          }));\r\n          this.calculateTotal();\r\n        } else {\r\n\r\n        }\r\n      } else {\r\n\r\n      }\r\n    } catch (error) {\r\n      console.error('載入報價資料失敗:', error);\r\n    }\r\n\r\n    this.dialogService.open(dialog, {\r\n      context: item,\r\n      closeOnBackdropClick: false\r\n    });\r\n  }\r\n\r\n  // 產生新報價單\r\n  createNewQuotation() {\r\n    this.currentQuotationId = 0;\r\n    this.quotationItems = [];\r\n    this.isQuotationEditable = true;\r\n    this.totalAmount = 0;\r\n    this.finalTotalAmount = 0;\r\n    this.additionalFeeAmount = 0;\r\n    this.enableAdditionalFee = true;\r\n\r\n    // 顯示成功訊息\r\n    this.message.showSucessMSG('已產生新報價單，可開始編輯');\r\n  }\r\n  // 新增自定義報價項目\r\n  addQuotationItem() {\r\n    this.quotationItems.push({\r\n      cHouseID: this.currentHouse?.CID || 0,\r\n      cItemName: '',\r\n      cLocation: '',\r\n      cUnit: '',\r\n      cUnitPrice: 0,\r\n      cCount: 1,\r\n      cStatus: 1,\r\n      CQuotationItemType: CQuotationItemType.自定義,\r\n      cRemark: ''\r\n    });\r\n  }\r\n\r\n  // 需求項目匯入處理\r\n  onRequestItemsImported(config: RequestItemImportConfig) {\r\n    console.log('匯入需求項目配置:', config);\r\n\r\n    if (!this.currentHouse) {\r\n      this.message.showErrorMSG('請先選擇戶別');\r\n      return;\r\n    }\r\n\r\n    if (!config.selectedItems || config.selectedItems.length === 0) {\r\n      this.message.showErrorMSG('未選擇任何需求項目');\r\n      return;\r\n    }\r\n\r\n    // 將需求項目轉換為報價項目\r\n    const newQuotationItems: QuotationItem[] = config.selectedItems.map(item => {\r\n      return {\r\n        cHouseID: this.currentHouse.CID,\r\n        cItemName: item.CRequirement || '未命名需求',\r\n        cLocation: item.CLocation || '',\r\n        cUnit: item.CUnit || '',\r\n        cUnitPrice: item.CUnitPrice || 0,\r\n        cCount: 1,\r\n        cStatus: 1,\r\n        CQuotationItemType: CQuotationItemType.客變需求,\r\n        cRemark: item.CRemark || ''\r\n      };\r\n    });\r\n\r\n    // 將新項目加入到現有報價單中\r\n    this.quotationItems = [...this.quotationItems, ...newQuotationItems];\r\n\r\n    // 重新計算總金額\r\n    this.calculateTotal();\r\n\r\n    this.message.showSucessMSG(`成功匯入 ${config.selectedItems.length} 個需求項目`);\r\n  }\r\n\r\n  onRequestImportError(errorMessage: string) {\r\n    this.message.showErrorMSG(errorMessage);\r\n  }\r\n  // 載入客變需求\r\n  async loadDefaultItems() {\r\n    try {\r\n      if (!this.currentHouse?.CID) {\r\n        this.message.showErrorMSG('請先選擇戶別');\r\n        return;\r\n      }\r\n\r\n      const request = {\r\n        CBuildCaseID: this.searchQuery?.CBuildCaseSelected?.cID || 0,\r\n        CHouseID: this.currentHouse.CID\r\n      };\r\n\r\n      const response = await this.quotationService.loadDefaultItems(request).toPromise();\r\n      if (response?.success && response.data) {\r\n        const defaultItems = response.data.map((x: any) => ({\r\n          cHouseID: this.currentHouse?.CID,\r\n          cItemName: x.cItemName,\r\n          cLocation: x.cLocation || '',\r\n          cUnit: x.cUnit || '',\r\n          cUnitPrice: x.cUnitPrice,\r\n          cCount: x.cCount,\r\n          cStatus: x.cStatus,\r\n          CQuotationItemType: CQuotationItemType.客變需求,\r\n          cRemark: x.cRemark\r\n        }));\r\n        this.quotationItems.push(...defaultItems);\r\n        this.calculateTotal();\r\n        this.message.showSucessMSG('載入客變需求成功');\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '載入客變需求失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入客變需求錯誤:', error);\r\n      this.message.showErrorMSG('載入客變需求失敗');\r\n    }\r\n  }\r\n\r\n  // 載入選樣資料\r\n  async loadRegularItems() {\r\n    try {\r\n      if (!this.currentHouse?.CID) {\r\n        this.message.showErrorMSG('請先選擇戶別');\r\n        return;\r\n      }\r\n\r\n      const request = {\r\n        CBuildCaseID: this.searchQuery?.CBuildCaseSelected?.cID || 0,\r\n        CHouseID: this.currentHouse.CID\r\n      };\r\n\r\n      const response = await this.quotationService.loadRegularItems(request).toPromise();\r\n      if (response?.success && response.data) {\r\n        const regularItems = response.data.map((x: any) => ({\r\n          cHouseID: this.currentHouse?.CID,\r\n          cItemName: x.cItemName,\r\n          cLocation: x.cLocation || '',\r\n          cUnit: x.cUnit || '',\r\n          cUnitPrice: x.cUnitPrice,\r\n          cCount: x.cCount,\r\n          cStatus: x.cStatus,\r\n          CQuotationItemType: CQuotationItemType.選樣, // 選樣資料\r\n          cRemark: x.cRemark || ''\r\n        }));\r\n        this.quotationItems.push(...regularItems);\r\n        this.calculateTotal();\r\n        this.message.showSucessMSG('載入選樣資料成功');\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '載入選樣資料失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入選樣資料錯誤:', error);\r\n      this.message.showErrorMSG('載入選樣資料失敗');\r\n    }\r\n  }\r\n\r\n  // 移除報價項目\r\n  removeQuotationItem(index: number) {\r\n    const item = this.quotationItems[index];\r\n    this.quotationItems.splice(index, 1);\r\n    this.calculateTotal();\r\n  }\r\n\r\n  // 計算總金額\r\n  calculateTotal() {\r\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\r\n      return sum + (item.cUnitPrice * item.cCount);\r\n    }, 0);\r\n    this.calculateFinalTotal();\r\n  }\r\n\r\n  // 計算百分比費用和最終總金額（固定營業稅5%）\r\n  calculateFinalTotal() {\r\n    // 固定計算營業稅5%\r\n    this.additionalFeeAmount = Math.round(this.totalAmount * 0.05);\r\n    this.finalTotalAmount = this.totalAmount + this.additionalFeeAmount;\r\n  }\r\n\r\n  // 格式化金額\r\n  formatCurrency(amount: number): string {\r\n    return new Intl.NumberFormat('zh-TW', {\r\n      style: 'currency',\r\n      currency: 'TWD',\r\n      minimumFractionDigits: 0\r\n    }).format(amount);\r\n  }\r\n\r\n\r\n\r\n  // 儲存報價單\r\n  async saveQuotation(ref: any) {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('請先新增報價項目');\r\n      return;\r\n    }\r\n\r\n    // 驗證必填欄位 (調整：允許單價和數量為負數)\r\n    const invalidNameItems = this.quotationItems.filter(item =>\r\n      !item.cItemName.trim()\r\n    );\r\n\r\n    if (invalidNameItems.length > 0) {\r\n      this.message.showErrorMSG('請確認所有項目名稱都已正確填寫');\r\n      return;\r\n    }\r\n\r\n    // 驗證單位欄位必填\r\n    const invalidUnitItems = this.quotationItems.filter(item =>\r\n      !item.cUnit || !item.cUnit.trim()\r\n    );\r\n\r\n    if (invalidUnitItems.length > 0) {\r\n      this.message.showErrorMSG('請確認所有項目的單位都已正確填寫');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const request = {\r\n        houseId: this.currentHouse.CID,\r\n        items: this.quotationItems,\r\n        quotationId: this.currentQuotationId, // 傳遞當前的報價單ID\r\n        // 額外費用相關欄位\r\n        cShowOther: this.enableAdditionalFee, // 啟用額外費用\r\n        cOtherName: this.additionalFeeName,   // 額外費用名稱\r\n        cOtherPercent: this.additionalFeePercentage // 額外費用百分比\r\n      };\r\n\r\n      const response = await this.quotationService.saveQuotation(request).toPromise();\r\n      if (response?.success) {\r\n        this.message.showSucessMSG('報價單儲存成功');\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '儲存失敗');\r\n      }\r\n    } catch (error) {\r\n      this.message.showErrorMSG('報價單儲存失敗');\r\n    }\r\n  }\r\n\r\n  // 匯出報價單\r\n  async exportQuotation() {\r\n    try {\r\n      const blob: Blob | undefined = await this.quotationService.exportQuotation(this.currentHouse.CID).toPromise();\r\n      if (blob) {\r\n        const url = window.URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = `報價單_${this.currentHouse.CHouseHold}_${this.currentHouse.CFloor}樓.pdf`;\r\n        link.click();\r\n        window.URL.revokeObjectURL(url);\r\n      } else {\r\n        this.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\r\n      }\r\n    } catch (error) {\r\n      this.message.showErrorMSG('匯出報價單失敗');\r\n    }\r\n  }\r\n\r\n  // 列印報價單\r\n  printQuotation() {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('沒有可列印的報價項目');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // 建立列印內容\r\n      const printContent = this.generatePrintContent();\r\n\r\n      // 建立新的視窗進行列印\r\n      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\r\n      if (printWindow) {\r\n        printWindow.document.open();\r\n        printWindow.document.write(printContent);\r\n        printWindow.document.close();\r\n\r\n        // 等待內容載入完成後列印\r\n        printWindow.onload = function () {\r\n          setTimeout(() => {\r\n            printWindow.print();\r\n            // 列印後不自動關閉視窗，讓使用者可以預覽\r\n          }, 500);\r\n        };\r\n      } else {\r\n        this.message.showErrorMSG('無法開啟列印視窗，請檢查瀏覽器是否阻擋彈出視窗');\r\n      }\r\n    } catch (error) {\r\n      console.error('列印報價單錯誤:', error);\r\n      this.message.showErrorMSG('列印報價單時發生錯誤');\r\n    }\r\n  }\r\n\r\n  // 產生列印內容\r\n  private generatePrintContent(): string {\r\n    // 使用導入的模板\r\n    const template = QUOTATION_TEMPLATE;\r\n\r\n    // 準備數據\r\n    const currentDate = new Date().toLocaleDateString('zh-TW');\r\n    const buildCaseName = this.searchQuery.CBuildCaseSelected?.CBuildCaseName || '';\r\n\r\n    // 生成項目HTML\r\n    let itemsHtml = '';\r\n    this.quotationItems.forEach((item, index) => {\r\n      const subtotal = item.cUnitPrice * item.cCount;\r\n      const quotationType = this.getQuotationTypeText(item.CQuotationItemType);\r\n      const unit = item.cUnit || '';\r\n      const location = item.cLocation || '';\r\n      itemsHtml += `\r\n          <tr>\r\n            <td class=\"text-center\">${index + 1}</td>\r\n            <td class=\"text-center\">${location}</td>\r\n            <td>${item.cItemName}</td>\r\n            <td class=\"text-right\">${this.formatCurrency(item.cUnitPrice)}</td>\r\n            <td class=\"text-center\">${unit}</td>\r\n            <td class=\"text-center\">${item.cCount}</td>\r\n            <td class=\"text-right\">${this.formatCurrency(subtotal)}</td>\r\n            <td class=\"text-center\">${quotationType}</td>\r\n          </tr>\r\n        `;\r\n    });\r\n\r\n    // 生成額外費用HTML\r\n    const additionalFeeHtml = this.enableAdditionalFee ? `\r\n        <div class=\"additional-fee\">\r\n          ${this.additionalFeeName} (${this.additionalFeePercentage}%)：${this.formatCurrency(this.additionalFeeAmount)}\r\n        </div>\r\n      ` : '';\r\n\r\n    // 替換模板中的占位符\r\n    const html = template\r\n      .replace(/{{buildCaseName}}/g, buildCaseName)\r\n      .replace(/{{houseHold}}/g, this.currentHouse?.CHouseHold || '')\r\n      .replace(/{{floor}}/g, this.currentHouse?.CFloor || '')\r\n      .replace(/{{customerName}}/g, this.currentHouse?.CCustomerName || '')\r\n      .replace(/{{printDate}}/g, currentDate)\r\n      .replace(/{{itemsHtml}}/g, itemsHtml)\r\n      .replace(/{{subtotalAmount}}/g, this.formatCurrency(this.totalAmount))\r\n      .replace(/{{additionalFeeHtml}}/g, additionalFeeHtml)\r\n      .replace(/{{totalAmount}}/g, this.formatCurrency(this.finalTotalAmount))\r\n      .replace(/{{printDateTime}}/g, new Date().toLocaleString('zh-TW'));\r\n\r\n    return html;\r\n  }\r\n\r\n\r\n  // 鎖定報價單\r\n  async lockQuotation(ref: any) {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('請先新增報價項目');\r\n      return;\r\n    }\r\n\r\n    // 驗證必填欄位\r\n    const invalidNameItems = this.quotationItems.filter(item =>\r\n      !item.cItemName.trim()\r\n    );\r\n\r\n    if (invalidNameItems.length > 0) {\r\n      this.message.showErrorMSG('請確認所有項目名稱都已正確填寫');\r\n      return;\r\n    }\r\n\r\n    // 驗證單位欄位必填\r\n    const invalidUnitItems = this.quotationItems.filter(item =>\r\n      !item.cUnit || !item.cUnit.trim()\r\n    );\r\n\r\n    if (invalidUnitItems.length > 0) {\r\n      this.message.showErrorMSG('請確認所有項目的單位都已正確填寫');\r\n      return;\r\n    }\r\n\r\n    if (!this.currentQuotationId) {\r\n      this.message.showErrorMSG('無效的報價單ID');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await this.quotationService.lockQuotation(this.currentQuotationId).toPromise();\r\n\r\n      if (response.success) {\r\n        this.message.showSucessMSG('報價單已成功鎖定');\r\n        console.log('報價單鎖定成功:', {\r\n          quotationId: this.currentQuotationId,\r\n          message: response.message\r\n        });\r\n      } else {\r\n        this.message.showErrorMSG(response.message || '報價單鎖定失敗');\r\n        console.error('報價單鎖定失敗:', response.message);\r\n      }\r\n\r\n      ref.close();\r\n    } catch (error) {\r\n      this.message.showErrorMSG('報價單鎖定失敗');\r\n      console.error('鎖定報價單錯誤:', error);\r\n    }\r\n  }\r\n\r\n  // 取得報價類型文字\r\n  getQuotationTypeText(quotationType: CQuotationItemType): string {\r\n    switch (quotationType) {\r\n      case CQuotationItemType.客變需求:\r\n        return '客變需求';\r\n      case CQuotationItemType.自定義:\r\n        return '客變項目';\r\n      case CQuotationItemType.選樣:\r\n        return '選樣';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n  // 空間模板相關方法 - 使用共用元件\r\n  onSpaceTemplateApplied(config: SpaceTemplateConfig) {\r\n    console.log('套用空間模板配置:', config);\r\n\r\n    if (!this.currentHouse) {\r\n      this.message.showErrorMSG('請先選擇戶別');\r\n      return;\r\n    }\r\n\r\n    // 將模板項目轉換為報價單項目\r\n    this.convertTemplatesToQuotationItems(config.selectedItems, config.templateDetails);\r\n  }\r\n\r\n  onTemplateError(errorMessage: string) {\r\n    this.message.showErrorMSG(errorMessage);\r\n  }\r\n\r\n  // 將模板項目轉換為報價單項目\r\n  private convertTemplatesToQuotationItems(selectedTemplates: any[], templateDetails: Map<number, any[]>) {\r\n    if (!selectedTemplates || selectedTemplates.length === 0) {\r\n      this.message.showErrorMSG('未選擇任何模板項目');\r\n      return;\r\n    }\r\n\r\n    let newQuotationItems: QuotationItem[] = [];\r\n\r\n    // 處理每個模板的明細項目\r\n    selectedTemplates.forEach(template => {\r\n      const details = templateDetails.get(template.CTemplateId);\r\n      if (details && details.length > 0) {\r\n        details.forEach(detail => {\r\n          const newItem: QuotationItem = {\r\n            cHouseID: this.currentHouse.CID, // 設定戶別ID\r\n            cItemName: detail.CRequirement || detail.CPart || '', // 項目名稱，優先使用 CRequirement，否則使用 CPart\r\n            cLocation: detail.CLocation || '', // 區域欄位對應\r\n            cUnitPrice: detail.CUnitPrice || 0,\r\n            cCount: 1, // 預設數量為1\r\n            cUnit: detail.CUnit || '',\r\n            CQuotationItemType: CQuotationItemType.客變需求, // 從模板來的項目標記為客變需求\r\n            cRemark: detail.CRemark || ''\r\n          };\r\n          newQuotationItems.push(newItem);\r\n        });\r\n      }\r\n    });\r\n\r\n    if (newQuotationItems.length === 0) {\r\n      this.message.showErrorMSG('所選模板中沒有有效的項目');\r\n      return;\r\n    }\r\n\r\n    // 將新項目加入到現有報價單中\r\n    this.quotationItems = [...this.quotationItems, ...newQuotationItems];\r\n\r\n    // 重新計算總金額\r\n    this.calculateTotal();\r\n\r\n    const templateCount = selectedTemplates.length;\r\n    const itemCount = newQuotationItems.length;\r\n    this.message.showSucessMSG(`成功載入 ${templateCount} 個模板，共 ${itemCount} 個項目`);\r\n\r\n    console.log('模板轉換完成:', {\r\n      templateCount,\r\n      itemCount,\r\n      newItems: newQuotationItems\r\n    });\r\n  }\r\n\r\n  getQuotationStatusText(status: number): string {\r\n    switch (status) {\r\n      case EnumQuotationStatus.待報價:\r\n        return '待報價';\r\n      case EnumQuotationStatus.已報價:\r\n        return '已報價';\r\n      case EnumQuotationStatus.已簽回:\r\n        return '已簽回';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header> <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\" class=\"col-9\"\r\n            (selectedChange)=\"onSelectionChangeBuildCase()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cHouseType\" class=\"label col-3\">類型</label>\r\n          <nb-select [(ngModel)]=\"searchQuery.CHouseTypeSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of houseTypeOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">樓\r\n          </label>\r\n          <nb-form-field class=\"ml-3\">\r\n            <input type=\"text\" id=\"CFrom\" nbInput class=\"w-full col-4\" [(ngModel)]=\"searchQuery.CFrom\">\r\n          </nb-form-field>\r\n          <label for=\"cFloorTo\" class=\"label col-1\">~\r\n          </label>\r\n          <nb-form-field class=\"mr-3\">\r\n            <input type=\"text\" id=\"CTo\" nbInput class=\"w-full\" [(ngModel)]=\"searchQuery.CTo\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">\r\n            棟別\r\n          </label>\r\n          <nb-select [(ngModel)]=\"searchQuery.CBuildingNameSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of buildingSelectedOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div> -->\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cHousehold\" class=\"label col-3\">\r\n            戶型\r\n          </label>\r\n          <nb-select placeholder=\"戶型\" [(ngModel)]=\"searchQuery.CHouseHoldSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of houseHoldOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cPayStatus\" class=\"label col-3\">\r\n            繳款狀態\r\n          </label>\r\n          <nb-select placeholder=\"繳款狀態\" [(ngModel)]=\"searchQuery.CPayStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of payStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cProgress\" class=\"label col-3\">\r\n            進度\r\n          </label>\r\n          <nb-select placeholder=\"進度\" [(ngModel)]=\"searchQuery.CProgressSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of progressOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cStatus\" class=\"label col-3\">\r\n            狀態\r\n          </label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"searchQuery.CIsEnableSeleted\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of cIsEnableOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cSignStatus\" class=\"label col-3\">\r\n            簽回狀態\r\n          </label>\r\n          <nb-select placeholder=\"簽回狀態\" [(ngModel)]=\"searchQuery.CSignStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of signStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cQuotationStatus\" class=\"label col-3\">\r\n            報價單狀態\r\n          </label>\r\n          <nb-select placeholder=\"報價單狀態\" [(ngModel)]=\"searchQuery.CQuotationStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of quotationStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->\r\n      </div>\r\n\r\n      <!-- 查詢按鈕移到這裡，放在搜尋條件的右下角 -->\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-2 mb-3\">\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search me-1\"></i>查詢\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-3\">\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openModel(dialogHouseholdMain)\">\r\n            <i class=\"fas fa-users-plus me-1\"></i>批次新增戶別資料\r\n          </button>\r\n          <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"onNavidateId('modify-floor-plan')\">\r\n            <i class=\"fas fa-building me-1\"></i>修改樓層戶型\r\n          </button>\r\n          <!-- <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"onNavidateId('standard-house-plan')\">\r\n            3.設定戶型標準圖\r\n          </button> -->\r\n          <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"exportHouse()\">\r\n            <i class=\"fas fa-file-export me-1\"></i>匯出戶別明細檔\r\n          </button>\r\n          <input type=\"file\" #fileInput style=\"display:none\" (change)=\"onFileSelected($event)\" accept=\".xlsx, .xls\" />\r\n          <button class=\"btn btn-info btn-sm\" (click)=\"triggerFileInput()\">\r\n            <i class=\"fas fa-file-import me-1\"></i>匯入更新戶別明細檔\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table\" style=\"min-width: 1000px;\">\r\n        <thead class=\"table-header\">\r\n          <tr>\r\n            <!-- <th scope=\"col\" class=\"col-1\">棟別</th> -->\r\n            <th scope=\"col\" class=\"col-1\">戶型</th>\r\n            <th scope=\"col\" class=\"col-1\">樓層</th>\r\n            <th scope=\"col\" class=\"col-1\">戶別</th>\r\n            <th scope=\"col\" class=\"col-1\">類型</th>\r\n            <th scope=\"col\" class=\"col-1\">進度</th>\r\n            <th scope=\"col\" class=\"col-1\">繳款狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">簽回狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">報價單狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-4\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of houseList ; let i = index\">\r\n            <!-- <td>{{ item.CBuildingName}}</td> -->\r\n            <td>{{ item.CHouseHold}}</td>\r\n            <td>{{ item.CFloor}}</td>\r\n            <td>\r\n              {{ item.CHouseType === 2 ? '銷售戶' : ''}}\r\n              {{item.CHouseType === 1 ? '地主戶' : ''}}\r\n            </td>\r\n            <td>{{ item.CIsChange === true ? '客變' : (item.CIsChange === false ? '標準' :'') }}</td>\r\n            <td>{{ item.CProgressName}}</td>\r\n            <td>\r\n              {{item.CPayStatus === 0 ? '未付款': ''}}\r\n              {{item.CPayStatus === 1 ? '已付款': ''}}\r\n              {{item.CPayStatus === 2 ? '無須付款': ''}}\r\n            </td>\r\n            <td>{{ (item.CSignStatus === 0 || item.CSignStatus == null) ? '未簽回' : '已簽回' }}</td>\r\n            <td>{{ getQuotationStatusText(item.CQuotationStatus) }}</td>\r\n            <td>{{ item.CIsEnable ? '啟用' : '停用'}}</td>\r\n            <td class=\"w-32 px-0\">\r\n              <button *ngIf=\"isUpdate\" class=\"btn btn-outline-success btn-sm text-left m-[2px]\"\r\n                (click)=\"openModelDetail(dialogUpdateHousehold, item)\">\r\n                <i class=\"fas fa-edit me-1\"></i>編輯\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('customer-change-picture', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                <i class=\"fas fa-comments me-1\"></i>洽談紀錄\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('sample-selection-result', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                <i class=\"fas fa-file-image me-1\"></i>客變確認圖說\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('finaldochouse_management', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                <i class=\"fas fa-file-signature me-1\"></i>簽署文件歷程\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\" (click)=\"resetSecureKey(item)\">\r\n                <i class=\"fas fa-key me-1\"></i>重置密碼\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\" (click)=\"openQuotation(dialogQuotation, item)\">\r\n                <i class=\"fas fa-file-invoice-dollar me-1\"></i>報價單\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <!-- <nb-card-header>\r\n    </nb-card-header> -->\r\n    <nb-card-body class=\"px-4\" *ngIf=\"houseDetail\">\r\n      <div class=\"form-group\">\r\n        <label for=\"cBuildCaseId\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          建案名稱\r\n        </label>\r\n        <nb-select placeholder=\"建案名稱\" [(ngModel)]=\"detailSelected.CBuildCaseSelected\" class=\"w-full\" disabled=\"true\">\r\n          <nb-option *ngFor=\"let status of userBuildCaseOptions\" [value]=\"status\">\r\n            {{ status.CBuildCaseName }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cHousehold\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          戶型名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"戶型名稱\" [(ngModel)]=\"houseDetail.CHousehold\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cFloor\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          樓層\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"樓層\" [(ngModel)]=\"houseDetail.CFloor\" min=\"1\" max=\"100\"\r\n          disabled=\"true\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cCustomerName\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          客戶姓名\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"客戶姓名\" [(ngModel)]=\"houseDetail.CCustomerName\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cNationalId\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          身分證字號\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"身分證字號\" [(ngModel)]=\"houseDetail.CNationalId\"\r\n          maxlength=\"20\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cMail\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          電子郵件\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"電子郵件\" [(ngModel)]=\"houseDetail.CMail\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cPhone\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          聯絡電話\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"聯絡電話\" [(ngModel)]=\"houseDetail.CPhone\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cHouseType\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          戶別類型\r\n        </label>\r\n        <nb-select placeholder=\"戶別類型\" [(ngModel)]=\"detailSelected.CHouseTypeSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.houseTypeOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\" *ngIf=\"isChangePayStatus\">\r\n        <label for=\"cPayStatus\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          付款狀態\r\n        </label>\r\n        <nb-select placeholder=\"付款狀態\" [(ngModel)]=\"detailSelected.CPayStatusSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.payStatusOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group\" *ngIf=\"isChangeProgress\">\r\n        <label for=\"cProgress\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          進度\r\n        </label>\r\n        <nb-select placeholder=\"進度\" [(ngModel)]=\"detailSelected.CProgressSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.progressOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsChange\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          是否客變\r\n        </label>\r\n        <nb-checkbox status=\"basic\" [(checked)]=\"houseDetail.CIsChange\">是\r\n        </nb-checkbox>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsEnable\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          是否啟用\r\n        </label>\r\n        <nb-checkbox status=\"basic\" [(checked)]=\"houseDetail.CIsEnable\">是\r\n        </nb-checkbox>\r\n      </div>\r\n\r\n      <div class=\"form-group flex flex-row\">\r\n        <label for=\"cIsEnable\" class=\"mr-4 content-center\" style=\"min-width:75px\" baseLabel>\r\n          客變時段\r\n        </label>\r\n        <div class=\"max-w-xs flex flex-row\">\r\n          <nb-form-field class=\"w-1/2\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"StartDate\" placeholder=\"yyyy-mm-dd\" [nbDatepicker]=\"StartDate\"\r\n              class=\"w-[42%] mr-2\" [(ngModel)]=\"houseDetail.changeStartDate\">\r\n            <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <nb-form-field class=\"w-1/2\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"EndDate\" placeholder=\"yyyy-mm-dd\" [nbDatepicker]=\"EndDate\"\r\n              class=\"w-[42%] ml-2\" [(ngModel)]=\"houseDetail.changeEndDate\">\r\n            <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-outline-secondary m-2 px-8\" (click)=\"onClose(ref)\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary m-2 bg-[#169BD5] px-8\" *ngIf=\"isCreate\" (click)=\"onSubmitDetail(ref)\">\r\n        <i class=\"fas fa-check me-1\"></i>送出\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialogHouseholdMain let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 》批次新增戶別資料\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group\">\r\n        <label for=\"cBuildingName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          棟別\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"棟別\" [(ngModel)]=\"houseHoldMain.CBuildingName\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"CHouseHoldCount\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>當層最多戶數\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"當層最多戶數\" [(ngModel)]=\"houseHoldMain.CHouseHoldCount\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"CFloor\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>本棠總樓層\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"本棠總樓層\" [(ngModel)]=\"houseHoldMain.CFloor\" />\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-primary mr-4\" (click)=\"onClose(ref)\">\r\n        <i class=\"fas fa-times me-1\"></i>關閉\r\n      </button>\r\n      <button class=\"btn btn-primary\" *ngIf=\"isCreate\" (click)=\"addHouseHoldMain(ref)\">\r\n        <i class=\"fas fa-save me-1\"></i>儲存\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 報價單對話框 -->\r\n<ng-template #dialogQuotation let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:1200px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      報價單 - {{ currentHouse?.CHouseHold }} ({{ currentHouse?.CFloor }}樓)\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <!-- 只有報價單可編輯時才顯示操作按鈕 -->\r\n      <div *ngIf=\"isQuotationEditable\" class=\"mb-4 d-flex justify-content-between\">\r\n        <div class=\"d-flex\">\r\n          <app-space-template-selector-button [buildCaseId]=\"searchQuery.CBuildCaseSelected?.cID?.toString() || ''\"\r\n            [text]=\"'模板新增'\" [icon]=\"'fas fa-layer-group'\" [buttonClass]=\"'btn btn-warning btn-sm me-2'\"\r\n            [disabled]=\"!searchQuery.CBuildCaseSelected?.cID\" (templateApplied)=\"onSpaceTemplateApplied($event)\"\r\n            (error)=\"onTemplateError($event)\" [CTemplateType]=\"2\">\r\n          </app-space-template-selector-button>\r\n          <button class=\"btn btn-success btn-sm\" (click)=\"addQuotationItem()\">\r\n            <i class=\"fas fa-plus me-1\"></i>新增自定義項目\r\n          </button>\r\n        </div>\r\n        <div>\r\n          <button class=\"btn btn-secondary btn-sm me-2\" (click)=\"loadDefaultItems()\">\r\n            <i class=\"fas fa-list-alt me-1\"></i>載入客變需求\r\n          </button>\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"loadRegularItems()\">\r\n            <i class=\"fas fa-palette me-1\"></i>載入選樣資料\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 報價單已鎖定時的提示 -->\r\n      <div *ngIf=\"!isQuotationEditable\" class=\"mb-4 alert alert-warning\">\r\n        <i class=\"fas fa-lock me-2\"></i>\r\n        <strong>報價單已鎖定</strong> - 此報價單已鎖定，無法進行修改。您可以列印此報價單或產生新的報價單。\r\n      </div>\r\n\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table\">\r\n          <thead class=\"table-header\">\r\n            <tr>\r\n              <th width=\"12%\">區域</th>\r\n              <th width=\"20%\">項目名稱</th>\r\n              <th width=\"12%\">單價 (元)</th>\r\n              <th width=\"8%\">單位</th>\r\n              <th width=\"8%\">數量</th>\r\n              <th width=\"15%\">小計 (元)</th>\r\n              <th width=\"10%\">類型</th>\r\n              <th width=\"10%\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let item of quotationItems; let i = index\">\r\n              <td>\r\n                <input type=\"text\" nbInput [(ngModel)]=\"item.cLocation\"\r\n                  [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\"\r\n                  class=\"w-full\" placeholder=\"區域\"\r\n                  [class.bg-light]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\"\r\n                  maxlength=\"20\">\r\n              </td>\r\n              <td>\r\n                <input type=\"text\" nbInput [(ngModel)]=\"item.cItemName\"\r\n                  [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\"\r\n                  class=\"w-full\"\r\n                  [class.bg-light]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td>\r\n                <input type=\"number\" nbInput [(ngModel)]=\"item.cUnitPrice\" (ngModelChange)=\"calculateTotal()\"\r\n                  class=\"w-full\" min=\"0\" step=\"0.01\" [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td>\r\n                <input type=\"text\" nbInput [(ngModel)]=\"item.cUnit\"\r\n                  [disabled]=\"!isQuotationEditable ||  item.CQuotationItemType === 3\" class=\"w-full\" placeholder=\"單位\"\r\n                  [class.bg-light]=\"!isQuotationEditable ||  item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td>\r\n                <input type=\"number\" nbInput [(ngModel)]=\"item.cCount\" (ngModelChange)=\"calculateTotal()\" class=\"w-full\"\r\n                  step=\"0.01\" [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td class=\"text-right\">\r\n                {{ formatCurrency(item.cUnitPrice * item.cCount) }}\r\n              </td>\r\n              <td>\r\n                <span class=\"badge\" [class.badge-primary]=\"item.CQuotationItemType === 1\"\r\n                  [class.badge-info]=\"item.CQuotationItemType === 3\"\r\n                  [class.badge-secondary]=\"item.CQuotationItemType !== 1 && item.CQuotationItemType !== 3\">\r\n                  {{ getQuotationTypeText(item.CQuotationItemType) }}\r\n                </span>\r\n              </td>\r\n              <td>\r\n                <button *ngIf=\"isQuotationEditable\" class=\"btn btn-danger btn-sm\" (click)=\"removeQuotationItem(i)\">\r\n                  <i class=\"fas fa-trash me-1\"></i>刪除\r\n                </button>\r\n                <span *ngIf=\"!isQuotationEditable\" class=\"text-muted\">\r\n                  <i class=\"fas fa-lock\"></i>\r\n                </span>\r\n              </td>\r\n            </tr>\r\n            <tr *ngIf=\"quotationItems.length === 0\">\r\n              <td colspan=\"8\" class=\"text-muted py-4\">\r\n                請點擊「新增自定義項目」或「載入客變需求」開始建立報價單\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 金額計算區塊 -->\r\n      <div class=\"mt-4\">\r\n        <div class=\"card border-0 shadow-sm\">\r\n          <div class=\"card-body p-4\">\r\n            <!-- 小計 -->\r\n            <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n              <span class=\"h6 mb-0 text-muted\">小計</span>\r\n              <span class=\"h5 mb-0 text-dark fw-bold\">{{ formatCurrency(totalAmount) }}</span>\r\n            </div>\r\n\r\n            <!-- 營業稅 -->\r\n            <div class=\"tax-section d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded\">\r\n              <div class=\"d-flex align-items-center\">\r\n                <div class=\"tax-icon-wrapper me-3\">\r\n                  <i class=\"fas fa-receipt text-info\"></i>\r\n                </div>\r\n                <div>\r\n                  <span class=\"fw-medium text-dark\">營業稅</span>\r\n                  <span class=\"tax-percentage ms-1 badge bg-info text-white\">5%</span>\r\n                  <div class=\"small text-muted mt-1\">\r\n                    <i class=\"fas fa-info-circle me-1\"></i>\r\n                    固定為小計金額的5%\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"text-end\">\r\n                <div class=\"tax-amount h6 mb-0 text-info fw-bold\">{{ formatCurrency(additionalFeeAmount) }}</div>\r\n                <div class=\"small text-muted\">含稅金額</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 分隔線 -->\r\n            <hr class=\"my-3\">\r\n\r\n            <!-- 總金額 -->\r\n            <div class=\"d-flex justify-content-between align-items-center\">\r\n              <span class=\"h5 mb-0 text-dark fw-bold\">總金額</span>\r\n              <span class=\"h4 mb-0 text-primary fw-bold\">{{ formatCurrency(finalTotalAmount) }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between\">\r\n      <div>\r\n        <button class=\"btn btn-outline-info btn-sm me-2\" (click)=\"printQuotation()\"\r\n          [disabled]=\"quotationItems.length === 0\" title=\"列印報價單\">\r\n          <i class=\"fas fa-print me-1\"></i> 列印報價單\r\n        </button>\r\n        <!-- 報價單已鎖定時才顯示 -->\r\n        <button *ngIf=\"!isQuotationEditable\" class=\"btn btn-outline-success btn-sm me-2\" (click)=\"createNewQuotation()\"\r\n          title=\"產生新報價單\">\r\n          <i class=\"fas fa-plus me-1\"></i> 產生新報價單\r\n        </button>\r\n        <!-- <button class=\"btn btn-outline-info btn-sm\" (click)=\"exportQuotation()\"\r\n          [disabled]=\"quotationItems.length === 0\">\r\n          匯出報價單\r\n        </button> -->\r\n      </div>\r\n      <div>\r\n        <button class=\"btn btn-outline-secondary m-2\" (click)=\"onClose(ref)\">\r\n          <i class=\"fas fa-times me-1\"></i>取消\r\n        </button>\r\n        <!-- 只有在報價單可編輯時才顯示鎖定和儲存按鈕 -->\r\n        <button *ngIf=\"isQuotationEditable\" class=\"btn btn-warning m-2\" (click)=\"lockQuotation(ref)\"\r\n          [disabled]=\"quotationItems.length === 0\">\r\n          <i class=\"fas fa-paper-plane me-1\"></i>送出報價單\r\n        </button>\r\n        <button *ngIf=\"isQuotationEditable\" class=\"btn btn-primary m-2\" (click)=\"saveQuotation(ref)\"\r\n          [disabled]=\"quotationItems.length === 0\">\r\n          <i class=\"fas fa-save me-1\"></i>暫存報價單\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": ";AACA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,kBAAkB,QAAyB,gBAAgB;AAOpE,SAASC,aAAa,QAAQ,kCAAkC;AAGhE,SAASC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AACrC,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,MAAM,QAA8B,uCAAuC;AAEpF,SAASC,iBAAiB,QAAQ,uCAAuC;AAEzE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAAwBC,kBAAkB,QAAQ,gCAAgC;AAIlF,SAASC,oCAAoC,QAAQ,4FAA4F;;;;;;;;;;;;;;;;;;;;;;ICpBrIC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IAQAR,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAK,OAAA,CAAc;IAC7DT,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,OAAA,CAAAC,KAAA,MACF;;;;;IAuCAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAO,OAAA,CAAc;IAC7DX,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAI,OAAA,CAAAD,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAQ,OAAA,CAAc;IAC7DZ,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAK,OAAA,CAAAF,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAAS,OAAA,CAAc;IAC5Db,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAM,OAAA,CAAAH,KAAA,MACF;;;;;IAUAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAU,OAAA,CAAc;IAC7Dd,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAO,OAAA,CAAAJ,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAW,OAAA,CAAc;IAC9Df,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAQ,OAAA,CAAAL,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF2CH,EAAA,CAAAI,UAAA,UAAAY,OAAA,CAAc;IACnEhB,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAS,OAAA,CAAAN,KAAA,MACF;;;;;;IAoBFV,EAAA,CAAAC,cAAA,iBAAmG;IAAzCD,EAAA,CAAAiB,UAAA,mBAAAC,wEAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,MAAAC,uBAAA,GAAAvB,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAK,SAAA,CAAAH,uBAAA,CAA8B;IAAA,EAAC;IAChGvB,EAAA,CAAA2B,SAAA,YAAsC;IAAA3B,EAAA,CAAAE,MAAA,wDACxC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAuDLH,EAAA,CAAAC,cAAA,iBACyD;IAAvDD,EAAA,CAAAiB,UAAA,mBAAAW,+EAAA;MAAA5B,EAAA,CAAAmB,aAAA,CAAAU,IAAA;MAAA,MAAAC,QAAA,GAAA9B,EAAA,CAAAsB,aAAA,GAAAS,SAAA;MAAA,MAAAV,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,MAAAU,yBAAA,GAAAhC,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAY,eAAA,CAAAD,yBAAA,EAAAF,QAAA,CAA4C;IAAA,EAAC;IACtD9B,EAAA,CAAA2B,SAAA,YAAgC;IAAA3B,EAAA,CAAAE,MAAA,oBAClC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IApBXH,EAFF,CAAAC,cAAA,SAAmD,SAE7C;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA4E;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrFH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAE,MAAA,IAGF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA0E;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnFH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5DH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAkC,UAAA,KAAAC,sDAAA,qBACyD;IAGzDnC,EAAA,CAAAC,cAAA,kBACmH;IAAjHD,EAAA,CAAAiB,UAAA,mBAAAmB,sEAAA;MAAA,MAAAN,QAAA,GAAA9B,EAAA,CAAAmB,aAAA,CAAAkB,IAAA,EAAAN,SAAA;MAAA,MAAAV,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAiB,4BAAA,CAA6B,yBAAyB,EAAAjB,OAAA,CAAAkB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAX,QAAA,CAAAY,GAAA,CAAgD;IAAA,EAAC;IAChH1C,EAAA,CAAA2B,SAAA,aAAoC;IAAA3B,EAAA,CAAAE,MAAA,iCACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACmH;IAAjHD,EAAA,CAAAiB,UAAA,mBAAA0B,sEAAA;MAAA,MAAAb,QAAA,GAAA9B,EAAA,CAAAmB,aAAA,CAAAkB,IAAA,EAAAN,SAAA;MAAA,MAAAV,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAiB,4BAAA,CAA6B,yBAAyB,EAAAjB,OAAA,CAAAkB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAX,QAAA,CAAAY,GAAA,CAAgD;IAAA,EAAC;IAChH1C,EAAA,CAAA2B,SAAA,aAAsC;IAAA3B,EAAA,CAAAE,MAAA,6CACxC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACoH;IAAlHD,EAAA,CAAAiB,UAAA,mBAAA2B,sEAAA;MAAA,MAAAd,QAAA,GAAA9B,EAAA,CAAAmB,aAAA,CAAAkB,IAAA,EAAAN,SAAA;MAAA,MAAAV,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAiB,4BAAA,CAA6B,0BAA0B,EAAAjB,OAAA,CAAAkB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAX,QAAA,CAAAY,GAAA,CAAgD;IAAA,EAAC;IACjH1C,EAAA,CAAA2B,SAAA,aAA0C;IAAA3B,EAAA,CAAAE,MAAA,6CAC5C;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAsF;IAA/BD,EAAA,CAAAiB,UAAA,mBAAA4B,sEAAA;MAAA,MAAAf,QAAA,GAAA9B,EAAA,CAAAmB,aAAA,CAAAkB,IAAA,EAAAN,SAAA;MAAA,MAAAV,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAyB,cAAA,CAAAhB,QAAA,CAAoB;IAAA,EAAC;IACnF9B,EAAA,CAAA2B,SAAA,aAA+B;IAAA3B,EAAA,CAAAE,MAAA,iCACjC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAsG;IAA/CD,EAAA,CAAAiB,UAAA,mBAAA8B,sEAAA;MAAA,MAAAjB,QAAA,GAAA9B,EAAA,CAAAmB,aAAA,CAAAkB,IAAA,EAAAN,SAAA;MAAA,MAAAV,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,MAAA0B,mBAAA,GAAAhD,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA4B,aAAA,CAAAD,mBAAA,EAAAlB,QAAA,CAAoC;IAAA,EAAC;IACnG9B,EAAA,CAAA2B,SAAA,aAA+C;IAAA3B,EAAA,CAAAE,MAAA,2BACjD;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACN,EACF;;;;;IAxCCH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAkD,iBAAA,CAAApB,QAAA,CAAAqB,UAAA,CAAoB;IACpBnD,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAkD,iBAAA,CAAApB,QAAA,CAAAsB,MAAA,CAAgB;IAElBpD,EAAA,CAAAM,SAAA,GAEF;IAFEN,EAAA,CAAAqD,kBAAA,MAAAvB,QAAA,CAAAwB,UAAA,yCAAAxB,QAAA,CAAAwB,UAAA,wCAEF;IACItD,EAAA,CAAAM,SAAA,GAA4E;IAA5EN,EAAA,CAAAkD,iBAAA,CAAApB,QAAA,CAAAyB,SAAA,6BAAAzB,QAAA,CAAAyB,SAAA,iCAA4E;IAC5EvD,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAkD,iBAAA,CAAApB,QAAA,CAAA0B,aAAA,CAAuB;IAEzBxD,EAAA,CAAAM,SAAA,GAGF;IAHEN,EAAA,CAAAyD,kBAAA,MAAA3B,QAAA,CAAA4B,UAAA,yCAAA5B,QAAA,CAAA4B,UAAA,yCAAA5B,QAAA,CAAA4B,UAAA,8CAGF;IACI1D,EAAA,CAAAM,SAAA,GAA0E;IAA1EN,EAAA,CAAAkD,iBAAA,CAAApB,QAAA,CAAA6B,WAAA,UAAA7B,QAAA,CAAA6B,WAAA,uDAA0E;IAC1E3D,EAAA,CAAAM,SAAA,GAAmD;IAAnDN,EAAA,CAAAkD,iBAAA,CAAA7B,OAAA,CAAAuC,sBAAA,CAAA9B,QAAA,CAAA+B,gBAAA,EAAmD;IACnD7D,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAkD,iBAAA,CAAApB,QAAA,CAAAgC,SAAA,mCAAiC;IAE1B9D,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAA0C,QAAA,CAAc;;;;;IA6C3B/D,EAAA,CAAAC,cAAA,oBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF2CH,EAAA,CAAAI,UAAA,UAAA4D,UAAA,CAAgB;IACrEhE,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAyD,UAAA,CAAAxD,cAAA,MACF;;;;;IAoDAR,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAA6D,UAAA,CAAgB;IACzEjE,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAA0D,UAAA,CAAAvD,KAAA,MACF;;;;;IASAV,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAA8D,UAAA,CAAgB;IACzElE,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAA2D,UAAA,CAAAxD,KAAA,MACF;;;;;;IANFV,EADF,CAAAC,cAAA,cAAkD,iBACqC;IACnFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAA6F;IAA/DD,EAAA,CAAAmE,gBAAA,2BAAAC,+GAAAC,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAmD,cAAA,CAAAC,kBAAA,EAAAJ,MAAA,MAAAhD,OAAA,CAAAmD,cAAA,CAAAC,kBAAA,GAAAJ,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAA+C;IAC3ErE,EAAA,CAAAkC,UAAA,IAAAwC,uFAAA,wBAA4E;IAIhF1E,EADE,CAAAG,YAAA,EAAY,EACR;;;;IAL0BH,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAmD,cAAA,CAAAC,kBAAA,CAA+C;IAC7CzE,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAAuD,OAAA,CAAAC,gBAAA,CAA2B;;;;;IAUzD7E,EAAA,CAAAC,cAAA,oBAA2E;IACzED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8CH,EAAA,CAAAI,UAAA,UAAA0E,UAAA,CAAgB;IACxE9E,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAuE,UAAA,CAAApE,KAAA,MACF;;;;;;IANFV,EADF,CAAAC,cAAA,cAAiD,iBACqC;IAClFD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAA0F;IAA9DD,EAAA,CAAAmE,gBAAA,2BAAAY,+GAAAV,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAA6D,IAAA;MAAA,MAAA3D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAmD,cAAA,CAAAS,iBAAA,EAAAZ,MAAA,MAAAhD,OAAA,CAAAmD,cAAA,CAAAS,iBAAA,GAAAZ,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAA8C;IACxErE,EAAA,CAAAkC,UAAA,IAAAgD,uFAAA,wBAA2E;IAI/ElF,EADE,CAAAG,YAAA,EAAY,EACR;;;;IALwBH,EAAA,CAAAM,SAAA,GAA8C;IAA9CN,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAmD,cAAA,CAAAS,iBAAA,CAA8C;IAC1CjF,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAAuD,OAAA,CAAAO,eAAA,CAA0B;;;;;;IA/E1DnF,EAFJ,CAAAC,cAAA,uBAA+C,cACrB,gBACiE;IACrFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBAA6G;IAA/ED,EAAA,CAAAmE,gBAAA,2BAAAiB,wGAAAf,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAmD,cAAA,CAAAhC,kBAAA,EAAA6B,MAAA,MAAAhD,OAAA,CAAAmD,cAAA,CAAAhC,kBAAA,GAAA6B,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAA+C;IAC3ErE,EAAA,CAAAkC,UAAA,IAAAoD,gFAAA,wBAAwE;IAI5EtF,EADE,CAAAG,YAAA,EAAY,EACR;IAGJH,EADF,CAAAC,cAAA,cAAwB,gBAC+D;IACnFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAoG;IAAvCD,EAAA,CAAAmE,gBAAA,2BAAAoB,oGAAAlB,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAmE,WAAA,CAAAC,UAAA,EAAApB,MAAA,MAAAhD,OAAA,CAAAmE,WAAA,CAAAC,UAAA,GAAApB,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAAoC;IACnGrE,EADE,CAAAG,YAAA,EAAoG,EAChG;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC2D;IAC/ED,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBACoB;IADyCD,EAAA,CAAAmE,gBAAA,2BAAAuB,qGAAArB,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAmE,WAAA,CAAApC,MAAA,EAAAiB,MAAA,MAAAhD,OAAA,CAAAmE,WAAA,CAAApC,MAAA,GAAAiB,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAAgC;IAE/FrE,EAFE,CAAAG,YAAA,EACoB,EAChB;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBACmD;IACvED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAuG;IAA1CD,EAAA,CAAAmE,gBAAA,2BAAAwB,qGAAAtB,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAmE,WAAA,CAAAI,aAAA,EAAAvB,MAAA,MAAAhD,OAAA,CAAAmE,WAAA,CAAAI,aAAA,GAAAvB,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAAuC;IACtGrE,EADE,CAAAG,YAAA,EAAuG,EACnG;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBACiD;IACrED,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBACmB;IAD2CD,EAAA,CAAAmE,gBAAA,2BAAA0B,qGAAAxB,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAmE,WAAA,CAAAM,WAAA,EAAAzB,MAAA,MAAAhD,OAAA,CAAAmE,WAAA,CAAAM,WAAA,GAAAzB,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAAqC;IAErGrE,EAFE,CAAAG,YAAA,EACmB,EACf;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC2C;IAC/DD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAA+F;IAAlCD,EAAA,CAAAmE,gBAAA,2BAAA4B,qGAAA1B,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAmE,WAAA,CAAAQ,KAAA,EAAA3B,MAAA,MAAAhD,OAAA,CAAAmE,WAAA,CAAAQ,KAAA,GAAA3B,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAA+B;IAC9FrE,EADE,CAAAG,YAAA,EAA+F,EAC3F;IAEJH,EADF,CAAAC,cAAA,eAAwB,iBAC4C;IAChED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAgG;IAAnCD,EAAA,CAAAmE,gBAAA,2BAAA8B,qGAAA5B,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAmE,WAAA,CAAAU,MAAA,EAAA7B,MAAA,MAAAhD,OAAA,CAAAmE,WAAA,CAAAU,MAAA,GAAA7B,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAAgC;IAC/FrE,EADE,CAAAG,YAAA,EAAgG,EAC5F;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC+D;IACnFD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAA6F;IAA/DD,EAAA,CAAAmE,gBAAA,2BAAAgC,yGAAA9B,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAmD,cAAA,CAAA4B,kBAAA,EAAA/B,MAAA,MAAAhD,OAAA,CAAAmD,cAAA,CAAA4B,kBAAA,GAAA/B,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAA+C;IAC3ErE,EAAA,CAAAkC,UAAA,KAAAmE,iFAAA,wBAA4E;IAIhFrG,EADE,CAAAG,YAAA,EAAY,EACR;IAYNH,EAVA,CAAAkC,UAAA,KAAAoE,2EAAA,kBAAkD,KAAAC,2EAAA,kBAUD;IAY/CvG,EADF,CAAAC,cAAA,eAAwB,iBAC+C;IACnED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,uBAAgE;IAApCD,EAAA,CAAAmE,gBAAA,2BAAAqC,2GAAAnC,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAmE,WAAA,CAAAjC,SAAA,EAAAc,MAAA,MAAAhD,OAAA,CAAAmE,WAAA,CAAAjC,SAAA,GAAAc,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAAmC;IAACrE,EAAA,CAAAE,MAAA,eAChE;IACFF,EADE,CAAAG,YAAA,EAAc,EACV;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC+C;IACnED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,uBAAgE;IAApCD,EAAA,CAAAmE,gBAAA,2BAAAsC,2GAAApC,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAmE,WAAA,CAAA1B,SAAA,EAAAO,MAAA,MAAAhD,OAAA,CAAAmE,WAAA,CAAA1B,SAAA,GAAAO,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAAmC;IAACrE,EAAA,CAAAE,MAAA,eAChE;IACFF,EADE,CAAAG,YAAA,EAAc,EACV;IAGJH,EADF,CAAAC,cAAA,eAAsC,iBACgD;IAClFD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAENH,EADF,CAAAC,cAAA,eAAoC,yBACL;IAC3BD,EAAA,CAAA2B,SAAA,mBAAoD;IACpD3B,EAAA,CAAAC,cAAA,iBACiE;IAA1CD,EAAA,CAAAmE,gBAAA,2BAAAuC,qGAAArC,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAmE,WAAA,CAAAmB,eAAA,EAAAtC,MAAA,MAAAhD,OAAA,CAAAmE,WAAA,CAAAmB,eAAA,GAAAtC,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAAyC;IADhErE,EAAA,CAAAG,YAAA,EACiE;IACjEH,EAAA,CAAA2B,SAAA,6BAA8D;IAChE3B,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,yBAA6B;IAC3BD,EAAA,CAAA2B,SAAA,mBAAoD;IACpD3B,EAAA,CAAAC,cAAA,kBAC+D;IAAxCD,EAAA,CAAAmE,gBAAA,2BAAAyC,qGAAAvC,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAmE,WAAA,CAAAqB,aAAA,EAAAxC,MAAA,MAAAhD,OAAA,CAAAmE,WAAA,CAAAqB,aAAA,GAAAxC,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAAuC;IAD9DrE,EAAA,CAAAG,YAAA,EAC+D;IAC/DH,EAAA,CAAA2B,SAAA,6BAA4D;IAIpE3B,EAHM,CAAAG,YAAA,EAAgB,EACZ,EACF,EACO;;;;;;IArHmBH,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAmD,cAAA,CAAAhC,kBAAA,CAA+C;IAC7CxC,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAAyF,oBAAA,CAAuB;IAUM9G,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAmE,WAAA,CAAAC,UAAA,CAAoC;IAOpCzF,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAmE,WAAA,CAAApC,MAAA,CAAgC;IAQhCpD,EAAA,CAAAM,SAAA,GAAuC;IAAvCN,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAmE,WAAA,CAAAI,aAAA,CAAuC;IAOtC5F,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAmE,WAAA,CAAAM,WAAA,CAAqC;IAQtC9F,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAmE,WAAA,CAAAQ,KAAA,CAA+B;IAM/BhG,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAmE,WAAA,CAAAU,MAAA,CAAgC;IAO/DlG,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAmD,cAAA,CAAA4B,kBAAA,CAA+C;IAC7CpG,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAAuD,OAAA,CAAAmC,gBAAA,CAA2B;IAMpC/G,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAA2F,iBAAA,CAAuB;IAUvBhH,EAAA,CAAAM,SAAA,EAAsB;IAAtBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAA4F,gBAAA,CAAsB;IAejBjH,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAmE,WAAA,CAAAjC,SAAA,CAAmC;IAQnCvD,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAmE,WAAA,CAAA1B,SAAA,CAAmC;IAWQ9D,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,iBAAA8G,aAAA,CAA0B;IACtElH,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAmE,WAAA,CAAAmB,eAAA,CAAyC;IAKC3G,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,iBAAA+G,WAAA,CAAwB;IAClEnH,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAmE,WAAA,CAAAqB,aAAA,CAAuC;;;;;;IAUpE7G,EAAA,CAAAC,cAAA,kBAAqG;IAA9BD,EAAA,CAAAiB,UAAA,mBAAAmG,uFAAA;MAAApH,EAAA,CAAAmB,aAAA,CAAAkG,IAAA;MAAA,MAAAC,OAAA,GAAAtH,EAAA,CAAAsB,aAAA,GAAAiG,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAmG,cAAA,CAAAF,OAAA,CAAmB;IAAA,EAAC;IAClGtH,EAAA,CAAA2B,SAAA,aAAiC;IAAA3B,EAAA,CAAAE,MAAA,oBACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IApIbH,EAAA,CAAAC,cAAA,kBAA+C;IAG7CD,EAAA,CAAAkC,UAAA,IAAAuF,oEAAA,6BAA+C;IA4H7CzH,EADF,CAAAC,cAAA,yBAAsD,iBACsB;IAAvBD,EAAA,CAAAiB,UAAA,mBAAAyG,8EAAA;MAAA,MAAAJ,OAAA,GAAAtH,EAAA,CAAAmB,aAAA,CAAAwG,IAAA,EAAAJ,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAuG,OAAA,CAAAN,OAAA,CAAY;IAAA,EAAC;IACvEtH,EAAA,CAAA2B,SAAA,YAAiC;IAAA3B,EAAA,CAAAE,MAAA,oBACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAkC,UAAA,IAAA2F,8DAAA,qBAAqG;IAIzG7H,EADE,CAAAG,YAAA,EAAiB,EACT;;;;IAnIoBH,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAmE,WAAA,CAAiB;IA+HYxF,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAyG,QAAA,CAAc;;;;;;IAkCrE9H,EAAA,CAAAC,cAAA,kBAAiF;IAAhCD,EAAA,CAAAiB,UAAA,mBAAA8G,wFAAA;MAAA/H,EAAA,CAAAmB,aAAA,CAAA6G,IAAA;MAAA,MAAAC,OAAA,GAAAjI,EAAA,CAAAsB,aAAA,GAAAiG,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA6G,gBAAA,CAAAD,OAAA,CAAqB;IAAA,EAAC;IAC9EjI,EAAA,CAAA2B,SAAA,aAAgC;IAAA3B,EAAA,CAAAE,MAAA,oBAClC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA3BXH,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,wFACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,uBAA2B,cACD,iBACkE;IACtFD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAuG;IAA5CD,EAAA,CAAAmE,gBAAA,2BAAAgE,qFAAA9D,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAiH,IAAA;MAAA,MAAA/G,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAgH,aAAA,CAAAC,aAAA,EAAAjE,MAAA,MAAAhD,OAAA,CAAAgH,aAAA,CAAAC,aAAA,GAAAjE,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAAyC;IACtGrE,EADE,CAAAG,YAAA,EAAuG,EACnG;IAEJH,EADF,CAAAC,cAAA,cAAwB,iBACoE;IAAAD,EAAA,CAAAE,MAAA,6CAC1F;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,kBAA+G;IAA9CD,EAAA,CAAAmE,gBAAA,2BAAAoE,sFAAAlE,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAiH,IAAA;MAAA,MAAA/G,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAgH,aAAA,CAAAG,eAAA,EAAAnE,MAAA,MAAAhD,OAAA,CAAAgH,aAAA,CAAAG,eAAA,GAAAnE,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAA2C;IAC9GrE,EADE,CAAAG,YAAA,EAA+G,EAC3G;IAEJH,EADF,CAAAC,cAAA,eAAwB,kBAC2D;IAAAD,EAAA,CAAAE,MAAA,uCACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,kBAAqG;IAArCD,EAAA,CAAAmE,gBAAA,2BAAAsE,sFAAApE,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAiH,IAAA;MAAA,MAAA/G,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuE,kBAAA,CAAAlD,OAAA,CAAAgH,aAAA,CAAAjF,MAAA,EAAAiB,MAAA,MAAAhD,OAAA,CAAAgH,aAAA,CAAAjF,MAAA,GAAAiB,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAAkC;IAEtGrE,EAFI,CAAAG,YAAA,EAAqG,EACjG,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,mBACQ;IAAvBD,EAAA,CAAAiB,UAAA,mBAAAyH,+EAAA;MAAA,MAAAT,OAAA,GAAAjI,EAAA,CAAAmB,aAAA,CAAAiH,IAAA,EAAAb,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAuG,OAAA,CAAAK,OAAA,CAAY;IAAA,EAAC;IACzDjI,EAAA,CAAA2B,SAAA,aAAiC;IAAA3B,EAAA,CAAAE,MAAA,qBACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAkC,UAAA,KAAAyG,+DAAA,sBAAiF;IAIrF3I,EADE,CAAAG,YAAA,EAAiB,EACT;;;;IArBuDH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAgH,aAAA,CAAAC,aAAA,CAAyC;IAKnCtI,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAgH,aAAA,CAAAG,eAAA,CAA2C;IAK5CxI,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAA2E,gBAAA,YAAAtD,OAAA,CAAAgH,aAAA,CAAAjF,MAAA,CAAkC;IAOnEpD,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAyG,QAAA,CAAc;;;;;;IAiB3C9H,EAFJ,CAAAC,cAAA,eAA6E,eACvD,8CAIsC;IAAtDD,EADkD,CAAAiB,UAAA,6BAAA2H,0HAAAvE,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAA0H,IAAA;MAAA,MAAAxH,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAAmBJ,OAAA,CAAAyH,sBAAA,CAAAzE,MAAA,CAA8B;IAAA,EAAC,mBAAA0E,gHAAA1E,MAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAA0H,IAAA;MAAA,MAAAxH,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAC3FJ,OAAA,CAAA2H,eAAA,CAAA3E,MAAA,CAAuB;IAAA,EAAC;IACnCrE,EAAA,CAAAG,YAAA,EAAqC;IACrCH,EAAA,CAAAC,cAAA,kBAAoE;IAA7BD,EAAA,CAAAiB,UAAA,mBAAAgI,oFAAA;MAAAjJ,EAAA,CAAAmB,aAAA,CAAA0H,IAAA;MAAA,MAAAxH,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA6H,gBAAA,EAAkB;IAAA,EAAC;IACjElJ,EAAA,CAAA2B,SAAA,aAAgC;IAAA3B,EAAA,CAAAE,MAAA,kDAClC;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IAEJH,EADF,CAAAC,cAAA,UAAK,kBACwE;IAA7BD,EAAA,CAAAiB,UAAA,mBAAAkI,oFAAA;MAAAnJ,EAAA,CAAAmB,aAAA,CAAA0H,IAAA;MAAA,MAAAxH,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA+H,gBAAA,EAAkB;IAAA,EAAC;IACxEpJ,EAAA,CAAA2B,SAAA,aAAoC;IAAA3B,EAAA,CAAAE,MAAA,4CACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAsE;IAA7BD,EAAA,CAAAiB,UAAA,mBAAAoI,qFAAA;MAAArJ,EAAA,CAAAmB,aAAA,CAAA0H,IAAA;MAAA,MAAAxH,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAiI,gBAAA,EAAkB;IAAA,EAAC;IACnEtJ,EAAA,CAAA2B,SAAA,cAAmC;IAAA3B,EAAA,CAAAE,MAAA,6CACrC;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAjBkCH,EAAA,CAAAM,SAAA,GAAqE;IAGrEN,EAHA,CAAAI,UAAA,iBAAAiB,OAAA,CAAAkB,WAAA,CAAAC,kBAAA,kBAAAnB,OAAA,CAAAkB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,kBAAApB,OAAA,CAAAkB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,CAAA8G,QAAA,UAAqE,oCACxF,8BAA8B,8CAA8C,eAAAlI,OAAA,CAAAkB,WAAA,CAAAC,kBAAA,kBAAAnB,OAAA,CAAAkB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAC1C,oBACI;;;;;IAiB3DzC,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAA2B,SAAA,aAAgC;IAChC3B,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,iNAC1B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAuDIH,EAAA,CAAAC,cAAA,kBAAmG;IAAjCD,EAAA,CAAAiB,UAAA,mBAAAuI,8FAAA;MAAAxJ,EAAA,CAAAmB,aAAA,CAAAsI,IAAA;MAAA,MAAAC,KAAA,GAAA1J,EAAA,CAAAsB,aAAA,GAAAqI,KAAA;MAAA,MAAAtI,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAuI,mBAAA,CAAAF,KAAA,CAAsB;IAAA,EAAC;IAChG1J,EAAA,CAAA2B,SAAA,aAAiC;IAAA3B,EAAA,CAAAE,MAAA,oBACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IACTH,EAAA,CAAAC,cAAA,gBAAsD;IACpDD,EAAA,CAAA2B,SAAA,aAA2B;IAC7B3B,EAAA,CAAAG,YAAA,EAAO;;;;;;IAzCPH,EAFJ,CAAAC,cAAA,SAAuD,SACjD,iBAKe;IAJUD,EAAA,CAAAmE,gBAAA,2BAAA0F,2FAAAxF,MAAA;MAAA,MAAAyF,QAAA,GAAA9J,EAAA,CAAAmB,aAAA,CAAA4I,IAAA,EAAAhI,SAAA;MAAA/B,EAAA,CAAAuE,kBAAA,CAAAuF,QAAA,CAAAE,SAAA,EAAA3F,MAAA,MAAAyF,QAAA,CAAAE,SAAA,GAAA3F,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAA4B;IAKzDrE,EALE,CAAAG,YAAA,EAIiB,EACd;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAI0G;IAHjFD,EAAA,CAAAmE,gBAAA,2BAAA8F,2FAAA5F,MAAA;MAAA,MAAAyF,QAAA,GAAA9J,EAAA,CAAAmB,aAAA,CAAA4I,IAAA,EAAAhI,SAAA;MAAA/B,EAAA,CAAAuE,kBAAA,CAAAuF,QAAA,CAAAI,SAAA,EAAA7F,MAAA,MAAAyF,QAAA,CAAAI,SAAA,GAAA7F,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAA4B;IAIzDrE,EAJE,CAAAG,YAAA,EAG4G,EACzG;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAEsG;IAD3ED,EAAA,CAAAmE,gBAAA,2BAAAgG,2FAAA9F,MAAA;MAAA,MAAAyF,QAAA,GAAA9J,EAAA,CAAAmB,aAAA,CAAA4I,IAAA,EAAAhI,SAAA;MAAA/B,EAAA,CAAAuE,kBAAA,CAAAuF,QAAA,CAAAM,UAAA,EAAA/F,MAAA,MAAAyF,QAAA,CAAAM,UAAA,GAAA/F,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAA6B;IAACrE,EAAA,CAAAiB,UAAA,2BAAAkJ,2FAAA;MAAAnK,EAAA,CAAAmB,aAAA,CAAA4I,IAAA;MAAA,MAAA1I,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAAiBJ,OAAA,CAAAgJ,cAAA,EAAgB;IAAA,EAAC;IAE/FrK,EAFE,CAAAG,YAAA,EACwG,EACrG;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAG0E;IAFjDD,EAAA,CAAAmE,gBAAA,2BAAAmG,2FAAAjG,MAAA;MAAA,MAAAyF,QAAA,GAAA9J,EAAA,CAAAmB,aAAA,CAAA4I,IAAA,EAAAhI,SAAA;MAAA/B,EAAA,CAAAuE,kBAAA,CAAAuF,QAAA,CAAAS,KAAA,EAAAlG,MAAA,MAAAyF,QAAA,CAAAS,KAAA,GAAAlG,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAAwB;IAGrDrE,EAHE,CAAAG,YAAA,EAE4E,EACzE;IAEHH,EADF,CAAAC,cAAA,SAAI,kBAE+E;IADpDD,EAAA,CAAAmE,gBAAA,2BAAAqG,4FAAAnG,MAAA;MAAA,MAAAyF,QAAA,GAAA9J,EAAA,CAAAmB,aAAA,CAAA4I,IAAA,EAAAhI,SAAA;MAAA/B,EAAA,CAAAuE,kBAAA,CAAAuF,QAAA,CAAAW,MAAA,EAAApG,MAAA,MAAAyF,QAAA,CAAAW,MAAA,GAAApG,MAAA;MAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;IAAA,EAAyB;IAACrE,EAAA,CAAAiB,UAAA,2BAAAuJ,4FAAA;MAAAxK,EAAA,CAAAmB,aAAA,CAAA4I,IAAA;MAAA,MAAA1I,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAAiBJ,OAAA,CAAAgJ,cAAA,EAAgB;IAAA,EAAC;IAE3FrK,EAFE,CAAAG,YAAA,EACiF,EAC9E;IACLH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,UAAI,iBAGyF;IACzFD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;IACLH,EAAA,CAAAC,cAAA,UAAI;IAIFD,EAHA,CAAAkC,UAAA,KAAAwI,qEAAA,sBAAmG,KAAAC,mEAAA,oBAG7C;IAI1D3K,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAxCCH,EAAA,CAAAM,SAAA,GAAyG;IAAzGN,EAAA,CAAA4K,WAAA,cAAAvJ,OAAA,CAAAwJ,mBAAA,IAAAf,QAAA,CAAAhK,kBAAA,UAAAgK,QAAA,CAAAhK,kBAAA,OAAyG;IAHhFE,EAAA,CAAA2E,gBAAA,YAAAmF,QAAA,CAAAE,SAAA,CAA4B;IACrDhK,EAAA,CAAAI,UAAA,cAAAiB,OAAA,CAAAwJ,mBAAA,IAAAf,QAAA,CAAAhK,kBAAA,UAAAgK,QAAA,CAAAhK,kBAAA,OAAmG;IASnGE,EAAA,CAAAM,SAAA,GAAyG;IAAzGN,EAAA,CAAA4K,WAAA,cAAAvJ,OAAA,CAAAwJ,mBAAA,IAAAf,QAAA,CAAAhK,kBAAA,UAAAgK,QAAA,CAAAhK,kBAAA,OAAyG;IAHhFE,EAAA,CAAA2E,gBAAA,YAAAmF,QAAA,CAAAI,SAAA,CAA4B;IACrDlK,EAAA,CAAAI,UAAA,cAAAiB,OAAA,CAAAwJ,mBAAA,IAAAf,QAAA,CAAAhK,kBAAA,UAAAgK,QAAA,CAAAhK,kBAAA,OAAmG;IAKxEE,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAA2E,gBAAA,YAAAmF,QAAA,CAAAM,UAAA,CAA6B;IACrBpK,EAAA,CAAAI,UAAA,cAAAiB,OAAA,CAAAwJ,mBAAA,IAAAf,QAAA,CAAAhK,kBAAA,OAAkE;IAKrGE,EAAA,CAAAM,SAAA,GAAyE;IAAzEN,EAAA,CAAA4K,WAAA,cAAAvJ,OAAA,CAAAwJ,mBAAA,IAAAf,QAAA,CAAAhK,kBAAA,OAAyE;IAFhDE,EAAA,CAAA2E,gBAAA,YAAAmF,QAAA,CAAAS,KAAA,CAAwB;IACjDvK,EAAA,CAAAI,UAAA,cAAAiB,OAAA,CAAAwJ,mBAAA,IAAAf,QAAA,CAAAhK,kBAAA,OAAmE;IAIxCE,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAA2E,gBAAA,YAAAmF,QAAA,CAAAW,MAAA,CAAyB;IACxCzK,EAAA,CAAAI,UAAA,cAAAiB,OAAA,CAAAwJ,mBAAA,IAAAf,QAAA,CAAAhK,kBAAA,OAAkE;IAGhFE,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAc,OAAA,CAAAyJ,cAAA,CAAAhB,QAAA,CAAAM,UAAA,GAAAN,QAAA,CAAAW,MAAA,OACF;IAEsBzK,EAAA,CAAAM,SAAA,GAAqD;IAEvEN,EAFkB,CAAA4K,WAAA,kBAAAd,QAAA,CAAAhK,kBAAA,OAAqD,eAAAgK,QAAA,CAAAhK,kBAAA,OACrB,oBAAAgK,QAAA,CAAAhK,kBAAA,UAAAgK,QAAA,CAAAhK,kBAAA,OACsC;IACxFE,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAc,OAAA,CAAA0J,oBAAA,CAAAjB,QAAA,CAAAhK,kBAAA,OACF;IAGSE,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAwJ,mBAAA,CAAyB;IAG3B7K,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAI,UAAA,UAAAiB,OAAA,CAAAwJ,mBAAA,CAA0B;;;;;IAMnC7K,EADF,CAAAC,cAAA,SAAwC,cACE;IACtCD,EAAA,CAAAE,MAAA,iLACF;IACFF,EADE,CAAAG,YAAA,EAAK,EACF;;;;;;IAuDTH,EAAA,CAAAC,cAAA,kBACiB;IADgED,EAAA,CAAAiB,UAAA,mBAAA+J,wFAAA;MAAAhL,EAAA,CAAAmB,aAAA,CAAA8J,IAAA;MAAA,MAAA5J,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA6J,kBAAA,EAAoB;IAAA,EAAC;IAE7GlL,EAAA,CAAA2B,SAAA,aAAgC;IAAC3B,EAAA,CAAAE,MAAA,6CACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAWTH,EAAA,CAAAC,cAAA,kBAC2C;IADqBD,EAAA,CAAAiB,UAAA,mBAAAkK,wFAAA;MAAAnL,EAAA,CAAAmB,aAAA,CAAAiK,IAAA;MAAA,MAAAC,OAAA,GAAArL,EAAA,CAAAsB,aAAA,GAAAiG,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAiK,aAAA,CAAAD,OAAA,CAAkB;IAAA,EAAC;IAE1FrL,EAAA,CAAA2B,SAAA,aAAuC;IAAA3B,EAAA,CAAAE,MAAA,sCACzC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFPH,EAAA,CAAAI,UAAA,aAAAiB,OAAA,CAAAkK,cAAA,CAAAC,MAAA,OAAwC;;;;;;IAG1CxL,EAAA,CAAAC,cAAA,kBAC2C;IADqBD,EAAA,CAAAiB,UAAA,mBAAAwK,wFAAA;MAAAzL,EAAA,CAAAmB,aAAA,CAAAuK,IAAA;MAAA,MAAAL,OAAA,GAAArL,EAAA,CAAAsB,aAAA,GAAAiG,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAsK,aAAA,CAAAN,OAAA,CAAkB;IAAA,EAAC;IAE1FrL,EAAA,CAAA2B,SAAA,aAAgC;IAAA3B,EAAA,CAAAE,MAAA,sCAClC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFPH,EAAA,CAAAI,UAAA,aAAAiB,OAAA,CAAAkK,cAAA,CAAAC,MAAA,OAAwC;;;;;;IA3K9CxL,EADF,CAAAC,cAAA,mBAAgD,qBAC9B;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAAC,cAAA,mBAAc;IAwBZD,EAtBA,CAAAkC,UAAA,IAAA0J,2DAAA,oBAA6E,IAAAC,2DAAA,mBAsBV;IAS3D7L,EAJR,CAAAC,cAAA,eAA8B,iBACP,gBACS,SACtB,eACc;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,6BAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,eAAe;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,eAAe;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,6BAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEtBF,EAFsB,CAAAG,YAAA,EAAK,EACpB,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IA+CLD,EA9CA,CAAAkC,UAAA,KAAA4J,2DAAA,mBAAuD,KAAAC,2DAAA,kBA8Cf;IAO9C/L,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAQEH,EALR,CAAAC,cAAA,gBAAkB,gBACqB,gBACR,gBAE2C,iBACjC;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,iBAAwC;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAC3EF,EAD2E,CAAAG,YAAA,EAAO,EAC5E;IAKFH,EAFJ,CAAAC,cAAA,gBAAqG,gBAC5D,gBACF;IACjCD,EAAA,CAAA2B,SAAA,cAAwC;IAC1C3B,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,WAAK,iBAC+B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,iBAA2D;IAAAD,EAAA,CAAAE,MAAA,UAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpEH,EAAA,CAAAC,cAAA,gBAAmC;IACjCD,EAAA,CAAA2B,SAAA,cAAuC;IACvC3B,EAAA,CAAAE,MAAA,4DACF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAEJH,EADF,CAAAC,cAAA,gBAAsB,gBAC8B;IAAAD,EAAA,CAAAE,MAAA,IAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjGH,EAAA,CAAAC,cAAA,gBAA8B;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAEtCF,EAFsC,CAAAG,YAAA,EAAM,EACpC,EACF;IAGNH,EAAA,CAAA2B,SAAA,eAAiB;IAIf3B,EADF,CAAAC,cAAA,gBAA+D,iBACrB;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,iBAA2C;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAK3FF,EAL2F,CAAAG,YAAA,EAAO,EACpF,EACF,EACF,EACF,EACO;IAGXH,EAFJ,CAAAC,cAAA,2BAAuD,WAChD,mBAEsD;IADRD,EAAA,CAAAiB,UAAA,mBAAA+K,+EAAA;MAAAhM,EAAA,CAAAmB,aAAA,CAAA8K,IAAA;MAAA,MAAA5K,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA6K,cAAA,EAAgB;IAAA,EAAC;IAEzElM,EAAA,CAAA2B,SAAA,cAAiC;IAAC3B,EAAA,CAAAE,MAAA,wCACpC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAkC,UAAA,KAAAiK,+DAAA,sBACiB;IAOnBnM,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,WAAK,mBACkE;IAAvBD,EAAA,CAAAiB,UAAA,mBAAAmL,+EAAA;MAAA,MAAAf,OAAA,GAAArL,EAAA,CAAAmB,aAAA,CAAA8K,IAAA,EAAA1E,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAuG,OAAA,CAAAyD,OAAA,CAAY;IAAA,EAAC;IAClErL,EAAA,CAAA2B,SAAA,aAAiC;IAAA3B,EAAA,CAAAE,MAAA,qBACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAMTH,EAJA,CAAAkC,UAAA,KAAAmK,+DAAA,sBAC2C,KAAAC,+DAAA,sBAIA;IAKjDtM,EAFI,CAAAG,YAAA,EAAM,EACS,EACT;;;;IA/KNH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAqD,kBAAA,2BAAAhC,OAAA,CAAAkL,YAAA,kBAAAlL,OAAA,CAAAkL,YAAA,CAAApJ,UAAA,QAAA9B,OAAA,CAAAkL,YAAA,kBAAAlL,OAAA,CAAAkL,YAAA,CAAAnJ,MAAA,aACF;IAGQpD,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAwJ,mBAAA,CAAyB;IAsBzB7K,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAI,UAAA,UAAAiB,OAAA,CAAAwJ,mBAAA,CAA0B;IAoBL7K,EAAA,CAAAM,SAAA,IAAmB;IAAnBN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAAkK,cAAA,CAAmB;IA8CnCvL,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAkK,cAAA,CAAAC,MAAA,OAAiC;IAgBIxL,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAkD,iBAAA,CAAA7B,OAAA,CAAAyJ,cAAA,CAAAzJ,OAAA,CAAAmL,WAAA,EAAiC;IAmBrBxM,EAAA,CAAAM,SAAA,IAAyC;IAAzCN,EAAA,CAAAkD,iBAAA,CAAA7B,OAAA,CAAAyJ,cAAA,CAAAzJ,OAAA,CAAAoL,mBAAA,EAAyC;IAWlDzM,EAAA,CAAAM,SAAA,GAAsC;IAAtCN,EAAA,CAAAkD,iBAAA,CAAA7B,OAAA,CAAAyJ,cAAA,CAAAzJ,OAAA,CAAAqL,gBAAA,EAAsC;IASrF1M,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAI,UAAA,aAAAiB,OAAA,CAAAkK,cAAA,CAAAC,MAAA,OAAwC;IAIjCxL,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,UAAAiB,OAAA,CAAAwJ,mBAAA,CAA0B;IAc1B7K,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAwJ,mBAAA,CAAyB;IAIzB7K,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAwJ,mBAAA,CAAyB;;;ADrgB1C,OAAM,MAAO8B,4BAA6B,SAAQ1N,aAAa;EAE7D2N,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,qBAA2C,EAC3CC,iBAAmC,EACnCC,OAAsB,EACtBC,MAAc,EACdC,aAA2B,EAC3BC,gBAAgC,EAChCC,gBAAkC,EAClCC,eAAgC,EAChCC,4BAA0D;IAElE,KAAK,CAACd,MAAM,CAAC;IAhBL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,4BAA4B,GAA5BA,4BAA4B;IAhBtC,KAAAC,eAAe,GAAW,CAAC,CAAC;IA4BnB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,aAAa,GAAiB,CAC5B;MACEC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,OAAO;MACZzN,KAAK,EAAE;KACR,EACD;MACEwN,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClBzN,KAAK,EAAE;KACR,CACF;IAED,KAAA0N,gBAAgB,GAAG,CACjB;MACEF,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,KAAK;MACVzN,KAAK,EAAE;KACR,EACD;MACEwN,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,QAAQ;MACbzN,KAAK,EAAE;KACR,EACD;MACEwN,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,YAAY;MACjBzN,KAAK,EAAE;KACR,CACF;IAKD,KAAA2N,gBAAgB,GAAU,CAAC;MAAE3N,KAAK,EAAE,IAAI;MAAEwN,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAAI,gBAAgB,GAAU,CAAC;MAAE5N,KAAK,EAAE,IAAI;MAAEwN,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAA/I,eAAe,GAAU,CAAC;MAAEzE,KAAK,EAAE,IAAI;MAAEwN,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACrD,KAAAnH,gBAAgB,GAAU,CAAC;MAAErG,KAAK,EAAE,IAAI;MAAEwN,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAArJ,gBAAgB,GAAU,CAAC;MAAEnE,KAAK,EAAE,IAAI;MAAEwN,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAAK,iBAAiB,GAAU,CAAC;MAAE7N,KAAK,EAAE,IAAI;MAAEwN,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACvD,KAAAM,sBAAsB,GAAU,CAAC;MAAE9N,KAAK,EAAE,IAAI;MAAEwN,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IAE5D,KAAAtJ,OAAO,GAAG;MACRO,eAAe,EAAE,IAAI,CAAC2H,UAAU,CAAC2B,cAAc,CAAClP,iBAAiB,CAAC;MAClEsF,gBAAgB,EAAE,IAAI,CAACiI,UAAU,CAAC2B,cAAc,CAAChP,aAAa,CAAC;MAC/DsH,gBAAgB,EAAE,IAAI,CAAC+F,UAAU,CAAC2B,cAAc,CAACjP,aAAa,CAAC;MAC/DgP,sBAAsB,EAAE,IAAI,CAAC1B,UAAU,CAAC2B,cAAc,CAAC9O,mBAAmB;KAC3E;IAGD,KAAA+O,UAAU,GAAG;MACXC,QAAQ,EAAE,CAAC;MACX3I,KAAK,EAAE,EAAE;MACTzC,SAAS,EAAE,KAAK;MAChBG,UAAU,EAAE,CAAC;MACbI,SAAS,EAAE,KAAK;MAChB8B,aAAa,EAAE,EAAE;MACjBgJ,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbvL,UAAU,EAAE,CAAC;MACbH,UAAU,EAAE,EAAE;MACd+C,MAAM,EAAE;KACT;IACD;IACA,KAAAqF,cAAc,GAAoB,EAAE;IACpC,KAAAiB,WAAW,GAAW,CAAC;IACvB;IACA,KAAAsC,iBAAiB,GAAW,KAAK,CAAC,CAAE;IACpC,KAAAC,uBAAuB,GAAW,CAAC,CAAC,CAAG;IACvC,KAAAtC,mBAAmB,GAAW,CAAC,CAAC,CAAO;IACvC,KAAAC,gBAAgB,GAAW,CAAC,CAAC,CAAU;IACvC,KAAAsC,mBAAmB,GAAY,IAAI,CAAC,CAAG;IACvC,KAAAzC,YAAY,GAAQ,IAAI;IACxB,KAAA0C,kBAAkB,GAAW,CAAC;IAC9B,KAAApE,mBAAmB,GAAY,IAAI,CAAC,CAAC;IAiIrC,KAAAqE,YAAY,GAAgB,IAAI;IAuKhC,KAAAC,uBAAuB,GAAU,CAC/B;MACEjB,KAAK,EAAE,EAAE;MAAExN,KAAK,EAAE;KACnB,CACF;IApYC,IAAI,CAAC6M,aAAa,CAAC6B,OAAO,EAAE,CAACC,IAAI,CAC/BlQ,GAAG,CAAEmQ,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,kDAA4B,CAAC,CAACD,GAAG,CAACE,OAAO,EAAE;QACvD,IAAI,CAAC5B,eAAe,GAAG0B,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAmFA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAESC,QAAQA,CAAA;IACf,IAAI,CAACvK,eAAe,GAAG,CACrB,GAAG,IAAI,CAACA,eAAe,EACvB,GAAG,IAAI,CAAC2H,UAAU,CAAC2B,cAAc,CAAClP,iBAAiB,CAAC,CACrD;IACD,IAAI,CAACwH,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAAC+F,UAAU,CAAC2B,cAAc,CAACjP,aAAa,CAAC,CACjD;IACD,IAAI,CAACqF,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAACiI,UAAU,CAAC2B,cAAc,CAAChP,aAAa,CAAC,CACjD;IACD,IAAI,CAAC8O,iBAAiB,GAAG,CACvB,GAAG,IAAI,CAACA,iBAAiB,EACzB,GAAG,IAAI,CAACzB,UAAU,CAAC2B,cAAc,CAAC/O,cAAc,CAAC,CAClD;IACD,IAAI,CAAC8O,sBAAsB,GAAG,CAC5B,GAAG,IAAI,CAACA,sBAAsB,EAC9B,GAAG,IAAI,CAAC1B,UAAU,CAAC2B,cAAc,CAAC9O,mBAAmB,CAAC,CACvD;IAED,IAAIC,mBAAmB,CAAC+P,iBAAiB,CAAC9P,WAAW,CAAC+P,YAAY,CAAC,IAAI,IAAI,IACtEhQ,mBAAmB,CAAC+P,iBAAiB,CAAC9P,WAAW,CAAC+P,YAAY,CAAC,IAAIC,SAAS,IAC5EjQ,mBAAmB,CAAC+P,iBAAiB,CAAC9P,WAAW,CAAC+P,YAAY,CAAC,IAAI,EAAE,EAAE;MAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACpQ,mBAAmB,CAAC+P,iBAAiB,CAAC9P,WAAW,CAAC+P,YAAY,CAAC,CAAC;MACjG,IAAI,CAACrN,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxB;QACA;QACA;QACAyN,kBAAkB,EAAEH,eAAe,CAACG,kBAAkB,IAAI,IAAI,IAAIH,eAAe,CAACG,kBAAkB,IAAIJ,SAAS,GAC7G,IAAI,CAACvB,gBAAgB,CAAC4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjC,KAAK,IAAI4B,eAAe,CAACG,kBAAkB,CAAC/B,KAAK,CAAC,GACpF,IAAI,CAACI,gBAAgB,CAAC,CAAC,CAAC;QAC5BlI,kBAAkB,EAAE0J,eAAe,CAAC1J,kBAAkB,IAAI,IAAI,IAAI0J,eAAe,CAAC1J,kBAAkB,IAAIyJ,SAAS,GAC7G,IAAI,CAAC9I,gBAAgB,CAACmJ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjC,KAAK,IAAI4B,eAAe,CAAC1J,kBAAkB,CAAC8H,KAAK,CAAC,GACpF,IAAI,CAACnH,gBAAgB,CAAC,CAAC,CAAC;QAC5BtC,kBAAkB,EAAEqL,eAAe,CAACrL,kBAAkB,IAAI,IAAI,IAAIqL,eAAe,CAACrL,kBAAkB,IAAIoL,SAAS,GAC7G,IAAI,CAAChL,gBAAgB,CAACqL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjC,KAAK,IAAI4B,eAAe,CAACrL,kBAAkB,CAACyJ,KAAK,CAAC,GACpF,IAAI,CAACrJ,gBAAgB,CAAC,CAAC,CAAC;QAC5BI,iBAAiB,EAAE6K,eAAe,CAAC7K,iBAAiB,IAAI,IAAI,IAAI6K,eAAe,CAAC7K,iBAAiB,IAAI4K,SAAS,GAC1G,IAAI,CAAC1K,eAAe,CAAC+K,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjC,KAAK,IAAI4B,eAAe,CAAC7K,iBAAiB,CAACiJ,KAAK,CAAC,GAClF,IAAI,CAAC/I,eAAe,CAAC,CAAC,CAAC;QAC3BiL,mBAAmB,EAAEN,eAAe,CAACM,mBAAmB,IAAI,IAAI,IAAIN,eAAe,CAACM,mBAAmB,IAAIP,SAAS,GAChH,IAAI,CAACtB,iBAAiB,CAAC2B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjC,KAAK,IAAI4B,eAAe,CAACM,mBAAmB,CAAClC,KAAK,CAAC,GACtF,IAAI,CAACK,iBAAiB,CAAC,CAAC,CAAC;QAC7B8B,wBAAwB,EAAEP,eAAe,CAACO,wBAAwB,IAAI,IAAI,IAAIP,eAAe,CAACO,wBAAwB,IAAIR,SAAS,GAC/H,IAAI,CAACrB,sBAAsB,CAAC0B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjC,KAAK,IAAI4B,eAAe,CAACO,wBAAwB,CAACnC,KAAK,CAAC,GAChG,IAAI,CAACM,sBAAsB,CAAC,CAAC,CAAC;QAClC8B,gBAAgB,EAAER,eAAe,CAACQ,gBAAgB,IAAI,IAAI,IAAIR,eAAe,CAACQ,gBAAgB,IAAIT,SAAS,GACvG,IAAI,CAACzB,gBAAgB,CAAC8B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjC,KAAK,IAAI4B,eAAe,CAACQ,gBAAgB,CAACpC,KAAK,CAAC,GAClF,IAAI,CAACE,gBAAgB,CAAC,CAAC,CAAC;QAC5BmC,KAAK,EAAET,eAAe,CAACS,KAAK,IAAI,IAAI,IAAIT,eAAe,CAACS,KAAK,IAAIV,SAAS,GACtEC,eAAe,CAACS,KAAK,GACrB,EAAE;QACNC,GAAG,EAAEV,eAAe,CAACU,GAAG,IAAI,IAAI,IAAIV,eAAe,CAACU,GAAG,IAAIX,SAAS,GAChEC,eAAe,CAACU,GAAG,GACnB;OACL;IACH,CAAC,MACI;MACH,IAAI,CAACjO,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxB;QACAyN,kBAAkB,EAAE,IAAI,CAAC3B,gBAAgB,CAAC,CAAC,CAAC;QAC5ClI,kBAAkB,EAAE,IAAI,CAACW,gBAAgB,CAAC,CAAC,CAAC;QAC5CtC,kBAAkB,EAAE,IAAI,CAACI,gBAAgB,CAAC,CAAC,CAAC;QAC5CI,iBAAiB,EAAE,IAAI,CAACE,eAAe,CAAC,CAAC,CAAC;QAC1CiL,mBAAmB,EAAE,IAAI,CAAC7B,iBAAiB,CAAC,CAAC,CAAC;QAC9C8B,wBAAwB,EAAE,IAAI,CAAC7B,sBAAsB,CAAC,CAAC,CAAC;QACxD8B,gBAAgB,EAAE,IAAI,CAAClC,gBAAgB,CAAC,CAAC,CAAC;QAC1CmC,KAAK,EAAE,EAAE;QACTC,GAAG,EAAE;OACN;IACH;IACA,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAC,QAAQA,CAAA;IACN,IAAIC,WAAW,GAAG;MAChBnO,kBAAkB,EAAE,IAAI,CAACD,WAAW,CAACC,kBAAkB;MACvD;MACA+N,KAAK,EAAE,IAAI,CAAChO,WAAW,CAACgO,KAAK;MAC7BC,GAAG,EAAE,IAAI,CAACjO,WAAW,CAACiO,GAAG;MACzBP,kBAAkB,EAAE,IAAI,CAAC1N,WAAW,CAAC0N,kBAAkB;MACvD7J,kBAAkB,EAAE,IAAI,CAAC7D,WAAW,CAAC6D,kBAAkB;MACvDkK,gBAAgB,EAAE,IAAI,CAAC/N,WAAW,CAAC+N,gBAAgB;MACnD7L,kBAAkB,EAAE,IAAI,CAAClC,WAAW,CAACkC,kBAAkB;MACvDQ,iBAAiB,EAAE,IAAI,CAAC1C,WAAW,CAAC0C,iBAAiB;MACrDmL,mBAAmB,EAAE,IAAI,CAAC7N,WAAW,CAAC6N,mBAAmB;MACzDC,wBAAwB,EAAE,IAAI,CAAC9N,WAAW,CAAC8N;KAC5C;IACDzQ,mBAAmB,CAACgR,iBAAiB,CAAC/Q,WAAW,CAAC+P,YAAY,EAAEG,IAAI,CAACc,SAAS,CAACF,WAAW,CAAC,CAAC;IAC5F,IAAI,CAACG,YAAY,EAAE,CAACrB,SAAS,EAAE;EACjC;EAEAsB,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACjD,SAAS,GAAGiD,OAAO;IACxB,IAAI,CAACF,YAAY,EAAE,CAACrB,SAAS,EAAE;EACjC;EAEAwB,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC1O,WAAW,CAACC,kBAAkB,CAACC,GAAG,EAAE;MAC3C,IAAI,CAACyK,aAAa,CAACgE,4BAA4B,CAAC;QAC9CC,YAAY,EAAE,IAAI,CAAC5O,WAAW,CAACC,kBAAkB,CAACC;OACnD,CAAC,CAACgN,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;UACtC,IAAI,CAAC7D,gBAAgB,CAAC8D,iBAAiB,CACrChC,GAAG,CAAC8B,OAAO,EAAE,QAAQ,CACtB;QACH,CAAC,MAAM;UACL,IAAI,CAACpE,OAAO,CAACuE,YAAY,CAACjC,GAAG,CAACkC,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAMAC,gBAAgBA,CAAA;IACd,IAAI,CAACC,SAAS,CAACC,aAAa,CAACC,KAAK,EAAE;EACtC;EAEAC,cAAcA,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAACzG,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAAC0D,YAAY,GAAG6C,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAClC,IAAI,CAACC,WAAW,EAAE;IACpB;EACF;EAEAA,WAAWA,CAAA;IACT,IAAI,IAAI,CAAChD,YAAY,EAAE;MACrB,MAAMiD,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,IAAI,CAACnD,YAAY,CAAC;MAC3C,IAAI,CAAChC,aAAa,CAACoF,4BAA4B,CAAC;QAC9CC,IAAI,EAAE;UACJpB,YAAY,EAAE,IAAI,CAAC5O,WAAW,CAACC,kBAAkB,CAACC,GAAG;UACrD+P,KAAK,EAAE,IAAI,CAACtD;;OAEf,CAAC,CAACO,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAAC+B,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACrE,OAAO,CAACyF,aAAa,CAACnD,GAAG,CAACkC,OAAQ,CAAC;UACxC,IAAI,CAACV,YAAY,EAAE,CAACrB,SAAS,EAAE;QACjC,CAAC,MAAM;UACL,IAAI,CAACzC,OAAO,CAACuE,YAAY,CAACjC,GAAG,CAACkC,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAGAkB,gBAAgBA,CAAA;IACd,IAAI,CAACxF,aAAa,CAACyF,iCAAiC,CAAC;MACnDJ,IAAI,EAAE;QAAEpB,YAAY,EAAE,IAAI,CAAC5O,WAAW,CAACC,kBAAkB,CAACC;MAAG;KAC9D,CAAC,CAACgN,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC/C,gBAAgB,GAAG,CAAC;UACvBJ,KAAK,EAAE,EAAE;UAAExN,KAAK,EAAE;SACnB,EAAE,GAAG4O,GAAG,CAAC8B,OAAO,CAACwB,GAAG,CAACC,CAAC,IAAG;UACxB,OAAO;YAAE3E,KAAK,EAAE2E,CAAC;YAAEnS,KAAK,EAAEmS;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;QACH,IAAIjT,mBAAmB,CAAC+P,iBAAiB,CAAC9P,WAAW,CAAC+P,YAAY,CAAC,IAAI,IAAI,IACtEhQ,mBAAmB,CAAC+P,iBAAiB,CAAC9P,WAAW,CAAC+P,YAAY,CAAC,IAAIC,SAAS,IAC5EjQ,mBAAmB,CAAC+P,iBAAiB,CAAC9P,WAAW,CAAC+P,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACpQ,mBAAmB,CAAC+P,iBAAiB,CAAC9P,WAAW,CAAC+P,YAAY,CAAC,CAAC;UACjG,IAAIE,eAAe,CAACG,kBAAkB,IAAI,IAAI,IAAIH,eAAe,CAACG,kBAAkB,IAAIJ,SAAS,EAAE;YACjG,IAAIlG,KAAK,GAAG,IAAI,CAAC2E,gBAAgB,CAACwE,SAAS,CAAE3C,CAAM,IAAKA,CAAC,CAACjC,KAAK,IAAI4B,eAAe,CAACG,kBAAkB,CAAC/B,KAAK,CAAC;YAC5G,IAAI,CAAC3L,WAAW,CAAC0N,kBAAkB,GAAG,IAAI,CAAC3B,gBAAgB,CAAC3E,KAAK,CAAC;UACpE,CAAC,MAAM;YACL,IAAI,CAACpH,WAAW,CAAC0N,kBAAkB,GAAG,IAAI,CAAC3B,gBAAgB,CAAC,CAAC,CAAC;UAChE;QACF;MACF;IACF,CAAC,CAAC;EACJ;EAKAyE,WAAWA,CAAA;IACT,IAAI,CAACC,WAAW,GAAG;MACjB7B,YAAY,EAAE,IAAI,CAAC5O,WAAW,CAACC,kBAAkB,CAACC,GAAG;MACrDwQ,SAAS,EAAE,IAAI,CAAClF,SAAS;MACzBmF,QAAQ,EAAE,IAAI,CAACpF;KAChB;IACD,IAAI,IAAI,CAACvL,WAAW,CAACgO,KAAK,IAAI,IAAI,CAAChO,WAAW,CAACiO,GAAG,EAAE;MAClD,IAAI,CAACwC,WAAW,CAAC,QAAQ,CAAC,GAAG;QAAEzC,KAAK,EAAE,IAAI,CAAChO,WAAW,CAACgO,KAAK;QAAEC,GAAG,EAAE,IAAI,CAACjO,WAAW,CAACiO;MAAG,CAAE;IAC3F;IACA,IAAI,IAAI,CAACjO,WAAW,CAAC0N,kBAAkB,EAAE;MACvC,IAAI,CAAC+C,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACzQ,WAAW,CAAC0N,kBAAkB,CAAC/B,KAAK;IAC5E;IACA,IAAI,IAAI,CAAC3L,WAAW,CAAC6D,kBAAkB,CAAC8H,KAAK,EAAE;MAC7C,IAAI,CAAC8E,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACzQ,WAAW,CAAC6D,kBAAkB,CAAC8H,KAAK;IAC5E;IACA,IAAI,OAAO,IAAI,CAAC3L,WAAW,CAAC+N,gBAAgB,CAACpC,KAAK,KAAK,SAAS,EAAE;MAChE,IAAI,CAAC8E,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAACzQ,WAAW,CAAC+N,gBAAgB,CAACpC,KAAK;IACzE;IACA,IAAI,IAAI,CAAC3L,WAAW,CAACkC,kBAAkB,CAACyJ,KAAK,EAAE;MAC7C,IAAI,CAAC8E,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACzQ,WAAW,CAACkC,kBAAkB,CAACyJ,KAAK;IAC5E;IACA,IAAI,IAAI,CAAC3L,WAAW,CAAC0C,iBAAiB,CAACiJ,KAAK,EAAE;MAC5C,IAAI,CAAC8E,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAACzQ,WAAW,CAAC0C,iBAAiB,CAACiJ,KAAK;IAC1E;IACA,IAAI,IAAI,CAAC3L,WAAW,CAAC6N,mBAAmB,CAAClC,KAAK,EAAE;MAC9C,IAAI,CAAC8E,WAAW,CAAC,aAAa,CAAC,GAAG,IAAI,CAACzQ,WAAW,CAAC6N,mBAAmB,CAAClC,KAAK;IAC9E;IACA,IAAI,IAAI,CAAC3L,WAAW,CAAC8N,wBAAwB,CAACnC,KAAK,EAAE;MACnD,IAAI,CAAC8E,WAAW,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAACzQ,WAAW,CAAC8N,wBAAwB,CAACnC,KAAK;IACxF;IAEA,OAAO,IAAI,CAAC8E,WAAW;EACzB;EAEAG,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACnQ,MAAM,IAAI,CAAC,KAAKkQ,CAAC,CAAClQ,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEA0N,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC5D,aAAa,CAACsG,6BAA6B,CAAC;MACtDjB,IAAI,EAAE,IAAI,CAACQ,WAAW;KACvB,CAAC,CAAC1D,IAAI,CACLlQ,GAAG,CAACmQ,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACoC,SAAS,GAAGnE,GAAG,CAAC8B,OAAO;QAC5B,IAAI,CAACpD,YAAY,GAAGsB,GAAG,CAACoE,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAIAC,0BAA0BA,CAAA;IACxB;IACA,IAAI,CAACjB,gBAAgB,EAAE;IACvB,IAAI,CAAC5B,YAAY,EAAE,CAACrB,SAAS,EAAE;EACjC;EACAgB,gBAAgBA,CAAA;IACd,IAAI,CAACrD,iBAAiB,CAACwG,6CAA6C,CAAC;MACnErB,IAAI,EAAE;QACJsB,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;;KAEZ,CAAC,CAACzE,IAAI,CACLlQ,GAAG,CAACmQ,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACvK,oBAAoB,GAAGwI,GAAG,CAAC8B,OAAO,EAAE5F,MAAM,GAAG8D,GAAG,CAAC8B,OAAO,CAACwB,GAAG,CAACtD,GAAG,IAAG;UACtE,OAAO;YACL9O,cAAc,EAAE8O,GAAG,CAAC9O,cAAc;YAClCiC,GAAG,EAAE6M,GAAG,CAAC7M;WACV;QACH,CAAC,CAAC,GAAG,EAAE;QAEP,IAAI7C,mBAAmB,CAAC+P,iBAAiB,CAAC9P,WAAW,CAAC+P,YAAY,CAAC,IAAI,IAAI,IACtEhQ,mBAAmB,CAAC+P,iBAAiB,CAAC9P,WAAW,CAAC+P,YAAY,CAAC,IAAIC,SAAS,IAC5EjQ,mBAAmB,CAAC+P,iBAAiB,CAAC9P,WAAW,CAAC+P,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACpQ,mBAAmB,CAAC+P,iBAAiB,CAAC9P,WAAW,CAAC+P,YAAY,CAAC,CAAC;UACjG,IAAIE,eAAe,CAACtN,kBAAkB,IAAI,IAAI,IAAIsN,eAAe,CAACtN,kBAAkB,IAAIqN,SAAS,EAAE;YACjG,IAAIlG,KAAK,GAAG,IAAI,CAAC7C,oBAAoB,CAACgM,SAAS,CAAE3C,CAAM,IAAKA,CAAC,CAAC1N,GAAG,IAAIqN,eAAe,CAACtN,kBAAkB,CAACC,GAAG,CAAC;YAC5G,IAAI,CAACF,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACsE,oBAAoB,CAAC6C,KAAK,CAAC;UACxE,CAAC,MAAM;YACL,IAAI,CAACpH,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACsE,oBAAoB,CAAC,CAAC,CAAC;UACpE;QACF,CAAC,MACI;UACH,IAAI,CAACvE,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACsE,oBAAoB,CAAC,CAAC,CAAC;QACpE;MACF;IACF,CAAC,CAAC,EACF3H,GAAG,CAAC,MAAK;MACP;MACA,IAAI,CAACuT,gBAAgB,EAAE;MACvBqB,UAAU,CAAC,MAAK;QACd,IAAI,CAACjD,YAAY,EAAE,CAACrB,SAAS,EAAE;MACjC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,CACH,CAACA,SAAS,EAAE;EACf;EA6CAuE,YAAYA,CAACtR,GAAQ,EAAEuR,GAAQ;IAC7B,IAAI,CAACzP,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC0I,aAAa,CAACgH,6BAA6B,CAAC;MAC/C3B,IAAI,EAAE;QAAE5D,QAAQ,EAAEjM;MAAG;KACtB,CAAC,CAAC+M,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC7L,WAAW,GAAG;UACjB,GAAG8J,GAAG,CAAC8B,OAAO;UACdzK,eAAe,EAAE2I,GAAG,CAAC8B,OAAO,CAAC+C,gBAAgB,GAAG,IAAIC,IAAI,CAAC9E,GAAG,CAAC8B,OAAO,CAAC+C,gBAAgB,CAAC,GAAGtE,SAAS;UAClGhJ,aAAa,EAAEyI,GAAG,CAAC8B,OAAO,CAACiD,cAAc,GAAG,IAAID,IAAI,CAAC9E,GAAG,CAAC8B,OAAO,CAACiD,cAAc,CAAC,GAAGxE;SACpF;QAED,IAAIP,GAAG,CAAC8B,OAAO,CAACkD,YAAY,EAAE;UAC5B,IAAI,CAAC9P,cAAc,CAAChC,kBAAkB,GAAG,IAAI,CAAC+R,eAAe,CAAC,IAAI,CAACzN,oBAAoB,EAAE,KAAK,EAAEwI,GAAG,CAAC8B,OAAO,CAACkD,YAAY,CAAC;QAC3H;QACA,IAAI,CAAC9P,cAAc,CAACC,kBAAkB,GAAG,IAAI,CAAC8P,eAAe,CAAC,IAAI,CAAC3P,OAAO,CAACC,gBAAgB,EAAE,OAAO,EAAEyK,GAAG,CAAC8B,OAAO,CAAC1N,UAAU,CAAC;QAC7H,IAAI4L,GAAG,CAAC8B,OAAO,CAAC9N,UAAU,EAAE;UAC1B,IAAI,CAACkB,cAAc,CAAC4B,kBAAkB,GAAG,IAAI,CAACmO,eAAe,CAAC,IAAI,CAAC3P,OAAO,CAACmC,gBAAgB,EAAE,OAAO,EAAEuI,GAAG,CAAC8B,OAAO,CAAC9N,UAAU,CAAC;QAC/H,CAAC,MAAM;UACL,IAAI,CAACkB,cAAc,CAAC4B,kBAAkB,GAAG,IAAI,CAACxB,OAAO,CAACmC,gBAAgB,CAAC,CAAC,CAAC;QAC3E;QACA,IAAI,CAACvC,cAAc,CAACS,iBAAiB,GAAG,IAAI,CAACsP,eAAe,CAAC,IAAI,CAAC3P,OAAO,CAACO,eAAe,EAAE,OAAO,EAAEmK,GAAG,CAAC8B,OAAO,CAACvC,SAAS,CAAC;QAE1H,IAAIS,GAAG,CAAC8B,OAAO,CAACkD,YAAY,EAAE;UAC5B,IAAI,IAAI,CAACjM,aAAa,EAAE;YACtB,IAAI,CAACA,aAAa,CAAC8I,YAAY,GAAG7B,GAAG,CAAC8B,OAAO,CAACkD,YAAY;UAC5D;QACF;QACA,IAAI,CAACvH,aAAa,CAACyH,IAAI,CAACP,GAAG,CAAC;MAC9B;IAEF,CAAC,CAAC;EACJ;EAGAM,eAAeA,CAACE,KAAY,EAAEtG,GAAW,EAAED,KAAU;IACnD,OAAOuG,KAAK,CAACvE,IAAI,CAACwE,IAAI,IAAIA,IAAI,CAACvG,GAAG,CAAC,KAAKD,KAAK,CAAC;EAChD;EAGAjM,eAAeA,CAACgS,GAAQ,EAAES,IAAS;IACjC,IAAI,CAACV,YAAY,CAACU,IAAI,CAAChS,GAAG,EAAEuR,GAAG,CAAC;EAClC;EAEAvS,SAASA,CAACuS,GAAQ;IAChB,IAAI,CAAC5L,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBlF,MAAM,EAAEyM,SAAS;MACjBrH,eAAe,EAAEqH;KAClB;IACD,IAAI,CAAC9C,aAAa,CAACyH,IAAI,CAACP,GAAG,CAAC;EAC9B;EAKAU,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOvV,MAAM,CAACuV,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEArN,cAAcA,CAACyM,GAAQ;IACrB,IAAI,CAACzO,WAAW,CAAC2O,gBAAgB,GAAG,IAAI,CAAC3O,WAAW,CAACmB,eAAe,GAAG,IAAI,CAACgO,UAAU,CAAC,IAAI,CAACnP,WAAW,CAACmB,eAAe,CAAC,GAAG,EAAE,EAC3H,IAAI,CAACnB,WAAW,CAAC6O,cAAc,GAAG,IAAI,CAAC7O,WAAW,CAACqB,aAAa,GAAG,IAAI,CAAC8N,UAAU,CAAC,IAAI,CAACnP,WAAW,CAACqB,aAAa,CAAC,GAAG,EAAE,EAEvH,IAAI,CAACiO,kBAAkB,GAAG;MACxBlP,aAAa,EAAE,IAAI,CAACJ,WAAW,CAACI,aAAa;MAC7CzC,UAAU,EAAE,IAAI,CAACqC,WAAW,CAACC,UAAU;MACvCkJ,QAAQ,EAAE,IAAI,CAACnJ,WAAW,CAACuP,GAAG;MAC9BzR,UAAU,EAAE,IAAI,CAACkB,cAAc,CAAC4B,kBAAkB,GAAG,IAAI,CAAC5B,cAAc,CAAC4B,kBAAkB,CAAC8H,KAAK,GAAG,IAAI;MACxG3K,SAAS,EAAE,IAAI,CAACiC,WAAW,CAACjC,SAAS;MACrCO,SAAS,EAAE,IAAI,CAAC0B,WAAW,CAAC1B,SAAS;MACrCkC,KAAK,EAAE,IAAI,CAACR,WAAW,CAACQ,KAAK;MAC7B4I,WAAW,EAAE,IAAI,CAACpJ,WAAW,CAACM,WAAW;MACzCpC,UAAU,EAAE,IAAI,CAACc,cAAc,CAACC,kBAAkB,GAAG,IAAI,CAACD,cAAc,CAACC,kBAAkB,CAACyJ,KAAK,GAAG,IAAI;MACxGhI,MAAM,EAAE,IAAI,CAACV,WAAW,CAACU,MAAM;MAC/B2I,SAAS,EAAE,IAAI,CAACrK,cAAc,CAACS,iBAAiB,GAAG,IAAI,CAACT,cAAc,CAACS,iBAAiB,CAACiJ,KAAK,GAAG,IAAI;MACrGiG,gBAAgB,EAAE,IAAI,CAAC3O,WAAW,CAAC2O,gBAAgB;MACnDE,cAAc,EAAE,IAAI,CAAC7O,WAAW,CAAC6O;KAClC;IACH,IAAI,CAACW,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC/H,KAAK,CAACgI,aAAa,CAACzJ,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACwB,OAAO,CAACkI,aAAa,CAAC,IAAI,CAACjI,KAAK,CAACgI,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAC/H,aAAa,CAACiI,0BAA0B,CAAC;MAC5C5C,IAAI,EAAE,IAAI,CAACuC;KACZ,CAAC,CAACzF,IAAI,CACLlQ,GAAG,CAACmQ,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC+B,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACrE,OAAO,CAACyF,aAAa,CAAC,MAAM,CAAC;QAClCwB,GAAG,CAACmB,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACpI,OAAO,CAACuE,YAAY,CAACjC,GAAG,CAACkC,OAAQ,CAAC;QACvCyC,GAAG,CAACmB,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACFlW,SAAS,CAAC,MAAM,IAAI,CAAC4R,YAAY,EAAE,CAAC,CACrC,CAACrB,SAAS,EAAE;EACf;EAGA4F,QAAQA,CAACpB,GAAQ;IACf,IAAIqB,OAAO,GAAkB;MAC3B1P,aAAa,EAAE,IAAI,CAACJ,WAAW,CAACI,aAAa;MAC7CzC,UAAU,EAAE,IAAI,CAACqC,WAAW,CAACC,UAAU;MACvCkJ,QAAQ,EAAE,IAAI,CAACnJ,WAAW,CAACuP,GAAG;MAC9BzR,UAAU,EAAE,IAAI,CAACkC,WAAW,CAAClC,UAAU;MACvCC,SAAS,EAAE,IAAI,CAACiC,WAAW,CAACjC,SAAS;MACrCO,SAAS,EAAE,IAAI,CAAC0B,WAAW,CAAC1B,SAAS;MACrCkC,KAAK,EAAE,IAAI,CAACR,WAAW,CAACQ,KAAK;MAC7B4I,WAAW,EAAE,IAAI,CAACpJ,WAAW,CAACM,WAAW;MACzCpC,UAAU,EAAE,IAAI,CAAC8B,WAAW,CAAC9B,UAAU;MACvCwC,MAAM,EAAE,IAAI,CAACV,WAAW,CAACU,MAAM;MAC/B2I,SAAS,EAAE,IAAI,CAACrJ,WAAW,CAACqJ;KAC7B;IACD,IAAI,CAAC3B,aAAa,CAACiI,0BAA0B,CAAC;MAC5C5C,IAAI,EAAE+C;KACP,CAAC,CAAC7F,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC+B,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACrE,OAAO,CAACyF,aAAa,CAAC,MAAM,CAAC;QAClCwB,GAAG,CAACmB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EAEJ;EAEAxN,OAAOA,CAACqM,GAAQ;IACdA,GAAG,CAACmB,KAAK,EAAE;EACb;EAEAG,YAAYA,CAACC,IAAS,EAAEC,EAAQ;IAC9B,MAAMC,KAAK,GAAGD,EAAE,GAAGA,EAAE,GAAG,IAAI,CAAClT,WAAW,CAACC,kBAAkB,CAACC,GAAG;IAC/D,IAAI,CAAC6K,MAAM,CAACqI,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEE,KAAK,CAAC,CAAC;EACtE;EAEApT,4BAA4BA,CAACkT,IAAS,EAAEI,WAAgB,EAAEC,OAAY;IACpE,IAAI,CAACvI,MAAM,CAACqI,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEI,WAAW,EAAEC,OAAO,CAAC,CAAC;EACrF;EAEA/S,cAAcA,CAAC4R,IAAS;IACtB,IAAIoB,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,IAAI,CAAC5I,aAAa,CAAC6I,oCAAoC,CAAC;QACtDxD,IAAI,EAAEmC,IAAI,CAAChS;OACZ,CAAC,CAAC+M,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACrE,OAAO,CAACyF,aAAa,CAAC,MAAM,CAAC;QACpC;MACF,CAAC,CAAC;IACJ;EACF;EAIAuC,UAAUA,CAAA;IACR,IAAI,CAAC/H,KAAK,CAAC+I,KAAK,EAAE;IAClB,IAAI,CAAC/I,KAAK,CAACgJ,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACzQ,WAAW,CAACuP,GAAG,CAAC;IACnD,IAAI,CAAC9H,KAAK,CAACgJ,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACnB,kBAAkB,CAAC3R,UAAU,CAAC;IACjE,IAAI,CAAC8J,KAAK,CAACiJ,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACpB,kBAAkB,CAAC3R,UAAU,EAAE,EAAE,CAAC;IAC5E,IAAI,CAAC8J,KAAK,CAACgJ,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACzQ,WAAW,CAACpC,MAAM,CAAC;IACpD,IAAI,CAAC6J,KAAK,CAACiJ,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACpB,kBAAkB,CAAClP,aAAa,EAAE,EAAE,CAAC;IACjF;IACA;IACA;IACA,IAAI,CAACqH,KAAK,CAACkJ,OAAO,CAAC,QAAQ,EAAE,IAAI,CAACrB,kBAAkB,CAAC9O,KAAK,EAAE,IAAI,CAACqH,OAAO,CAAC+I,WAAW,CAAC;IACrF,IAAI,CAACnJ,KAAK,CAACoJ,aAAa,CAAC,QAAQ,EAAE,IAAI,CAACvB,kBAAkB,CAAC5O,MAAM,CAAC;IAClE,IAAI,CAAC+G,KAAK,CAACgJ,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACnB,kBAAkB,CAACjG,SAAS,CAAC;IAC9D,IAAI,CAAC5B,KAAK,CAACgJ,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACzR,cAAc,CAAC4B,kBAAkB,CAAC8H,KAAK,CAAC;IAC3E,IAAI,CAACjB,KAAK,CAACgJ,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACzR,cAAc,CAACC,kBAAkB,CAACyJ,KAAK,CAAC;IAC3E,IAAI,IAAI,CAAC1I,WAAW,CAAC2O,gBAAgB,EAAE;MACrC,IAAI,CAAClH,KAAK,CAACgJ,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACzQ,WAAW,CAAC6O,cAAc,CAAC;IAClE;IACA,IAAI,IAAI,CAAC7O,WAAW,CAAC6O,cAAc,EAAE;MACnC,IAAI,CAACpH,KAAK,CAACgJ,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACzQ,WAAW,CAAC2O,gBAAgB,CAAC;IACpE;IACA,IAAI,CAAClH,KAAK,CAACqJ,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC9Q,WAAW,CAAC2O,gBAAgB,GAAG,IAAI,CAAC3O,WAAW,CAAC2O,gBAAgB,GAAG,EAAE,EAAE,IAAI,CAAC3O,WAAW,CAAC6O,cAAc,GAAG,IAAI,CAAC7O,WAAW,CAAC6O,cAAc,GAAG,EAAE,CAAC;EAC9L;EAEAkC,uBAAuBA,CAAA;IACrB,IAAI,CAACtJ,KAAK,CAAC+I,KAAK,EAAE;IAClB,IAAI,CAAC/I,KAAK,CAACgJ,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5N,aAAa,CAAC8I,YAAY,CAAC;IAC5D,IAAI,CAAClE,KAAK,CAACgJ,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5N,aAAa,CAACC,aAAa,CAAC;IAC7D,IAAI,CAAC2E,KAAK,CAACiJ,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC7N,aAAa,CAACC,aAAa,EAAE,EAAE,CAAC;IAC1E,IAAI,CAAC2E,KAAK,CAACuJ,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAACnO,aAAa,CAACjF,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC;IAChF,IAAI,CAAC6J,KAAK,CAACuJ,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAACnO,aAAa,CAACG,eAAe,EAAE,CAAC,EAAE,GAAG,CAAC;EAC1F;EAGAN,gBAAgBA,CAAC+L,GAAQ;IACvB,IAAI,CAAC5L,aAAa,CAAC8I,YAAY,GAAG,IAAI,CAAC5O,WAAW,CAACC,kBAAkB,CAACC,GAAG,EACvE,IAAI,CAAC8T,uBAAuB,EAAE;IAChC,IAAI,IAAI,CAACtJ,KAAK,CAACgI,aAAa,CAACzJ,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACwB,OAAO,CAACkI,aAAa,CAAC,IAAI,CAACjI,KAAK,CAACgI,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAAC9H,qBAAqB,CAACsJ,yCAAyC,CAAC;MACnElE,IAAI,EAAE,IAAI,CAAClK;KACZ,CAAC,CAACgH,IAAI,CACLlQ,GAAG,CAACmQ,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC+B,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACrE,OAAO,CAACyF,aAAa,CAAC,MAAM,CAAC;QAClCwB,GAAG,CAACmB,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACFlW,SAAS,CAAC,MAAM,IAAI,CAAC4R,YAAY,EAAE,CAAC,CACrC,CAACrB,SAAS,EAAE;EACf,CAAC,CAAE;EACGxM,aAAaA,CAACyT,MAAW,EAAEhC,IAAS;IAAA,IAAAiC,KAAA;IAAA,OAAAC,iBAAA;MACxCD,KAAI,CAACpK,YAAY,GAAGmI,IAAI;MACxBiC,KAAI,CAACpL,cAAc,GAAG,EAAE;MACxBoL,KAAI,CAACnK,WAAW,GAAG,CAAC;MACpBmK,KAAI,CAAC1H,kBAAkB,GAAG,CAAC,CAAC,CAAC;MAC7B0H,KAAI,CAAC9L,mBAAmB,GAAG,IAAI,CAAC,CAAC;MACjC;MACA8L,KAAI,CAAC7H,iBAAiB,GAAG,KAAK;MAC9B6H,KAAI,CAAC5H,uBAAuB,GAAG,CAAC;MAChC4H,KAAI,CAAClK,mBAAmB,GAAG,CAAC;MAC5BkK,KAAI,CAACjK,gBAAgB,GAAG,CAAC;MACzBiK,KAAI,CAAC3H,mBAAmB,GAAG,IAAI;MAE/B;MACA,IAAI;QACF,MAAM6H,QAAQ,SAASF,KAAI,CAAClJ,gBAAgB,CAACqJ,qBAAqB,CAACpC,IAAI,CAAChS,GAAG,CAAC,CAACqU,SAAS,EAAE;QAExF,IAAIF,QAAQ,IAAIA,QAAQ,CAACxF,UAAU,KAAK,CAAC,IAAIwF,QAAQ,CAACzF,OAAO,EAAE;UAC7D;UACAuF,KAAI,CAAC1H,kBAAkB,GAAG4H,QAAQ,CAACzF,OAAO,CAAC4F,mBAAmB,IAAI,CAAC;UACnE;UACA,IAAIH,QAAQ,CAACzF,OAAO,CAACvN,gBAAgB,KAAK,CAAC,EAAE;YAAE;YAC7C8S,KAAI,CAAC9L,mBAAmB,GAAG,KAAK;UAClC,CAAC,MAAM;YACL8L,KAAI,CAAC9L,mBAAmB,GAAG,IAAI;UACjC;UAEA;UACA8L,KAAI,CAAC3H,mBAAmB,GAAG,IAAI;UAC/B2H,KAAI,CAAC7H,iBAAiB,GAAG,KAAK;UAC9B6H,KAAI,CAAC5H,uBAAuB,GAAG,CAAC;UAEhC;UACA,IAAI8H,QAAQ,CAACzF,OAAO,CAAC6F,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACzF,OAAO,CAAC6F,KAAK,CAAC,EAAE;YACnE;YACAN,KAAI,CAACpL,cAAc,GAAGsL,QAAQ,CAACzF,OAAO,CAAC6F,KAAK,CAACrE,GAAG,CAAEwE,KAAU,KAAM;cAChEC,QAAQ,EAAER,QAAQ,CAACzF,OAAO,CAACzC,QAAQ,IAAI+F,IAAI,CAAChS,GAAG;cAC/C4U,YAAY,EAAET,QAAQ,CAACzF,OAAO,CAACmG,YAAY;cAC3CrN,SAAS,EAAEkN,KAAK,CAACI,SAAS,IAAI,EAAE;cAChCxN,SAAS,EAAEoN,KAAK,CAACK,SAAS,IAAI,EAAE;cAChClN,KAAK,EAAE6M,KAAK,CAACM,KAAK,IAAI,EAAE;cACxBtN,UAAU,EAAEgN,KAAK,CAACO,UAAU,IAAI,CAAC;cACjClN,MAAM,EAAE2M,KAAK,CAACQ,MAAM,IAAI,CAAC;cACzBC,OAAO,EAAET,KAAK,CAACtD,OAAO,IAAI,CAAC;cAC3BhU,kBAAkB,EAAEsX,KAAK,CAACtX,kBAAkB,IAAIsX,KAAK,CAACtX,kBAAkB,GAAG,CAAC,GAAGsX,KAAK,CAACtX,kBAAkB,GAAGA,kBAAkB,CAACgY,GAAG;cAChIC,OAAO,EAAEX,KAAK,CAACY,OAAO,IAAI,EAAE;cAC5BC,gBAAgB,EAAEb,KAAK,CAACvT;aACzB,CAAC,CAAC;YACH8S,KAAI,CAACtM,cAAc,EAAE;UACvB,CAAC,MAAM,CAEP;QACF,CAAC,MAAM,CAEP;MACF,CAAC,CAAC,OAAO6N,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;MAEAvB,KAAI,CAAC5J,aAAa,CAACyH,IAAI,CAACkC,MAAM,EAAE;QAC9B0B,OAAO,EAAE1D,IAAI;QACb2D,oBAAoB,EAAE;OACvB,CAAC;IAAC;EACL;EAEA;EACAnN,kBAAkBA,CAAA;IAChB,IAAI,CAAC+D,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAAC1D,cAAc,GAAG,EAAE;IACxB,IAAI,CAACV,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC2B,WAAW,GAAG,CAAC;IACpB,IAAI,CAACE,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACD,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAACuC,mBAAmB,GAAG,IAAI;IAE/B;IACA,IAAI,CAAChC,OAAO,CAACyF,aAAa,CAAC,eAAe,CAAC;EAC7C;EACA;EACAvJ,gBAAgBA,CAAA;IACd,IAAI,CAACqC,cAAc,CAAC+M,IAAI,CAAC;MACvBjB,QAAQ,EAAE,IAAI,CAAC9K,YAAY,EAAE7J,GAAG,IAAI,CAAC;MACrCwH,SAAS,EAAE,EAAE;MACbF,SAAS,EAAE,EAAE;MACbO,KAAK,EAAE,EAAE;MACTH,UAAU,EAAE,CAAC;MACbK,MAAM,EAAE,CAAC;MACToN,OAAO,EAAE,CAAC;MACV/X,kBAAkB,EAAEA,kBAAkB,CAACgY,GAAG;MAC1CC,OAAO,EAAE;KACV,CAAC;EACJ;EAEA;EACAQ,sBAAsBA,CAACC,MAA+B;IACpDL,OAAO,CAACM,GAAG,CAAC,WAAW,EAAED,MAAM,CAAC;IAEhC,IAAI,CAAC,IAAI,CAACjM,YAAY,EAAE;MACtB,IAAI,CAACS,OAAO,CAACuE,YAAY,CAAC,QAAQ,CAAC;MACnC;IACF;IAEA,IAAI,CAACiH,MAAM,CAACE,aAAa,IAAIF,MAAM,CAACE,aAAa,CAAClN,MAAM,KAAK,CAAC,EAAE;MAC9D,IAAI,CAACwB,OAAO,CAACuE,YAAY,CAAC,WAAW,CAAC;MACtC;IACF;IAEA;IACA,MAAMoH,iBAAiB,GAAoBH,MAAM,CAACE,aAAa,CAAC9F,GAAG,CAAC8B,IAAI,IAAG;MACzE,OAAO;QACL2C,QAAQ,EAAE,IAAI,CAAC9K,YAAY,CAAC7J,GAAG;QAC/BwH,SAAS,EAAEwK,IAAI,CAACkE,YAAY,IAAI,OAAO;QACvC5O,SAAS,EAAE0K,IAAI,CAAC+C,SAAS,IAAI,EAAE;QAC/BlN,KAAK,EAAEmK,IAAI,CAACgD,KAAK,IAAI,EAAE;QACvBtN,UAAU,EAAEsK,IAAI,CAACiD,UAAU,IAAI,CAAC;QAChClN,MAAM,EAAE,CAAC;QACToN,OAAO,EAAE,CAAC;QACV/X,kBAAkB,EAAEA,kBAAkB,CAAC+Y,IAAI;QAC3Cd,OAAO,EAAErD,IAAI,CAACsD,OAAO,IAAI;OAC1B;IACH,CAAC,CAAC;IAEF;IACA,IAAI,CAACzM,cAAc,GAAG,CAAC,GAAG,IAAI,CAACA,cAAc,EAAE,GAAGoN,iBAAiB,CAAC;IAEpE;IACA,IAAI,CAACtO,cAAc,EAAE;IAErB,IAAI,CAAC2C,OAAO,CAACyF,aAAa,CAAC,QAAQ+F,MAAM,CAACE,aAAa,CAAClN,MAAM,QAAQ,CAAC;EACzE;EAEAsN,oBAAoBA,CAACC,YAAoB;IACvC,IAAI,CAAC/L,OAAO,CAACuE,YAAY,CAACwH,YAAY,CAAC;EACzC;EACA;EACM3P,gBAAgBA,CAAA;IAAA,IAAA4P,MAAA;IAAA,OAAApC,iBAAA;MACpB,IAAI;QACF,IAAI,CAACoC,MAAI,CAACzM,YAAY,EAAE7J,GAAG,EAAE;UAC3BsW,MAAI,CAAChM,OAAO,CAACuE,YAAY,CAAC,QAAQ,CAAC;UACnC;QACF;QAEA,MAAM0H,OAAO,GAAG;UACd9H,YAAY,EAAE6H,MAAI,CAACzW,WAAW,EAAEC,kBAAkB,EAAEC,GAAG,IAAI,CAAC;UAC5DkM,QAAQ,EAAEqK,MAAI,CAACzM,YAAY,CAAC7J;SAC7B;QAED,MAAMmU,QAAQ,SAASmC,MAAI,CAACvL,gBAAgB,CAACrE,gBAAgB,CAAC6P,OAAO,CAAC,CAAClC,SAAS,EAAE;QAClF,IAAIF,QAAQ,EAAEqC,OAAO,IAAIrC,QAAQ,CAACsC,IAAI,EAAE;UACtC,MAAMC,YAAY,GAAGvC,QAAQ,CAACsC,IAAI,CAACvG,GAAG,CAAEzC,CAAM,KAAM;YAClDkH,QAAQ,EAAE2B,MAAI,CAACzM,YAAY,EAAE7J,GAAG;YAChCwH,SAAS,EAAEiG,CAAC,CAACjG,SAAS;YACtBF,SAAS,EAAEmG,CAAC,CAACnG,SAAS,IAAI,EAAE;YAC5BO,KAAK,EAAE4F,CAAC,CAAC5F,KAAK,IAAI,EAAE;YACpBH,UAAU,EAAE+F,CAAC,CAAC/F,UAAU;YACxBK,MAAM,EAAE0F,CAAC,CAAC1F,MAAM;YAChBoN,OAAO,EAAE1H,CAAC,CAAC0H,OAAO;YAClB/X,kBAAkB,EAAEA,kBAAkB,CAAC+Y,IAAI;YAC3Cd,OAAO,EAAE5H,CAAC,CAAC4H;WACZ,CAAC,CAAC;UACHiB,MAAI,CAACzN,cAAc,CAAC+M,IAAI,CAAC,GAAGc,YAAY,CAAC;UACzCJ,MAAI,CAAC3O,cAAc,EAAE;UACrB2O,MAAI,CAAChM,OAAO,CAACyF,aAAa,CAAC,UAAU,CAAC;QACxC,CAAC,MAAM;UACLuG,MAAI,CAAChM,OAAO,CAACuE,YAAY,CAACsF,QAAQ,EAAE7J,OAAO,IAAI,UAAU,CAAC;QAC5D;MACF,CAAC,CAAC,OAAOkL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCc,MAAI,CAAChM,OAAO,CAACuE,YAAY,CAAC,UAAU,CAAC;MACvC;IAAC;EACH;EAEA;EACMjI,gBAAgBA,CAAA;IAAA,IAAA+P,MAAA;IAAA,OAAAzC,iBAAA;MACpB,IAAI;QACF,IAAI,CAACyC,MAAI,CAAC9M,YAAY,EAAE7J,GAAG,EAAE;UAC3B2W,MAAI,CAACrM,OAAO,CAACuE,YAAY,CAAC,QAAQ,CAAC;UACnC;QACF;QAEA,MAAM0H,OAAO,GAAG;UACd9H,YAAY,EAAEkI,MAAI,CAAC9W,WAAW,EAAEC,kBAAkB,EAAEC,GAAG,IAAI,CAAC;UAC5DkM,QAAQ,EAAE0K,MAAI,CAAC9M,YAAY,CAAC7J;SAC7B;QAED,MAAMmU,QAAQ,SAASwC,MAAI,CAAC5L,gBAAgB,CAACnE,gBAAgB,CAAC2P,OAAO,CAAC,CAAClC,SAAS,EAAE;QAClF,IAAIF,QAAQ,EAAEqC,OAAO,IAAIrC,QAAQ,CAACsC,IAAI,EAAE;UACtC,MAAMG,YAAY,GAAGzC,QAAQ,CAACsC,IAAI,CAACvG,GAAG,CAAEzC,CAAM,KAAM;YAClDkH,QAAQ,EAAEgC,MAAI,CAAC9M,YAAY,EAAE7J,GAAG;YAChCwH,SAAS,EAAEiG,CAAC,CAACjG,SAAS;YACtBF,SAAS,EAAEmG,CAAC,CAACnG,SAAS,IAAI,EAAE;YAC5BO,KAAK,EAAE4F,CAAC,CAAC5F,KAAK,IAAI,EAAE;YACpBH,UAAU,EAAE+F,CAAC,CAAC/F,UAAU;YACxBK,MAAM,EAAE0F,CAAC,CAAC1F,MAAM;YAChBoN,OAAO,EAAE1H,CAAC,CAAC0H,OAAO;YAClB/X,kBAAkB,EAAEA,kBAAkB,CAACyZ,EAAE;YAAE;YAC3CxB,OAAO,EAAE5H,CAAC,CAAC4H,OAAO,IAAI;WACvB,CAAC,CAAC;UACHsB,MAAI,CAAC9N,cAAc,CAAC+M,IAAI,CAAC,GAAGgB,YAAY,CAAC;UACzCD,MAAI,CAAChP,cAAc,EAAE;UACrBgP,MAAI,CAACrM,OAAO,CAACyF,aAAa,CAAC,UAAU,CAAC;QACxC,CAAC,MAAM;UACL4G,MAAI,CAACrM,OAAO,CAACuE,YAAY,CAACsF,QAAQ,EAAE7J,OAAO,IAAI,UAAU,CAAC;QAC5D;MACF,CAAC,CAAC,OAAOkL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCmB,MAAI,CAACrM,OAAO,CAACuE,YAAY,CAAC,UAAU,CAAC;MACvC;IAAC;EACH;EAEA;EACA3H,mBAAmBA,CAACD,KAAa;IAC/B,MAAM+K,IAAI,GAAG,IAAI,CAACnJ,cAAc,CAAC5B,KAAK,CAAC;IACvC,IAAI,CAAC4B,cAAc,CAACiO,MAAM,CAAC7P,KAAK,EAAE,CAAC,CAAC;IACpC,IAAI,CAACU,cAAc,EAAE;EACvB;EAEA;EACAA,cAAcA,CAAA;IACZ,IAAI,CAACmC,WAAW,GAAG,IAAI,CAACjB,cAAc,CAACkO,MAAM,CAAC,CAACC,GAAG,EAAEhF,IAAI,KAAI;MAC1D,OAAOgF,GAAG,GAAIhF,IAAI,CAACtK,UAAU,GAAGsK,IAAI,CAACjK,MAAO;IAC9C,CAAC,EAAE,CAAC,CAAC;IACL,IAAI,CAACkP,mBAAmB,EAAE;EAC5B;EAEA;EACAA,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAAClN,mBAAmB,GAAGmN,IAAI,CAACC,KAAK,CAAC,IAAI,CAACrN,WAAW,GAAG,IAAI,CAAC;IAC9D,IAAI,CAACE,gBAAgB,GAAG,IAAI,CAACF,WAAW,GAAG,IAAI,CAACC,mBAAmB;EACrE;EAEA;EACA3B,cAAcA,CAACgP,MAAc;IAC3B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACtF,MAAM,CAACiF,MAAM,CAAC;EACnB;EAIA;EACMnO,aAAaA,CAACsI,GAAQ;IAAA,IAAAmG,MAAA;IAAA,OAAAxD,iBAAA;MAC1B,IAAIwD,MAAI,CAAC7O,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;QACpC4O,MAAI,CAACpN,OAAO,CAACuE,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA;MACA,MAAM8I,gBAAgB,GAAGD,MAAI,CAAC7O,cAAc,CAAC+O,MAAM,CAAC5F,IAAI,IACtD,CAACA,IAAI,CAACxK,SAAS,CAACqQ,IAAI,EAAE,CACvB;MAED,IAAIF,gBAAgB,CAAC7O,MAAM,GAAG,CAAC,EAAE;QAC/B4O,MAAI,CAACpN,OAAO,CAACuE,YAAY,CAAC,iBAAiB,CAAC;QAC5C;MACF;MAEA;MACA,MAAMiJ,gBAAgB,GAAGJ,MAAI,CAAC7O,cAAc,CAAC+O,MAAM,CAAC5F,IAAI,IACtD,CAACA,IAAI,CAACnK,KAAK,IAAI,CAACmK,IAAI,CAACnK,KAAK,CAACgQ,IAAI,EAAE,CAClC;MAED,IAAIC,gBAAgB,CAAChP,MAAM,GAAG,CAAC,EAAE;QAC/B4O,MAAI,CAACpN,OAAO,CAACuE,YAAY,CAAC,kBAAkB,CAAC;QAC7C;MACF;MAEA,IAAI;QACF,MAAM0H,OAAO,GAAG;UACdpD,OAAO,EAAEuE,MAAI,CAAC7N,YAAY,CAAC7J,GAAG;UAC9B+X,KAAK,EAAEL,MAAI,CAAC7O,cAAc;UAC1BmP,WAAW,EAAEN,MAAI,CAACnL,kBAAkB;UAAE;UACtC;UACA0L,UAAU,EAAEP,MAAI,CAACpL,mBAAmB;UAAE;UACtC4L,UAAU,EAAER,MAAI,CAACtL,iBAAiB;UAAI;UACtC+L,aAAa,EAAET,MAAI,CAACrL,uBAAuB,CAAC;SAC7C;QAED,MAAM8H,QAAQ,SAASuD,MAAI,CAAC3M,gBAAgB,CAAC9B,aAAa,CAACsN,OAAO,CAAC,CAAClC,SAAS,EAAE;QAC/E,IAAIF,QAAQ,EAAEqC,OAAO,EAAE;UACrBkB,MAAI,CAACpN,OAAO,CAACyF,aAAa,CAAC,SAAS,CAAC;UACrCwB,GAAG,CAACmB,KAAK,EAAE;QACb,CAAC,MAAM;UACLgF,MAAI,CAACpN,OAAO,CAACuE,YAAY,CAACsF,QAAQ,EAAE7J,OAAO,IAAI,MAAM,CAAC;QACxD;MACF,CAAC,CAAC,OAAOkL,KAAK,EAAE;QACdkC,MAAI,CAACpN,OAAO,CAACuE,YAAY,CAAC,SAAS,CAAC;MACtC;IAAC;EACH;EAEA;EACMuJ,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAnE,iBAAA;MACnB,IAAI;QACF,MAAMoE,IAAI,SAA2BD,MAAI,CAACtN,gBAAgB,CAACqN,eAAe,CAACC,MAAI,CAACxO,YAAY,CAAC7J,GAAG,CAAC,CAACqU,SAAS,EAAE;QAC7G,IAAIiE,IAAI,EAAE;UACR,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;UAC5C,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;UACfI,IAAI,CAACI,QAAQ,GAAG,OAAOV,MAAI,CAACxO,YAAY,CAACpJ,UAAU,IAAI4X,MAAI,CAACxO,YAAY,CAACnJ,MAAM,OAAO;UACtFiY,IAAI,CAACzJ,KAAK,EAAE;UACZsJ,MAAM,CAACC,GAAG,CAACO,eAAe,CAACT,GAAG,CAAC;QACjC,CAAC,MAAM;UACLF,MAAI,CAAC/N,OAAO,CAACuE,YAAY,CAAC,iBAAiB,CAAC;QAC9C;MACF,CAAC,CAAC,OAAO2G,KAAK,EAAE;QACd6C,MAAI,CAAC/N,OAAO,CAACuE,YAAY,CAAC,SAAS,CAAC;MACtC;IAAC;EACH;EAEA;EACArF,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACX,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;MACpC,IAAI,CAACwB,OAAO,CAACuE,YAAY,CAAC,YAAY,CAAC;MACvC;IACF;IAEA,IAAI;MACF;MACA,MAAMoK,YAAY,GAAG,IAAI,CAACC,oBAAoB,EAAE;MAEhD;MACA,MAAMC,WAAW,GAAGX,MAAM,CAAC1G,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,mDAAmD,CAAC;MAClG,IAAIqH,WAAW,EAAE;QACfA,WAAW,CAACP,QAAQ,CAAC9G,IAAI,EAAE;QAC3BqH,WAAW,CAACP,QAAQ,CAACQ,KAAK,CAACH,YAAY,CAAC;QACxCE,WAAW,CAACP,QAAQ,CAAClG,KAAK,EAAE;QAE5B;QACAyG,WAAW,CAACE,MAAM,GAAG;UACnBhI,UAAU,CAAC,MAAK;YACd8H,WAAW,CAACG,KAAK,EAAE;YACnB;UACF,CAAC,EAAE,GAAG,CAAC;QACT,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAAChP,OAAO,CAACuE,YAAY,CAAC,yBAAyB,CAAC;MACtD;IACF,CAAC,CAAC,OAAO2G,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,IAAI,CAAClL,OAAO,CAACuE,YAAY,CAAC,YAAY,CAAC;IACzC;EACF;EAEA;EACQqK,oBAAoBA,CAAA;IAC1B;IACA,MAAMK,QAAQ,GAAGld,kBAAkB;IAEnC;IACA,MAAMmd,WAAW,GAAG,IAAI9H,IAAI,EAAE,CAAC+H,kBAAkB,CAAC,OAAO,CAAC;IAC1D,MAAMC,aAAa,GAAG,IAAI,CAAC7Z,WAAW,CAACC,kBAAkB,EAAEhC,cAAc,IAAI,EAAE;IAE/E;IACA,IAAI6b,SAAS,GAAG,EAAE;IAClB,IAAI,CAAC9Q,cAAc,CAAC+Q,OAAO,CAAC,CAAC5H,IAAI,EAAE/K,KAAK,KAAI;MAC1C,MAAM4S,QAAQ,GAAG7H,IAAI,CAACtK,UAAU,GAAGsK,IAAI,CAACjK,MAAM;MAC9C,MAAM+R,aAAa,GAAG,IAAI,CAACzR,oBAAoB,CAAC2J,IAAI,CAAC5U,kBAAkB,CAAC;MACxE,MAAM2c,IAAI,GAAG/H,IAAI,CAACnK,KAAK,IAAI,EAAE;MAC7B,MAAMmS,QAAQ,GAAGhI,IAAI,CAAC1K,SAAS,IAAI,EAAE;MACrCqS,SAAS,IAAI;;sCAEmB1S,KAAK,GAAG,CAAC;sCACT+S,QAAQ;kBAC5BhI,IAAI,CAACxK,SAAS;qCACK,IAAI,CAACY,cAAc,CAAC4J,IAAI,CAACtK,UAAU,CAAC;sCACnCqS,IAAI;sCACJ/H,IAAI,CAACjK,MAAM;qCACZ,IAAI,CAACK,cAAc,CAACyR,QAAQ,CAAC;sCAC5BC,aAAa;;SAE1C;IACL,CAAC,CAAC;IAEF;IACA,MAAMG,iBAAiB,GAAG,IAAI,CAAC3N,mBAAmB,GAAG;;YAE7C,IAAI,CAACF,iBAAiB,KAAK,IAAI,CAACC,uBAAuB,MAAM,IAAI,CAACjE,cAAc,CAAC,IAAI,CAAC2B,mBAAmB,CAAC;;OAE/G,GAAG,EAAE;IAER;IACA,MAAMmQ,IAAI,GAAGX,QAAQ,CAClBY,OAAO,CAAC,oBAAoB,EAAET,aAAa,CAAC,CAC5CS,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAACtQ,YAAY,EAAEpJ,UAAU,IAAI,EAAE,CAAC,CAC9D0Z,OAAO,CAAC,YAAY,EAAE,IAAI,CAACtQ,YAAY,EAAEnJ,MAAM,IAAI,EAAE,CAAC,CACtDyZ,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAACtQ,YAAY,EAAE3G,aAAa,IAAI,EAAE,CAAC,CACpEiX,OAAO,CAAC,gBAAgB,EAAEX,WAAW,CAAC,CACtCW,OAAO,CAAC,gBAAgB,EAAER,SAAS,CAAC,CACpCQ,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC/R,cAAc,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAAC,CACrEqQ,OAAO,CAAC,wBAAwB,EAAEF,iBAAiB,CAAC,CACpDE,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC/R,cAAc,CAAC,IAAI,CAAC4B,gBAAgB,CAAC,CAAC,CACvEmQ,OAAO,CAAC,oBAAoB,EAAE,IAAIzI,IAAI,EAAE,CAAC0I,cAAc,CAAC,OAAO,CAAC,CAAC;IAEpE,OAAOF,IAAI;EACb;EAGA;EACMtR,aAAaA,CAAC2I,GAAQ;IAAA,IAAA8I,MAAA;IAAA,OAAAnG,iBAAA;MAC1B,IAAImG,MAAI,CAACxR,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;QACpCuR,MAAI,CAAC/P,OAAO,CAACuE,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA;MACA,MAAM8I,gBAAgB,GAAG0C,MAAI,CAACxR,cAAc,CAAC+O,MAAM,CAAC5F,IAAI,IACtD,CAACA,IAAI,CAACxK,SAAS,CAACqQ,IAAI,EAAE,CACvB;MAED,IAAIF,gBAAgB,CAAC7O,MAAM,GAAG,CAAC,EAAE;QAC/BuR,MAAI,CAAC/P,OAAO,CAACuE,YAAY,CAAC,iBAAiB,CAAC;QAC5C;MACF;MAEA;MACA,MAAMiJ,gBAAgB,GAAGuC,MAAI,CAACxR,cAAc,CAAC+O,MAAM,CAAC5F,IAAI,IACtD,CAACA,IAAI,CAACnK,KAAK,IAAI,CAACmK,IAAI,CAACnK,KAAK,CAACgQ,IAAI,EAAE,CAClC;MAED,IAAIC,gBAAgB,CAAChP,MAAM,GAAG,CAAC,EAAE;QAC/BuR,MAAI,CAAC/P,OAAO,CAACuE,YAAY,CAAC,kBAAkB,CAAC;QAC7C;MACF;MAEA,IAAI,CAACwL,MAAI,CAAC9N,kBAAkB,EAAE;QAC5B8N,MAAI,CAAC/P,OAAO,CAACuE,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA,IAAI;QACF,MAAMsF,QAAQ,SAASkG,MAAI,CAACtP,gBAAgB,CAACnC,aAAa,CAACyR,MAAI,CAAC9N,kBAAkB,CAAC,CAAC8H,SAAS,EAAE;QAE/F,IAAIF,QAAQ,CAACqC,OAAO,EAAE;UACpB6D,MAAI,CAAC/P,OAAO,CAACyF,aAAa,CAAC,UAAU,CAAC;UACtC0F,OAAO,CAACM,GAAG,CAAC,UAAU,EAAE;YACtBiC,WAAW,EAAEqC,MAAI,CAAC9N,kBAAkB;YACpCjC,OAAO,EAAE6J,QAAQ,CAAC7J;WACnB,CAAC;QACJ,CAAC,MAAM;UACL+P,MAAI,CAAC/P,OAAO,CAACuE,YAAY,CAACsF,QAAQ,CAAC7J,OAAO,IAAI,SAAS,CAAC;UACxDmL,OAAO,CAACD,KAAK,CAAC,UAAU,EAAErB,QAAQ,CAAC7J,OAAO,CAAC;QAC7C;QAEAiH,GAAG,CAACmB,KAAK,EAAE;MACb,CAAC,CAAC,OAAO8C,KAAK,EAAE;QACd6E,MAAI,CAAC/P,OAAO,CAACuE,YAAY,CAAC,SAAS,CAAC;QACpC4G,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IAAC;EACH;EAEA;EACAnN,oBAAoBA,CAACyR,aAAiC;IACpD,QAAQA,aAAa;MACnB,KAAK1c,kBAAkB,CAAC+Y,IAAI;QAC1B,OAAO,MAAM;MACf,KAAK/Y,kBAAkB,CAACgY,GAAG;QACzB,OAAO,MAAM;MACf,KAAKhY,kBAAkB,CAACyZ,EAAE;QACxB,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF;EAEA;EACAzQ,sBAAsBA,CAAC0P,MAA2B;IAChDL,OAAO,CAACM,GAAG,CAAC,WAAW,EAAED,MAAM,CAAC;IAEhC,IAAI,CAAC,IAAI,CAACjM,YAAY,EAAE;MACtB,IAAI,CAACS,OAAO,CAACuE,YAAY,CAAC,QAAQ,CAAC;MACnC;IACF;IAEA;IACA,IAAI,CAACyL,gCAAgC,CAACxE,MAAM,CAACE,aAAa,EAAEF,MAAM,CAACyE,eAAe,CAAC;EACrF;EAEAjU,eAAeA,CAAC+P,YAAoB;IAClC,IAAI,CAAC/L,OAAO,CAACuE,YAAY,CAACwH,YAAY,CAAC;EACzC;EAEA;EACQiE,gCAAgCA,CAACE,iBAAwB,EAAED,eAAmC;IACpG,IAAI,CAACC,iBAAiB,IAAIA,iBAAiB,CAAC1R,MAAM,KAAK,CAAC,EAAE;MACxD,IAAI,CAACwB,OAAO,CAACuE,YAAY,CAAC,WAAW,CAAC;MACtC;IACF;IAEA,IAAIoH,iBAAiB,GAAoB,EAAE;IAE3C;IACAuE,iBAAiB,CAACZ,OAAO,CAACL,QAAQ,IAAG;MACnC,MAAMkB,OAAO,GAAGF,eAAe,CAACG,GAAG,CAACnB,QAAQ,CAACoB,WAAW,CAAC;MACzD,IAAIF,OAAO,IAAIA,OAAO,CAAC3R,MAAM,GAAG,CAAC,EAAE;QACjC2R,OAAO,CAACb,OAAO,CAACgB,MAAM,IAAG;UACvB,MAAMC,OAAO,GAAkB;YAC7BlG,QAAQ,EAAE,IAAI,CAAC9K,YAAY,CAAC7J,GAAG;YAAE;YACjCwH,SAAS,EAAEoT,MAAM,CAAC1E,YAAY,IAAI0E,MAAM,CAACE,KAAK,IAAI,EAAE;YAAE;YACtDxT,SAAS,EAAEsT,MAAM,CAAC7F,SAAS,IAAI,EAAE;YAAE;YACnCrN,UAAU,EAAEkT,MAAM,CAAC3F,UAAU,IAAI,CAAC;YAClClN,MAAM,EAAE,CAAC;YAAE;YACXF,KAAK,EAAE+S,MAAM,CAAC5F,KAAK,IAAI,EAAE;YACzB5X,kBAAkB,EAAEA,kBAAkB,CAAC+Y,IAAI;YAAE;YAC7Cd,OAAO,EAAEuF,MAAM,CAACtF,OAAO,IAAI;WAC5B;UACDW,iBAAiB,CAACL,IAAI,CAACiF,OAAO,CAAC;QACjC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI5E,iBAAiB,CAACnN,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAACwB,OAAO,CAACuE,YAAY,CAAC,cAAc,CAAC;MACzC;IACF;IAEA;IACA,IAAI,CAAChG,cAAc,GAAG,CAAC,GAAG,IAAI,CAACA,cAAc,EAAE,GAAGoN,iBAAiB,CAAC;IAEpE;IACA,IAAI,CAACtO,cAAc,EAAE;IAErB,MAAMoT,aAAa,GAAGP,iBAAiB,CAAC1R,MAAM;IAC9C,MAAMkS,SAAS,GAAG/E,iBAAiB,CAACnN,MAAM;IAC1C,IAAI,CAACwB,OAAO,CAACyF,aAAa,CAAC,QAAQgL,aAAa,UAAUC,SAAS,MAAM,CAAC;IAE1EvF,OAAO,CAACM,GAAG,CAAC,SAAS,EAAE;MACrBgF,aAAa;MACbC,SAAS;MACTC,QAAQ,EAAEhF;KACX,CAAC;EACJ;EAEA/U,sBAAsBA,CAACga,MAAc;IACnC,QAAQA,MAAM;MACZ,KAAKje,mBAAmB,CAACke,GAAG;QAC1B,OAAO,KAAK;MACd,KAAKle,mBAAmB,CAACme,GAAG;QAC1B,OAAO,KAAK;MACd,KAAKne,mBAAmB,CAACoe,GAAG;QAC1B,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;;;uCAjrCWpR,4BAA4B,EAAA3M,EAAA,CAAAge,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAle,EAAA,CAAAge,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAApe,EAAA,CAAAge,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAte,EAAA,CAAAge,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAxe,EAAA,CAAAge,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAA1e,EAAA,CAAAge,iBAAA,CAAAW,EAAA,CAAAC,YAAA,GAAA5e,EAAA,CAAAge,iBAAA,CAAAW,EAAA,CAAAE,oBAAA,GAAA7e,EAAA,CAAAge,iBAAA,CAAAW,EAAA,CAAAG,gBAAA,GAAA9e,EAAA,CAAAge,iBAAA,CAAAe,EAAA,CAAAC,aAAA,GAAAhf,EAAA,CAAAge,iBAAA,CAAAiB,EAAA,CAAAC,MAAA,GAAAlf,EAAA,CAAAge,iBAAA,CAAAmB,EAAA,CAAAC,YAAA,GAAApf,EAAA,CAAAge,iBAAA,CAAAqB,GAAA,CAAAC,cAAA,GAAAtf,EAAA,CAAAge,iBAAA,CAAAuB,GAAA,CAAAC,gBAAA,GAAAxf,EAAA,CAAAge,iBAAA,CAAAW,EAAA,CAAAc,eAAA,GAAAzf,EAAA,CAAAge,iBAAA,CAAA0B,GAAA,CAAAC,4BAAA;IAAA;EAAA;;;YAA5BhT,4BAA4B;MAAAiT,SAAA;MAAAC,SAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UCnEvC/f,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAA2B,SAAA,qBAAiC;UACnC3B,EAAA,CAAAG,YAAA,EAAiB;UACfH,EADgB,CAAAC,cAAA,mBAAc,YACO;UAAAD,EAAA,CAAAE,MAAA,gRAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIjFH,EAHN,CAAAC,cAAA,aAA8B,aACN,cACqC,gBACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,qBACkD;UADtBD,EAAA,CAAAmE,gBAAA,2BAAA8b,0EAAA5b,MAAA;YAAArE,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAAlgB,EAAA,CAAAuE,kBAAA,CAAAyb,GAAA,CAAAzd,WAAA,CAAAC,kBAAA,EAAA6B,MAAA,MAAA2b,GAAA,CAAAzd,WAAA,CAAAC,kBAAA,GAAA6B,MAAA;YAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;UAAA,EAA4C;UACtErE,EAAA,CAAAiB,UAAA,4BAAAkf,2EAAA;YAAAngB,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAA,OAAAlgB,EAAA,CAAAyB,WAAA,CAAkBue,GAAA,CAAArM,0BAAA,EAA4B;UAAA,EAAC;UAC/C3T,EAAA,CAAAkC,UAAA,KAAAke,kDAAA,wBAAoE;UAK1EpgB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACX;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtDH,EAAA,CAAAC,cAAA,qBAAsE;UAA3DD,EAAA,CAAAmE,gBAAA,2BAAAkc,0EAAAhc,MAAA;YAAArE,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAAlgB,EAAA,CAAAuE,kBAAA,CAAAyb,GAAA,CAAAzd,WAAA,CAAA6D,kBAAA,EAAA/B,MAAA,MAAA2b,GAAA,CAAAzd,WAAA,CAAA6D,kBAAA,GAAA/B,MAAA;YAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;UAAA,EAA4C;UACrDrE,EAAA,CAAAkC,UAAA,KAAAoe,kDAAA,wBAAgE;UAKtEtgB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eAC8B,iBACJ;UAAAD,EAAA,CAAAE,MAAA,eAC5C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBACiE;UAAhCD,EAAA,CAAAmE,gBAAA,2BAAAoc,sEAAAlc,MAAA;YAAArE,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAAlgB,EAAA,CAAAuE,kBAAA,CAAAyb,GAAA,CAAAzd,WAAA,CAAAgO,KAAA,EAAAlM,MAAA,MAAA2b,GAAA,CAAAzd,WAAA,CAAAgO,KAAA,GAAAlM,MAAA;YAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;UAAA,EAA+B;UAC5FrE,EADE,CAAAG,YAAA,EAA2F,EAC7E;UAChBH,EAAA,CAAAC,cAAA,iBAA0C;UAAAD,EAAA,CAAAE,MAAA,UAC1C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBACuD;UAA9BD,EAAA,CAAAmE,gBAAA,2BAAAqc,sEAAAnc,MAAA;YAAArE,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAAlgB,EAAA,CAAAuE,kBAAA,CAAAyb,GAAA,CAAAzd,WAAA,CAAAiO,GAAA,EAAAnM,MAAA,MAAA2b,GAAA,CAAAzd,WAAA,CAAAiO,GAAA,GAAAnM,MAAA;YAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;UAAA,EAA6B;UAGtFrE,EAHM,CAAAG,YAAA,EAAiF,EACnE,EACZ,EACF;UAENH,EAAA,CAAA2B,SAAA,cAWM;UAIF3B,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACX;UAC1CD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAuF;UAA3DD,EAAA,CAAAmE,gBAAA,2BAAAsc,0EAAApc,MAAA;YAAArE,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAAlgB,EAAA,CAAAuE,kBAAA,CAAAyb,GAAA,CAAAzd,WAAA,CAAA0N,kBAAA,EAAA5L,MAAA,MAAA2b,GAAA,CAAAzd,WAAA,CAAA0N,kBAAA,GAAA5L,MAAA;YAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;UAAA,EAA4C;UACtErE,EAAA,CAAAkC,UAAA,KAAAwe,kDAAA,wBAAgE;UAKtE1gB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACX;UAC1CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAyF;UAA3DD,EAAA,CAAAmE,gBAAA,2BAAAwc,0EAAAtc,MAAA;YAAArE,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAAlgB,EAAA,CAAAuE,kBAAA,CAAAyb,GAAA,CAAAzd,WAAA,CAAAkC,kBAAA,EAAAJ,MAAA,MAAA2b,GAAA,CAAAzd,WAAA,CAAAkC,kBAAA,GAAAJ,MAAA;YAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;UAAA,EAA4C;UACxErE,EAAA,CAAAkC,UAAA,KAAA0e,kDAAA,wBAAgE;UAKtE5gB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACZ;UACzCD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAsF;UAA1DD,EAAA,CAAAmE,gBAAA,2BAAA0c,0EAAAxc,MAAA;YAAArE,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAAlgB,EAAA,CAAAuE,kBAAA,CAAAyb,GAAA,CAAAzd,WAAA,CAAA0C,iBAAA,EAAAZ,MAAA,MAAA2b,GAAA,CAAAzd,WAAA,CAAA0C,iBAAA,GAAAZ,MAAA;YAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;UAAA,EAA2C;UACrErE,EAAA,CAAAkC,UAAA,KAAA4e,kDAAA,wBAA+D;UAKrE9gB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACd;UACvCD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAqF;UAAzDD,EAAA,CAAAmE,gBAAA,2BAAA4c,0EAAA1c,MAAA;YAAArE,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAAlgB,EAAA,CAAAuE,kBAAA,CAAAyb,GAAA,CAAAzd,WAAA,CAAA+N,gBAAA,EAAAjM,MAAA,MAAA2b,GAAA,CAAAzd,WAAA,CAAA+N,gBAAA,GAAAjM,MAAA;YAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;UAAA,EAA0C;UACpErE,EAAA,CAAAkC,UAAA,KAAA8e,kDAAA,wBAAgE;UAKtEhhB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACV;UAC3CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAA0F;UAA5DD,EAAA,CAAAmE,gBAAA,2BAAA8c,0EAAA5c,MAAA;YAAArE,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAAlgB,EAAA,CAAAuE,kBAAA,CAAAyb,GAAA,CAAAzd,WAAA,CAAA6N,mBAAA,EAAA/L,MAAA,MAAA2b,GAAA,CAAAzd,WAAA,CAAA6N,mBAAA,GAAA/L,MAAA;YAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;UAAA,EAA6C;UACzErE,EAAA,CAAAkC,UAAA,KAAAgf,kDAAA,wBAAiE;UAKvElhB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACL;UAChDD,EAAA,CAAAE,MAAA,wCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAgG;UAAjED,EAAA,CAAAmE,gBAAA,2BAAAgd,0EAAA9c,MAAA;YAAArE,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAAlgB,EAAA,CAAAuE,kBAAA,CAAAyb,GAAA,CAAAzd,WAAA,CAAA8N,wBAAA,EAAAhM,MAAA,MAAA2b,GAAA,CAAAzd,WAAA,CAAA8N,wBAAA,GAAAhM,MAAA;YAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;UAAA,EAAkD;UAC/ErE,EAAA,CAAAkC,UAAA,KAAAkf,kDAAA,wBAAsE;UAK5EphB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAENH,EAAA,CAAA2B,SAAA,cAEM;UAKF3B,EAFJ,CAAAC,cAAA,eAAuB,eACoC,kBACO;UAArBD,EAAA,CAAAiB,UAAA,mBAAAogB,+DAAA;YAAArhB,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAA,OAAAlgB,EAAA,CAAAyB,WAAA,CAASue,GAAA,CAAAtP,QAAA,EAAU;UAAA,EAAC;UAC3D1Q,EAAA,CAAA2B,SAAA,aAAkC;UAAA3B,EAAA,CAAAE,MAAA,qBACpC;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGJH,EADF,CAAAC,cAAA,eAAuB,eAC+B;UAClDD,EAAA,CAAAkC,UAAA,KAAAof,+CAAA,qBAAmG;UAGnGthB,EAAA,CAAAC,cAAA,kBAAqF;UAA5CD,EAAA,CAAAiB,UAAA,mBAAAsgB,+DAAA;YAAAvhB,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAA,OAAAlgB,EAAA,CAAAyB,WAAA,CAASue,GAAA,CAAAzK,YAAA,CAAa,mBAAmB,CAAC;UAAA,EAAC;UAClFvV,EAAA,CAAA2B,SAAA,aAAoC;UAAA3B,EAAA,CAAAE,MAAA,6CACtC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAITH,EAAA,CAAAC,cAAA,kBAAiE;UAAxBD,EAAA,CAAAiB,UAAA,mBAAAugB,+DAAA;YAAAxhB,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAA,OAAAlgB,EAAA,CAAAyB,WAAA,CAASue,GAAA,CAAA/O,WAAA,EAAa;UAAA,EAAC;UAC9DjR,EAAA,CAAA2B,SAAA,aAAuC;UAAA3B,EAAA,CAAAE,MAAA,mDACzC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAA4G;UAAzDD,EAAA,CAAAiB,UAAA,oBAAAwgB,+DAAApd,MAAA;YAAArE,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAA,OAAAlgB,EAAA,CAAAyB,WAAA,CAAUue,GAAA,CAAAnO,cAAA,CAAAxN,MAAA,CAAsB;UAAA,EAAC;UAApFrE,EAAA,CAAAG,YAAA,EAA4G;UAC5GH,EAAA,CAAAC,cAAA,kBAAiE;UAA7BD,EAAA,CAAAiB,UAAA,mBAAAygB,+DAAA;YAAA1hB,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAA,OAAAlgB,EAAA,CAAAyB,WAAA,CAASue,GAAA,CAAAvO,gBAAA,EAAkB;UAAA,EAAC;UAC9DzR,EAAA,CAAA2B,SAAA,aAAuC;UAAA3B,EAAA,CAAAE,MAAA,+DACzC;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAOEH,EALR,CAAAC,cAAA,eAAmC,iBACe,iBAClB,UACtB,cAE4B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,uCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACC;UACRH,EAAA,CAAAC,cAAA,cAAO;UACLD,EAAA,CAAAkC,UAAA,MAAAyf,4CAAA,mBAAmD;UA8C3D3hB,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,2BAAsD,2BAES;UAD7CD,EAAA,CAAAmE,gBAAA,wBAAAyd,6EAAAvd,MAAA;YAAArE,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAAlgB,EAAA,CAAAuE,kBAAA,CAAAyb,GAAA,CAAAjS,SAAA,EAAA1J,MAAA,MAAA2b,GAAA,CAAAjS,SAAA,GAAA1J,MAAA;YAAA,OAAArE,EAAA,CAAAyB,WAAA,CAAA4C,MAAA;UAAA,EAAoB;UAClCrE,EAAA,CAAAiB,UAAA,wBAAA2gB,6EAAAvd,MAAA;YAAArE,EAAA,CAAAmB,aAAA,CAAA+e,GAAA;YAAA,OAAAlgB,EAAA,CAAAyB,WAAA,CAAcue,GAAA,CAAAjP,WAAA,CAAA1M,MAAA,CAAmB;UAAA,EAAC;UAGxCrE,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UA+KVH,EA7KA,CAAAkC,UAAA,MAAA2f,qDAAA,gCAAA7hB,EAAA,CAAA8hB,sBAAA,CAAmE,MAAAC,qDAAA,iCAAA/hB,EAAA,CAAA8hB,sBAAA,CA0IF,MAAAE,qDAAA,kCAAAhiB,EAAA,CAAA8hB,sBAAA,CAmCJ;;;UApZvB9hB,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAA2E,gBAAA,YAAAqb,GAAA,CAAAzd,WAAA,CAAAC,kBAAA,CAA4C;UAE1CxC,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAA4f,GAAA,CAAAlZ,oBAAA,CAAuB;UAS1C9G,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAA2E,gBAAA,YAAAqb,GAAA,CAAAzd,WAAA,CAAA6D,kBAAA,CAA4C;UACzBpG,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAA4f,GAAA,CAAAjZ,gBAAA,CAAmB;UAYY/G,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAA2E,gBAAA,YAAAqb,GAAA,CAAAzd,WAAA,CAAAgO,KAAA,CAA+B;UAKvCvQ,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAA2E,gBAAA,YAAAqb,GAAA,CAAAzd,WAAA,CAAAiO,GAAA,CAA6B;UAuBtDxQ,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAA2E,gBAAA,YAAAqb,GAAA,CAAAzd,WAAA,CAAA0N,kBAAA,CAA4C;UAC1CjQ,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAA4f,GAAA,CAAA1R,gBAAA,CAAmB;UAYnBtO,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAA2E,gBAAA,YAAAqb,GAAA,CAAAzd,WAAA,CAAAkC,kBAAA,CAA4C;UAC5CzE,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAA4f,GAAA,CAAAnb,gBAAA,CAAmB;UAYrB7E,EAAA,CAAAM,SAAA,GAA2C;UAA3CN,EAAA,CAAA2E,gBAAA,YAAAqb,GAAA,CAAAzd,WAAA,CAAA0C,iBAAA,CAA2C;UACzCjF,EAAA,CAAAM,SAAA,EAAkB;UAAlBN,EAAA,CAAAI,UAAA,YAAA4f,GAAA,CAAA7a,eAAA,CAAkB;UAWpBnF,EAAA,CAAAM,SAAA,GAA0C;UAA1CN,EAAA,CAAA2E,gBAAA,YAAAqb,GAAA,CAAAzd,WAAA,CAAA+N,gBAAA,CAA0C;UACxCtQ,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAA4f,GAAA,CAAA5R,gBAAA,CAAmB;UAYnBpO,EAAA,CAAAM,SAAA,GAA6C;UAA7CN,EAAA,CAAA2E,gBAAA,YAAAqb,GAAA,CAAAzd,WAAA,CAAA6N,mBAAA,CAA6C;UAC7CpQ,EAAA,CAAAM,SAAA,EAAoB;UAApBN,EAAA,CAAAI,UAAA,YAAA4f,GAAA,CAAAzR,iBAAA,CAAoB;UAYnBvO,EAAA,CAAAM,SAAA,GAAkD;UAAlDN,EAAA,CAAA2E,gBAAA,YAAAqb,GAAA,CAAAzd,WAAA,CAAA8N,wBAAA,CAAkD;UACnDrQ,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAA4f,GAAA,CAAAxR,sBAAA,CAAyB;UAsBbxO,EAAA,CAAAM,SAAA,GAAc;UAAdN,EAAA,CAAAI,UAAA,SAAA4f,GAAA,CAAAlY,QAAA,CAAc;UAsCnC9H,EAAA,CAAAM,SAAA,IAAe;UAAfN,EAAA,CAAAI,UAAA,YAAA4f,GAAA,CAAAvM,SAAA,CAAe;UAgD1BzT,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAA2E,gBAAA,SAAAqb,GAAA,CAAAjS,SAAA,CAAoB;UAAuB/N,EAAtB,CAAAI,UAAA,aAAA4f,GAAA,CAAAlS,QAAA,CAAqB,mBAAAkS,GAAA,CAAAhS,YAAA,CAAgC;;;qBDzKlFlP,YAAY,EAAAmjB,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,IAAA,EAAEtjB,YAAY,EAAAujB,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,kBAAA,EAAAJ,GAAA,CAAAK,YAAA,EAAAL,GAAA,CAAAM,YAAA,EAAAN,GAAA,CAAAO,OAAA,EAAAtE,EAAA,CAAAuE,eAAA,EAAAvE,EAAA,CAAAwE,mBAAA,EAAAxE,EAAA,CAAAyE,qBAAA,EAAAzE,EAAA,CAAA0E,qBAAA,EAAA1E,EAAA,CAAA2E,mBAAA,EAAA3E,EAAA,CAAA4E,gBAAA,EAAA5E,EAAA,CAAA6E,iBAAA,EAAA7E,EAAA,CAAA8E,iBAAA,EAAA9E,EAAA,CAAA+E,oBAAA,EAAA/E,EAAA,CAAAgF,iBAAA,EAAAhF,EAAA,CAAAiF,eAAA,EAAAjF,EAAA,CAAAkF,qBAAA,EAAAlF,EAAA,CAAAmF,qBAAA,EAAAC,GAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,kBAAA,EAAE9kB,kBAAkB,EAAEI,mBAAmB,EAAEW,oCAAoC;MAAAgkB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}