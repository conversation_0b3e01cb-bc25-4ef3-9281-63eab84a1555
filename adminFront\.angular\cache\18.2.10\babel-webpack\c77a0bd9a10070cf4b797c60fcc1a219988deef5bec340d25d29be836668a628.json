{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { NgbPagination } from '@ng-bootstrap/ng-bootstrap';\nimport { NgIf } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nfunction PaginationComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"ngb-pagination\", 3);\n    i0.ɵɵtwoWayListener(\"pageChange\", function PaginationComponent_div_0_Template_ngb_pagination_pageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.Page, $event) || (ctx_r1.Page = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"pageChange\", function PaginationComponent_div_0_Template_ngb_pagination_pageChange_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pageChange());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"collectionSize\", ctx_r1.CollectionSize);\n    i0.ɵɵtwoWayProperty(\"page\", ctx_r1.Page);\n    i0.ɵɵproperty(\"pageSize\", ctx_r1.PageSize)(\"maxSize\", 5)(\"boundaryLinks\", true);\n  }\n}\nfunction PaginationComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u7E3D\\u8A18\\u9304\\u6578\\uFF1A\", ctx_r1.CollectionSize, \"\\n\");\n  }\n}\nexport class PaginationComponent {\n  constructor() {\n    this.PageChange = new EventEmitter();\n    this.PageSize = 0;\n    this.CollectionSize = 0;\n    this.PageSizeChange = new EventEmitter();\n    this.CollectionSizeChange = new EventEmitter();\n  }\n  ngOnInit() {}\n  pageChange() {\n    this.PageChange.emit(this.Page);\n  }\n  static {\n    this.ɵfac = function PaginationComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PaginationComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PaginationComponent,\n      selectors: [[\"ngx-pagination\"]],\n      inputs: {\n        Page: \"Page\",\n        PageSize: \"PageSize\",\n        CollectionSize: \"CollectionSize\"\n      },\n      outputs: {\n        PageChange: \"PageChange\",\n        PageSizeChange: \"PageSizeChange\",\n        CollectionSizeChange: \"CollectionSizeChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"d-flex justify-content-center p-2 pagination-wrapper\", 4, \"ngIf\"], [\"class\", \"text-center pagination-info\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"p-2\", \"pagination-wrapper\"], [1, \"pagination-theme\", 3, \"pageChange\", \"collectionSize\", \"page\", \"pageSize\", \"maxSize\", \"boundaryLinks\"], [1, \"text-center\", \"pagination-info\"]],\n      template: function PaginationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, PaginationComponent_div_0_Template, 2, 5, \"div\", 0)(1, PaginationComponent_div_1_Template, 2, 1, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.CollectionSize > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.CollectionSize > 0);\n        }\n      },\n      dependencies: [NgIf, NgbPagination],\n      styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 554:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nUndefined variable.\\\\n   \\u2577\\\\n32 \\u2502 $gradient-success: linear-gradient(135deg, $success-light 0%, $success-base 100%);\\\\r\\\\n   \\u2502                                            ^^^^^^^^^^^^^^\\\\n   \\u2575\\\\n  src\\\\\\\\app\\\\\\\\@theme\\\\\\\\styles\\\\\\\\_colors.scss 32:44                           @import\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\components\\\\\\\\pagination\\\\\\\\pagination.component.scss 6:9  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[554]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "NgbPagination", "NgIf", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "PaginationComponent_div_0_Template_ngb_pagination_pageChange_1_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "Page", "ɵɵresetView", "ɵɵlistener", "pageChange", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "CollectionSize", "ɵɵtwoWayProperty", "PageSize", "ɵɵtext", "ɵɵtextInterpolate1", "PaginationComponent", "constructor", "PageChange", "PageSizeChange", "CollectionSizeChange", "ngOnInit", "emit", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PaginationComponent_Template", "rf", "ctx", "ɵɵtemplate", "PaginationComponent_div_0_Template", "PaginationComponent_div_1_Template", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\pagination\\pagination.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\pagination\\pagination.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { NgbPagination } from '@ng-bootstrap/ng-bootstrap';\r\nimport { NgIf } from '@angular/common';\r\n\r\n@Component({\r\n    selector: 'ngx-pagination',\r\n    templateUrl: './pagination.component.html',\r\n    styleUrls: ['./pagination.component.scss'],\r\n    standalone: true,\r\n    imports: [NgIf, NgbPagination]\r\n})\r\nexport class PaginationComponent implements OnInit {\r\n\r\n  @Output() PageChange = new EventEmitter();\r\n  @Input() Page: number | undefined;\r\n  @Input() PageSize: number = 0;\r\n  @Input() CollectionSize: number = 0;\r\n\r\n  @Output() PageSizeChange = new EventEmitter()\r\n  @Output() CollectionSizeChange = new EventEmitter()\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n  pageChange() {\r\n    this.PageChange.emit(this.Page);\r\n  }\r\n\r\n}\r\n", "<div class=\"d-flex justify-content-center p-2 pagination-wrapper\" *ngIf=\"CollectionSize!>0\">\r\n  <ngb-pagination class=\"pagination-theme\" [collectionSize]=\"CollectionSize\" [(page)]=\"Page!\" [pageSize]=\"PageSize\"\r\n    (pageChange)=\"pageChange()\" [maxSize]=\"5\" [boundaryLinks]=\"true\">\r\n  </ngb-pagination>\r\n</div>\r\n\r\n<div class=\"text-center pagination-info\" *ngIf=\"CollectionSize>0\">\r\n  總記錄數：{{CollectionSize}}\r\n</div>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,IAAI,QAAQ,iBAAiB;;;;;ICDpCC,EADF,CAAAC,cAAA,aAA4F,wBAEvB;IADQD,EAAA,CAAAE,gBAAA,wBAAAC,wEAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAAG,IAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,IAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAgB;IACzFJ,EAAA,CAAAY,UAAA,wBAAAT,wEAAA;MAAAH,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAcJ,MAAA,CAAAM,UAAA,EAAY;IAAA,EAAC;IAE/Bb,EADE,CAAAc,YAAA,EAAiB,EACb;;;;IAHqCd,EAAA,CAAAe,SAAA,EAAiC;IAAjCf,EAAA,CAAAgB,UAAA,mBAAAT,MAAA,CAAAU,cAAA,CAAiC;IAACjB,EAAA,CAAAkB,gBAAA,SAAAX,MAAA,CAAAG,IAAA,CAAgB;IAC/CV,EADgD,CAAAgB,UAAA,aAAAT,MAAA,CAAAY,QAAA,CAAqB,cACtE,uBAAuB;;;;;IAIpEnB,EAAA,CAAAC,cAAA,aAAkE;IAChED,EAAA,CAAAoB,MAAA,GACF;IAAApB,EAAA,CAAAc,YAAA,EAAM;;;;IADJd,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAqB,kBAAA,oCAAAd,MAAA,CAAAU,cAAA,OACF;;;ADGA,OAAM,MAAOK,mBAAmB;EAU9BC,YAAA;IARU,KAAAC,UAAU,GAAG,IAAI3B,YAAY,EAAE;IAEhC,KAAAsB,QAAQ,GAAW,CAAC;IACpB,KAAAF,cAAc,GAAW,CAAC;IAEzB,KAAAQ,cAAc,GAAG,IAAI5B,YAAY,EAAE;IACnC,KAAA6B,oBAAoB,GAAG,IAAI7B,YAAY,EAAE;EAEnC;EAEhB8B,QAAQA,CAAA,GACR;EAEAd,UAAUA,CAAA;IACR,IAAI,CAACW,UAAU,CAACI,IAAI,CAAC,IAAI,CAAClB,IAAI,CAAC;EACjC;;;uCAjBWY,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAO,SAAA;MAAAC,MAAA;QAAApB,IAAA;QAAAS,QAAA;QAAAF,cAAA;MAAA;MAAAc,OAAA;QAAAP,UAAA;QAAAC,cAAA;QAAAC,oBAAA;MAAA;MAAAM,UAAA;MAAAC,QAAA,GAAAjC,EAAA,CAAAkC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCLhCxC,EANA,CAAA0C,UAAA,IAAAC,kCAAA,iBAA4F,IAAAC,kCAAA,iBAM1B;;;UANC5C,EAAA,CAAAgB,UAAA,SAAAyB,GAAA,CAAAxB,cAAA,KAAuB;UAMhDjB,EAAA,CAAAe,SAAA,EAAsB;UAAtBf,EAAA,CAAAgB,UAAA,SAAAyB,GAAA,CAAAxB,cAAA,KAAsB;;;qBDGlDlB,IAAI,EAAED,aAAa;MAAA+C,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}