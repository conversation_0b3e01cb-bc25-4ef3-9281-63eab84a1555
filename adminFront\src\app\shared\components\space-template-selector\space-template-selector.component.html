<!-- 空間模板選擇器共用元件 - 使用 nb-dialog -->
<nb-card class="space-template-dialog">
  <nb-card-header class="space-template-header">
    <div class="space-template-title">模板選擇器</div>
    <button class="close-btn" nbButton ghost (click)="close()">
      <nb-icon icon="close-outline"></nb-icon>
    </button>
  </nb-card-header>

  <nb-card-body class="space-template-body">
    <!-- 步驟導航 -->
    <div class="step-nav">
      <div class="step-item" [ngClass]="{
        'active': currentStep === 1,
        'completed': currentStep > 1,
        'pending': currentStep < 1
      }">1. 選擇模板</div>
      <div class="step-item" [ngClass]="{
        'active': currentStep === 2,
        'completed': currentStep > 2,
        'pending': currentStep < 2
      }">2. 確認套用</div>
    </div>

    <!-- 步驟1: 選擇模板 -->
    <div *ngIf="currentStep === 1" class="step-content">
      <div class="template-selection">
        <div class="section-title">
          <nb-icon icon="layers-outline" class="mr-2"></nb-icon>選擇模板項目
        </div>
        <div class="template-list">
          <div *ngIf="templates.length > 0; else noTemplates">
            <div *ngFor="let template of templates" class="template-item">
              <nb-checkbox [(ngModel)]="template.selected" (ngModelChange)="onTemplateItemChange()">
                <div class="template-info">
                  <div class="item-name">{{ template.CTemplateName }}</div>
                  <div class="item-code">ID: {{ template.CTemplateId }}</div>
                  <div class="item-status">
                    狀態: {{ template.CStatus === 1 ? '啟用' : '停用' }}
                  </div>
                  <div class="item-type">
                    類型: {{ EnumTemplateTypeHelper.getDisplayName(template.CTemplateType!) }}
                  </div>
                </div>
              </nb-checkbox>
            </div>
          </div>
          <ng-template #noTemplates>
            <div class="no-templates">
              <nb-icon icon="info-outline" class="mr-2"></nb-icon>
              暫無可用的模板項目，請稍後再試或聯繫管理員
            </div>
          </ng-template>
        </div>
      </div>
    </div>

    <!-- 步驟2: 確認套用 -->
    <div *ngIf="currentStep === 2" class="step-content">
      <div class="confirmation-area">
        <div class="section-title">
          <nb-icon icon="checkmark-circle-outline" class="mr-2"></nb-icon>確認套用詳情
        </div>

        <div class="selected-summary">
          <div class="summary-text">
            將套用 <strong>{{ EnumTemplateTypeHelper.getDisplayName(CTemplateType) }}</strong>：{{ getSelectedItems().length
            }} 個模板項目
          </div>
        </div>

        <!-- 選中的模板詳情展開 -->
        <div class="selected-templates-details">
          <div *ngFor="let item of getSelectedItems()" class="template-detail-section">
            <div class="template-detail-header">
              <h5 class="template-name">{{ item.CTemplateName }}</h5>
              <div class="template-meta">
                <span class="template-id">ID: {{ item.CTemplateId }}</span>
                <span class="template-status">{{ item.CStatus === 1 ? '啟用' : '停用' }}</span>
              </div>
            </div>

            <div class="template-detail-content">
              <div *ngIf="getTemplateDetails(item.CTemplateId!).length > 0; else noDetails" class="detail-items">
                <div class="detail-items-header">
                  <span class="detail-count">包含 {{ getTemplateDetails(item.CTemplateId!).length }} 個明細項目：</span>
                </div>
                <div class="detail-items-list">
                  <div *ngFor="let detail of getTemplateDetails(item.CTemplateId!); let i = index" class="detail-item">
                    <div class="detail-index">{{ i + 1 }}</div>
                    <div class="detail-info">
                      <div class="detail-part">{{ detail.CPart }}</div>
                      <div class="detail-location" *ngIf="detail.CLocation">
                        位置: {{ detail.CLocation }}
                      </div>
                      <!-- 項目模板顯示單位和單價資訊 -->
                      <div
                        *ngIf="CTemplateType === EnumTemplateType.ItemTemplate && (detail.CUnitPrice || detail.CUnit)"
                        class="detail-pricing">
                        <div class="pricing-info">
                          <span class="price-item" *ngIf="detail.CUnitPrice">
                            <i class="fas fa-dollar-sign"></i>
                            單價: NT$ {{ detail.CUnitPrice | number }}
                          </span>
                          <span class="unit-item" *ngIf="detail.CUnit">
                            <i class="fas fa-ruler"></i>
                            單位: {{ detail.CUnit }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <ng-template #noDetails>
                <div class="no-details">
                  <nb-icon icon="info-outline" class="mr-2"></nb-icon>
                  此模板暫無明細項目
                </div>
              </ng-template>
            </div>
          </div>
        </div>

        <div *ngIf="hasConflicts()" class="conflict-warning">
          <div class="warning-text">
            <strong><nb-icon icon="alert-triangle-outline" class="mr-1"></nb-icon>衝突檢測：</strong>
            檢測到 {{ getConflictCount() }} 個項目可能與現有需求重複，系統將自動處理衝突項目。
          </div>
        </div>
      </div>
    </div>
  </nb-card-body>

  <nb-card-footer class="space-template-footer">
    <div class="progress-info">
      <span>{{ getProgressText() }}</span>
    </div>
    <div class="step-buttons">
      <button nbButton status="basic" (click)="close()">取消</button>
      <button *ngIf="currentStep > 1" nbButton status="basic" (click)="previousStep()">上一步</button>
      <button *ngIf="currentStep < 2" nbButton status="primary" [disabled]="!canProceed()"
        (click)="nextStep()">下一步</button>
      <button *ngIf="currentStep === 2" nbButton status="success" [disabled]="getSelectedItems().length === 0"
        (click)="applyTemplate()">確認套用</button>
    </div>
  </nb-card-footer>
</nb-card>
