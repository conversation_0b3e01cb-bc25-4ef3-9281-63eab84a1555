// 導入統一色彩系統
@import '../../../@theme/styles/_colors';

// Bootstrap badge styles - 使用金色主題
.badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;

  &.badge-primary {
    color: $text-light;
    background-color: $primary-gold-light;
  }

  &.badge-success {
    color: $text-light;
    background-color: $success-base;
  }

  &.badge-info {
    color: $text-light;
    background-color: $info-base;
  }

  &.badge-secondary {
    color: $text-light;
    background-color: $text-secondary;
  }
}

/* 需求項目匯入對話框 - 使用金色主題 */
.request-item-import-dialog {
  min-width: 600px;
  max-width: 800px;
  min-height: 500px;
  max-height: 80vh;
  border-radius: $border-radius-lg;

  .request-item-import-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid $border-light;
    background: $gradient-primary;
    border-radius: $border-radius-lg $border-radius-lg 0 0;

    .request-item-import-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: $text-light;
      text-shadow: $text-shadow-sm;
    }

    .close-btn {
      padding: 0.25rem;
      min-width: auto;
      border: none;
      background: transparent;
      color: rgba(255, 255, 255, 0.8);
      transition: $transition-normal;
      border-radius: $border-radius-sm;

      &:hover {
        color: $text-light;
        background-color: rgba(255, 255, 255, 0.1);
        transform: scale(1.05);
      }

      nb-icon {
        font-size: 1.25rem;
      }
    }
  }

  .request-item-import-body {
    padding: 1.5rem;
    overflow-y: auto;
    max-height: 60vh;
    background-color: $bg-primary;
  }
}

/* 步驟內容 */
.step-content {
  min-height: 300px;

  .section-title {
    display: flex;
    align-items: center;
    font-size: 1.1rem;
    font-weight: 600;
    color: $text-primary;
    margin-bottom: 1rem;

    nb-icon {
      margin-right: 0.5rem;
      color: $primary-gold-light;
    }
  }

  /* 搜尋區塊 */
  .search-section {
    margin-bottom: 2rem;

    .search-form {
      background: $bg-light-gold;
      padding: 1.5rem;
      border-radius: $border-radius-md;
      border: 1px solid $border-primary;

      .form-group {
        margin-bottom: 1rem;

        .label {
          font-weight: 500;
          color: $text-primary;
          display: block;
        }

        nb-select,
        input {
          width: 100%;
          border-color: $border-primary;

          &:focus {
            border-color: $primary-gold-light;
            box-shadow: 0 0 0 0.2rem alpha-gold(0.25);
          }
        }
      }

      .btn {
        min-width: 80px;
        transition: $transition-normal;

        &.btn-primary {
          background: $gradient-primary;
          border-color: $primary-gold-light;
          color: $text-light;

          &:hover {
            background: $gradient-primary-hover;
            transform: translateY(-1px);
            box-shadow: $shadow-md;
          }
        }

        &.btn-secondary {
          background-color: $bg-secondary;
          border-color: $border-medium;
          color: $text-secondary;

          &:hover {
            background-color: $bg-tertiary;
            border-color: $border-dark;
          }
        }

        nb-icon {
          margin-right: 0.5rem;
        }
      }
    }
  }

  /* 需求項目選擇區域 */
  .requirement-selection {
    .requirement-list {
      .requirement-item {
        margin-bottom: 1rem;
        padding: 1rem;
        border: 1px solid $border-light;
        border-radius: $border-radius-md;
        transition: $transition-normal;
        background-color: $bg-primary;
        box-shadow: $shadow-sm;

        &:hover {
          border-color: $border-primary;
          box-shadow: $shadow-md;
          background: $bg-hover;
          transform: translateY(-1px);
        }

        &:has(nb-checkbox input:checked) {
          border-color: $primary-gold-light;
          background: $bg-selected;
          box-shadow: 0 4px 12px alpha-gold(0.2);
        }

        nb-checkbox {
          width: 100%;

          .requirement-info {
            margin-left: 0.5rem;
            flex: 1;

            .item-name {
              font-weight: 600;
              color: $text-primary;
              margin-bottom: 0.25rem;
            }

            .item-code {
              font-size: 0.875rem;
              color: $text-secondary;
              margin-bottom: 0.25rem;
            }

            .item-status {
              font-size: 0.875rem;
              color: $text-secondary;
              margin-bottom: 0.25rem;
            }

            .item-type {
              font-size: 0.875rem;
              color: $primary-gold-dark;
              font-weight: 500;
            }

            .item-remark {
              font-size: 0.875rem;
              color: $info-base;
              font-style: italic;
              margin-top: 0.25rem;
            }
          }
        }
      }

      .no-requirements {
        text-align: center;
        padding: 2rem;
        color: $text-secondary;
        background: $bg-light-gold;
        border-radius: $border-radius-md;
        border: 1px solid $border-primary;

        nb-icon {
          margin-right: 0.5rem;
          color: $primary-gold-light;
        }
      }
    }
  }

  /* 確認套用區域 */
  .confirmation-area {
    .selected-summary {
      background: $gradient-primary;
      border: 1px solid $primary-gold-light;
      border-radius: $border-radius-md;
      padding: 1rem;
      margin-bottom: 1.5rem;
      box-shadow: $shadow-md;

      .summary-text {
        color: $text-light;
        font-weight: 500;
        text-shadow: $text-shadow-sm;

        strong {
          color: $text-light;
          font-weight: 700;
        }
      }
    }

    .selected-requirements-details {
      .requirement-detail-section {
        border: 1px solid $border-light;
        border-radius: $border-radius-md;
        margin-bottom: 1rem;
        overflow: hidden;
        background-color: $bg-primary;
        box-shadow: $shadow-sm;
        transition: $transition-normal;

        &:hover {
          box-shadow: $shadow-md;
          transform: translateY(-1px);
        }

        .requirement-detail-header {
          background: $gradient-secondary;
          padding: 1rem;
          border-bottom: 1px solid $border-light;

          .requirement-name {
            margin: 0 0 0.5rem 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: $text-primary;
          }

          .requirement-meta {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;

            .requirement-location,
            .requirement-unit {
              font-size: 0.875rem;
              color: $text-secondary;
            }

            .requirement-price {
              font-size: 0.875rem;
              color: $primary-gold-dark;
              font-weight: 600;
            }
          }

          .requirement-remark {
            font-size: 0.875rem;
            color: $info-base;
            font-style: italic;
            margin-top: 0.5rem;
          }
        }
      }
    }
  }
}

/* 步驟導航 - 使用金色主題 */
.step-nav {
  display: flex !important;
  justify-content: center !important;
  margin-bottom: 2rem !important;
  border-bottom: 1px solid $border-light !important;
  padding-bottom: 1rem !important;
  background: transparent !important;

  .step-item {
    padding: 0.75rem 1.5rem !important;
    margin: 0 0.5rem !important;
    border-radius: $border-radius-md !important;
    font-weight: 500 !important;
    font-size: 0.9rem !important;
    transition: $transition-normal !important;
    cursor: default !important;
    display: inline-block !important;
    text-align: center !important;
    min-width: 120px !important;

    &.active {
      background: $gradient-primary !important;
      color: $text-light !important;
      box-shadow: $shadow-lg !important;
      border: none !important;
      text-shadow: $text-shadow-sm !important;
    }

    &.completed {
      background: $gradient-success !important;
      color: $text-light !important;
      box-shadow: 0 2px 8px alpha-gold(0.3) !important;
      border: none !important;
    }

    &.pending {
      background-color: $bg-secondary !important;
      color: $text-secondary !important;
      border: 1px solid $border-medium !important;
    }
  }
}

/* 底部按鈕區域 */
.request-item-import-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 1rem 1.5rem;
  border-top: 1px solid $border-light;
  background: $bg-light-gold;
  border-radius: 0 0 $border-radius-lg $border-radius-lg;

  .step-buttons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
}

/* 響應式設計 */
@media (max-width: 768px) {
  .request-item-import-dialog {
    min-width: 95vw;
    max-width: 95vw;
    margin: 0.5rem;

    .request-item-import-body {
      padding: 1rem;

      .step-nav {
        .step-item {
          font-size: 0.875rem;
          padding: 0.4rem 0.8rem;
          margin: 0 0.25rem;
        }
      }

      .step-content {
        .search-section {
          .search-form {
            padding: 1rem;

            .form-group {
              margin-bottom: 0.75rem;
            }
          }
        }

        .requirement-selection {
          .requirement-list {
            .requirement-item {
              padding: 0.75rem;

              nb-checkbox {
                .requirement-info {
                  .item-name {
                    font-size: 0.9rem;
                  }

                  .item-code,
                  .item-status,
                  .item-type,
                  .item-remark {
                    font-size: 0.8rem;
                  }
                }
              }
            }
          }
        }
      }
    }

    .request-item-import-footer {
      flex-direction: column;
      gap: 0.75rem;
      align-items: stretch;

      .step-buttons {
        justify-content: center;
      }
    }
  }
}

/* 深色主題支援 - 使用金色主題 */
:host-context(.nb-theme-dark) {
  .request-item-import-dialog {
    .request-item-import-header {
      background: linear-gradient(135deg, darken($primary-gold-dark, 20%) 0%, darken($primary-gold-darker, 15%) 100%);
      border-bottom-color: $dark-border;

      .request-item-import-title {
        color: $dark-text-primary;
      }

      .close-btn {
        color: rgba(255, 255, 255, 0.7);

        &:hover {
          color: $dark-text-primary;
          background-color: rgba(255, 255, 255, 0.1);
        }
      }
    }

    .request-item-import-body {
      background-color: $dark-bg-primary;

      .step-nav {
        border-bottom-color: $dark-border !important;

        .step-item {
          &.active {
            background: linear-gradient(135deg, $primary-gold-light 0%, $primary-gold-dark 100%) !important;
            color: $text-light !important;
          }

          &.completed {
            background: linear-gradient(135deg, $success-base 0%, darken($success-base, 10%) 100%) !important;
            color: $text-light !important;
          }

          &.pending {
            background-color: $dark-bg-secondary !important;
            color: $dark-text-secondary !important;
            border-color: $dark-border !important;
          }
        }
      }

      .step-content {
        .section-title {
          color: $dark-text-primary;
        }

        .search-section {
          .search-form {
            background-color: $dark-bg-secondary;
            border-color: $dark-border;
          }
        }

        .requirement-selection {
          .requirement-list {
            .requirement-item {
              background-color: $dark-bg-secondary;
              border-color: $dark-border;

              &:hover {
                box-shadow: 0 2px 8px alpha-gold(0.2);
                background-color: lighten($dark-bg-secondary, 5%);
              }

              &:has(nb-checkbox input:checked) {
                border-color: $primary-gold-light;
                background-color: alpha-gold(0.1);
              }

              .requirement-info {
                .item-name {
                  color: $dark-text-primary;
                }

                .item-code,
                .item-status {
                  color: $dark-text-secondary;
                }

                .item-type {
                  color: $primary-gold-light;
                }
              }
            }

            .no-requirements {
              background-color: $dark-bg-secondary;
              border-color: $dark-border;
              color: $dark-text-secondary;
            }
          }
        }

        .confirmation-area {
          .selected-summary {
            background: linear-gradient(135deg, $primary-gold-dark 0%, $primary-gold-darker 100%);
            border-color: $primary-gold-light;
          }

          .selected-requirements-details {
            .requirement-detail-section {
              background-color: $dark-bg-secondary;
              border-color: $dark-border;

              .requirement-detail-header {
                background: linear-gradient(135deg, alpha-gold(0.2) 0%, alpha-gold(0.1) 100%);
                border-bottom-color: $dark-border;
              }
            }
          }
        }
      }
    }

    .request-item-import-footer {
      background: linear-gradient(135deg, alpha-gold(0.05) 0%, alpha-gold(0.02) 100%);
      border-top-color: $dark-border;
    }
  }
}

/* nb-dialog 內容區域調整 */
:host {
  display: block;
  width: 100%;
}

/* 確保 nb-checkbox 正常顯示 */
nb-checkbox {
  display: flex;
  align-items: flex-start;
  width: 100%;

  .customised-control-input {
    margin-right: 0.75rem;
    margin-top: 0.125rem;
  }
}

/* 工具類別 */
.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}