import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  NbCardModule,
  NbButtonModule,
  NbIconModule,
  NbCheckboxModule,
  NbDialogRef
} from '@nebular/theme';
import { TemplateService } from 'src/services/api/services/template.service';
import { TemplateGetListArgs, TemplateGetListResponse, GetTemplateDetailByIdArgs, TemplateDetailItem } from 'src/services/api/models';
import { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';

// 擴展 API 模型以支援前端選擇功能
export interface ExtendedTemplateItem extends TemplateGetListResponse {
  selected?: boolean;
}

export interface SpaceTemplateConfig {
  spaceId: string;
  spaceName: string;
  selectedItems: ExtendedTemplateItem[];
  templateDetails: Map<number, TemplateDetailItem[]>; // 新增：包含所有模板的明細
  totalPrice: number;
}

@Component({
  selector: 'app-space-template-selector',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NbCardModule,
    NbButtonModule,
    NbIconModule,
    NbCheckboxModule
  ],
  templateUrl: './space-template-selector.component.html',
  styleUrls: ['./space-template-selector.component.scss']
})
export class SpaceTemplateSelectorComponent implements OnInit {
  @Input() buildCaseId: string = '';
  @Input() CTemplateType: number = EnumTemplateType.SpaceTemplate;
  @Output() templateApplied = new EventEmitter<SpaceTemplateConfig>();

  // 暴露枚舉給模板使用
  EnumTemplateType = EnumTemplateType;
  EnumTemplateTypeHelper = EnumTemplateTypeHelper;

  currentStep: number = 1; // 現在從步驟1開始（選擇模板）
  templates: ExtendedTemplateItem[] = []; // 直接使用 API 資料
  selectedTemplateDetails: Map<number, TemplateDetailItem[]> = new Map(); // 存儲已載入的模板詳情

  constructor(
    private templateService: TemplateService,
    private dialogRef: NbDialogRef<SpaceTemplateSelectorComponent>
  ) { }

  ngOnInit() {
    // 組件初始化時載入模板
    this.loadTemplatesFromAPI();
  }

  loadTemplatesFromAPI() {
    // 準備 API 請求參數
    const getTemplateListArgs: TemplateGetListArgs = {
      CTemplateType: this.CTemplateType,
      PageIndex: 1,
      PageSize: 100, // 載入足夠的資料
      CTemplateName: null // 不篩選名稱
    };

    // 調用 GetTemplateListForCommon API
    this.templateService.apiTemplateGetTemplateListForCommonPost$Json({
      body: getTemplateListArgs
    }).subscribe({
      next: (response) => {
        if (response.StatusCode === 0 && response.Entries) {
          // 直接使用 API 資料，只添加 selected 屬性
          this.templates = response.Entries.map(item => ({
            ...item,
            selected: false
          }));
        } else {
          // API 返回錯誤
          this.templates = [];
        }
      },
      error: (error) => {
        // HTTP 請求錯誤
        this.templates = [];
      }
    });
  }

  onTemplateItemChange() {
    // 當模板項目選擇變更時的處理
  }

  getSelectedItems(): ExtendedTemplateItem[] {
    return this.templates.filter(item => item.selected);
  }

  getSelectedTotalPrice(): number {
    // 由於 API 沒有價格資訊，返回 0
    return 0;
  }

  canProceed(): boolean {
    switch (this.currentStep) {
      case 1:
        return this.getSelectedItems().length > 0;
      case 2:
        return true;
      default:
        return false;
    }
  }

  nextStep() {
    if (this.canProceed() && this.currentStep < 2) {
      if (this.currentStep === 1) {
        // 進入步驟2前，載入選中模板的詳情
        this.loadSelectedTemplateDetails();
      }
      this.currentStep++;
    }
  }

  loadSelectedTemplateDetails() {
    const selectedItems = this.getSelectedItems();

    selectedItems.forEach(item => {
      if (item.CTemplateId && !this.selectedTemplateDetails.has(item.CTemplateId)) {
        // 只載入尚未載入過的模板詳情
        this.loadTemplateDetailById(item.CTemplateId);
      }
    });
  }

  loadTemplateDetailById(templateId: number) {
    const args: GetTemplateDetailByIdArgs = {
      templateId: templateId
    };

    this.templateService.apiTemplateGetTemplateDetailByIdPost$Json({
      body: args
    }).subscribe({
      next: (response) => {
        if (response.StatusCode === 0 && response.Entries) {
          this.selectedTemplateDetails.set(templateId, response.Entries);
        }
      },
      error: () => {
        // 錯誤處理：設置空陣列
        this.selectedTemplateDetails.set(templateId, []);
      }
    });
  }

  getTemplateDetails(templateId: number): TemplateDetailItem[] {
    return this.selectedTemplateDetails.get(templateId) || [];
  }

  previousStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  getProgressText(): string {
    const progressTexts = {
      1: '請選擇要套用的模板',
      2: '確認套用詳情'
    };
    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';
  }

  hasConflicts(): boolean {
    // 模擬衝突檢測邏輯
    return this.getSelectedItems().length > 2;
  }

  getConflictCount(): number {
    // 模擬衝突數量
    return this.hasConflicts() ? 1 : 0;
  }

  applyTemplate() {
    const config: SpaceTemplateConfig = {
      spaceId: 'common', // 通用模板，不特定空間
      spaceName: '通用模板',
      selectedItems: this.getSelectedItems(),
      templateDetails: new Map(this.selectedTemplateDetails), // 傳遞已載入的模板明細
      totalPrice: this.getSelectedTotalPrice()
    };

    this.templateApplied.emit(config);
    this.close();
  }

  close() {
    this.resetSelections();
    this.dialogRef.close();
  }

  // 移除不需要的方法
  // onBackdropClick 由 NbDialog 自動處理

  private reset() {
    this.currentStep = 1;
    this.templates = [];
  }

  private resetSelections() {
    this.currentStep = 1;
    // 保留 templates 資料，只重置選擇狀態
    this.templates.forEach(template => {
      template.selected = false;
    });
    // 清空詳情快取
    this.selectedTemplateDetails.clear();
  }
}
