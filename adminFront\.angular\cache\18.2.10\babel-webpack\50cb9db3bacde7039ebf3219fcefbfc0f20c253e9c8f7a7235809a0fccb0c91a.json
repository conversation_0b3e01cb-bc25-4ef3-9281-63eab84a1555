{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { NgbPagination } from '@ng-bootstrap/ng-bootstrap';\nimport { NgIf } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nfunction PaginationComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"ngb-pagination\", 3);\n    i0.ɵɵtwoWayListener(\"pageChange\", function PaginationComponent_div_0_Template_ngb_pagination_pageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.Page, $event) || (ctx_r1.Page = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"pageChange\", function PaginationComponent_div_0_Template_ngb_pagination_pageChange_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pageChange());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"collectionSize\", ctx_r1.CollectionSize);\n    i0.ɵɵtwoWayProperty(\"page\", ctx_r1.Page);\n    i0.ɵɵproperty(\"pageSize\", ctx_r1.PageSize)(\"maxSize\", 5)(\"boundaryLinks\", true);\n  }\n}\nfunction PaginationComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u7E3D\\u8A18\\u9304\\u6578\\uFF1A\", ctx_r1.CollectionSize, \"\\n\");\n  }\n}\nexport class PaginationComponent {\n  constructor() {\n    this.PageChange = new EventEmitter();\n    this.PageSize = 0;\n    this.CollectionSize = 0;\n    this.PageSizeChange = new EventEmitter();\n    this.CollectionSizeChange = new EventEmitter();\n  }\n  ngOnInit() {}\n  pageChange() {\n    this.PageChange.emit(this.Page);\n  }\n  static {\n    this.ɵfac = function PaginationComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PaginationComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PaginationComponent,\n      selectors: [[\"ngx-pagination\"]],\n      inputs: {\n        Page: \"Page\",\n        PageSize: \"PageSize\",\n        CollectionSize: \"CollectionSize\"\n      },\n      outputs: {\n        PageChange: \"PageChange\",\n        PageSizeChange: \"PageSizeChange\",\n        CollectionSizeChange: \"CollectionSizeChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"d-flex justify-content-center p-2 pagination-wrapper\", 4, \"ngIf\"], [\"class\", \"text-center pagination-info\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"p-2\", \"pagination-wrapper\"], [1, \"pagination-theme\", 3, \"pageChange\", \"collectionSize\", \"page\", \"pageSize\", \"maxSize\", \"boundaryLinks\"], [1, \"text-center\", \"pagination-info\"]],\n      template: function PaginationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, PaginationComponent_div_0_Template, 2, 5, \"div\", 0)(1, PaginationComponent_div_1_Template, 2, 1, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.CollectionSize > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.CollectionSize > 0);\n        }\n      },\n      dependencies: [NgIf, NgbPagination],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n.pagination-wrapper[_ngcontent-%COMP%] {\\n  margin: 1rem 0;\\n  padding: 0.5rem;\\n}\\n\\n.pagination-info[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n  font-size: 0.875rem;\\n  color: #5A5A5A;\\n  font-weight: 500;\\n}\\n\\n.pagination-empty[_ngcontent-%COMP%] {\\n  color: #DC3545 !important;\\n  font-weight: 600;\\n  padding: 1rem;\\n  background-color: #F8D7DA;\\n  border: 1px solid #DC3545;\\n  border-radius: 0.5rem;\\n  margin: 1rem 0;\\n}\\n\\n  ngb-pagination .pagination {\\n  gap: 0.25rem;\\n  margin-bottom: 0;\\n}\\n  ngb-pagination .pagination .page-item .page-link {\\n  color: #AE9B66;\\n  background-color: #FFFFFF;\\n  border: 1px solid #E9ECEF;\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  transition: all 0.2s ease-in-out;\\n  box-shadow: none;\\n}\\n  ngb-pagination .pagination .page-item .page-link:hover {\\n  background-color: rgba(184, 166, 118, 0.05);\\n  color: #C4B382;\\n  border-color: rgba(184, 166, 118, 0.3);\\n  box-shadow: 0 1px 3px rgba(184, 166, 118, 0.1);\\n  transform: translateY(-1px);\\n}\\n  ngb-pagination .pagination .page-item .page-link:focus {\\n  background-color: rgba(184, 166, 118, 0.05);\\n  color: #C4B382;\\n  border-color: rgba(184, 166, 118, 0.5);\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n  outline: none;\\n}\\n  ngb-pagination .pagination .page-item.active .page-link {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  color: #FFFFFF;\\n  border-color: #AE9B66;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.15);\\n  font-weight: 600;\\n}\\n  ngb-pagination .pagination .page-item.active .page-link:hover,   ngb-pagination .pagination .page-item.active .page-link:focus {\\n  background: linear-gradient(135deg, #C4B382 0%, #A89660 100%);\\n  border-color: #C4B382;\\n  box-shadow: 0 4px 12px rgba(184, 166, 118, 0.2);\\n  transform: none;\\n}\\n  ngb-pagination .pagination .page-item.disabled .page-link {\\n  color: #CED4DA;\\n  background-color: #F8F9FA;\\n  border-color: #E9ECEF;\\n  cursor: not-allowed;\\n  opacity: 0.6;\\n}\\n  ngb-pagination .pagination .page-item.disabled .page-link:hover,   ngb-pagination .pagination .page-item.disabled .page-link:focus {\\n  background-color: #F8F9FA;\\n  color: #CED4DA;\\n  border-color: #E9ECEF;\\n  box-shadow: none;\\n  transform: none;\\n}\\n  ngb-pagination .pagination .page-item:first-child .page-link,   ngb-pagination .pagination .page-item:last-child .page-link {\\n  font-weight: 600;\\n}\\n\\n@media (max-width: 576px) {\\n    ngb-pagination .pagination {\\n    gap: 0.125rem;\\n  }\\n    ngb-pagination .pagination .page-item .page-link {\\n    padding: 0.375rem 0.5rem;\\n    font-size: 0.75rem;\\n  }\\n    .text-center {\\n    font-size: 0.75rem;\\n  }\\n}\\n[data-theme=dark][_ngcontent-%COMP%]     ngb-pagination .pagination .page-item .page-link {\\n  background-color: #1A1A1A;\\n  color: #FFFFFF;\\n  border-color: #404040;\\n}\\n[data-theme=dark][_ngcontent-%COMP%]     ngb-pagination .pagination .page-item .page-link:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n  color: #B8A676;\\n}\\n[data-theme=dark][_ngcontent-%COMP%]     ngb-pagination .pagination .page-item.active .page-link {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  color: #FFFFFF;\\n}\\n[data-theme=dark][_ngcontent-%COMP%]     ngb-pagination .pagination .page-item.disabled .page-link {\\n  background-color: #2D2D2D;\\n  color: #CCCCCC;\\n}\\n[data-theme=dark][_ngcontent-%COMP%]     .text-center {\\n  color: #CCCCCC;\\n}\\n[data-theme=dark][_ngcontent-%COMP%]     .text-center.text-danger {\\n  background-color: rgba(184, 166, 118, 0.1);\\n  border-color: #C82333;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "NgbPagination", "NgIf", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "PaginationComponent_div_0_Template_ngb_pagination_pageChange_1_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "Page", "ɵɵresetView", "ɵɵlistener", "pageChange", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "CollectionSize", "ɵɵtwoWayProperty", "PageSize", "ɵɵtext", "ɵɵtextInterpolate1", "PaginationComponent", "constructor", "PageChange", "PageSizeChange", "CollectionSizeChange", "ngOnInit", "emit", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PaginationComponent_Template", "rf", "ctx", "ɵɵtemplate", "PaginationComponent_div_0_Template", "PaginationComponent_div_1_Template", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\pagination\\pagination.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\pagination\\pagination.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { NgbPagination } from '@ng-bootstrap/ng-bootstrap';\r\nimport { NgIf } from '@angular/common';\r\n\r\n@Component({\r\n    selector: 'ngx-pagination',\r\n    templateUrl: './pagination.component.html',\r\n    styleUrls: ['./pagination.component.scss'],\r\n    standalone: true,\r\n    imports: [NgIf, NgbPagination]\r\n})\r\nexport class PaginationComponent implements OnInit {\r\n\r\n  @Output() PageChange = new EventEmitter();\r\n  @Input() Page: number | undefined;\r\n  @Input() PageSize: number = 0;\r\n  @Input() CollectionSize: number = 0;\r\n\r\n  @Output() PageSizeChange = new EventEmitter()\r\n  @Output() CollectionSizeChange = new EventEmitter()\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n  pageChange() {\r\n    this.PageChange.emit(this.Page);\r\n  }\r\n\r\n}\r\n", "<div class=\"d-flex justify-content-center p-2 pagination-wrapper\" *ngIf=\"CollectionSize!>0\">\r\n  <ngb-pagination class=\"pagination-theme\" [collectionSize]=\"CollectionSize\" [(page)]=\"Page!\" [pageSize]=\"PageSize\"\r\n    (pageChange)=\"pageChange()\" [maxSize]=\"5\" [boundaryLinks]=\"true\">\r\n  </ngb-pagination>\r\n</div>\r\n\r\n<div class=\"text-center pagination-info\" *ngIf=\"CollectionSize>0\">\r\n  總記錄數：{{CollectionSize}}\r\n</div>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,IAAI,QAAQ,iBAAiB;;;;;ICDpCC,EADF,CAAAC,cAAA,aAA4F,wBAEvB;IADQD,EAAA,CAAAE,gBAAA,wBAAAC,wEAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAAG,IAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,IAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAgB;IACzFJ,EAAA,CAAAY,UAAA,wBAAAT,wEAAA;MAAAH,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAcJ,MAAA,CAAAM,UAAA,EAAY;IAAA,EAAC;IAE/Bb,EADE,CAAAc,YAAA,EAAiB,EACb;;;;IAHqCd,EAAA,CAAAe,SAAA,EAAiC;IAAjCf,EAAA,CAAAgB,UAAA,mBAAAT,MAAA,CAAAU,cAAA,CAAiC;IAACjB,EAAA,CAAAkB,gBAAA,SAAAX,MAAA,CAAAG,IAAA,CAAgB;IAC/CV,EADgD,CAAAgB,UAAA,aAAAT,MAAA,CAAAY,QAAA,CAAqB,cACtE,uBAAuB;;;;;IAIpEnB,EAAA,CAAAC,cAAA,aAAkE;IAChED,EAAA,CAAAoB,MAAA,GACF;IAAApB,EAAA,CAAAc,YAAA,EAAM;;;;IADJd,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAqB,kBAAA,oCAAAd,MAAA,CAAAU,cAAA,OACF;;;ADGA,OAAM,MAAOK,mBAAmB;EAU9BC,YAAA;IARU,KAAAC,UAAU,GAAG,IAAI3B,YAAY,EAAE;IAEhC,KAAAsB,QAAQ,GAAW,CAAC;IACpB,KAAAF,cAAc,GAAW,CAAC;IAEzB,KAAAQ,cAAc,GAAG,IAAI5B,YAAY,EAAE;IACnC,KAAA6B,oBAAoB,GAAG,IAAI7B,YAAY,EAAE;EAEnC;EAEhB8B,QAAQA,CAAA,GACR;EAEAd,UAAUA,CAAA;IACR,IAAI,CAACW,UAAU,CAACI,IAAI,CAAC,IAAI,CAAClB,IAAI,CAAC;EACjC;;;uCAjBWY,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAO,SAAA;MAAAC,MAAA;QAAApB,IAAA;QAAAS,QAAA;QAAAF,cAAA;MAAA;MAAAc,OAAA;QAAAP,UAAA;QAAAC,cAAA;QAAAC,oBAAA;MAAA;MAAAM,UAAA;MAAAC,QAAA,GAAAjC,EAAA,CAAAkC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCLhCxC,EANA,CAAA0C,UAAA,IAAAC,kCAAA,iBAA4F,IAAAC,kCAAA,iBAM1B;;;UANC5C,EAAA,CAAAgB,UAAA,SAAAyB,GAAA,CAAAxB,cAAA,KAAuB;UAMhDjB,EAAA,CAAAe,SAAA,EAAsB;UAAtBf,EAAA,CAAAgB,UAAA,SAAAyB,GAAA,CAAAxB,cAAA,KAAsB;;;qBDGlDlB,IAAI,EAAED,aAAa;MAAA+C,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}